# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/2/8 13:00
# <AUTHOR> shaocanfan
# @File    : resend临时邮箱(发送).py
# @explain : 

# 先安装依赖
# pip install resend
# import resend
# # 这里换成自己的resend API Key
# resend.api_key = "re_7TSUYD9T_72DmWB9jJgBi5ipvrvQ4zHZy"
#
# params: resend.Emails.SendParams = {
#    # 发件人可以是自己域名下的任何一个人
#   "from": "<EMAIL>",
#   "to": ["<EMAIL>"],
#   "subject": "hi",
#   "html": "<strong>hello, world!</strong>"
# }
#
# email = resend.Emails.send(params)
# print(email)
import time

import requests
universities = [


]
for i in range(len(universities)):
  url = f"https://chatgpt.com/backend-api/classmates/{universities[i]}"
  print(url)
  payload = {}
  headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjE5MzQ0ZTY1LWJiYzktNDRkMS1hOWQwLWY5NTdiMDc5YmQwZSIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yDxx8rg_mVlWYw81vvDGLgXWxwzmDxyVWQ3VwUuwbqpxbm4RZvzVtIdUAfZ_pB-f_etNRTxKop-ZpsDx6-xWB99I-YIa0cClLRbEBacc9F46v6Ffx9DRQCr5BvaWA_BBCs3Aaaaf3SrrcAQ6ksqZjH213XcbuRgIXqEUbpVfhol-jj-IeNLJZZSqVJRZrEQ4WJVg60uGFBYip7V08L6dhTGqW-XFJxZzrC9GmXB9een-BcLHlSTMBm9PJxv3Zup6wdFbG9Lo00Gk6VwvM9FhjKHt3Wh_DUmQjQHvwXuaAq1wyoZlKh2sA_uBjaVw-QjWdMPRinHNcknCggzTDNyzH5mzqOQ_Q-oyItIB9GgPH9fVKsf2EKg-bdCJLaOZ2b1HcTA6vYt_rd-7lB_jhJdbz6TBO1-PJkoIOpNoXaIf6UjN32d8qRHmoO85dQvsejHJHHRcuRLld5xmypsLlPPxNhQP2mbc5bIckZlgONTcOjguhCN1BSeyoeydHyexrUNQ3YeSJUl60qq_WfnVJ9qyBu2FoUu2RV_R13_Wj7R_hIh1XZPQxBPGk_u08CZueI9DqzFfS1zmkwa714xEyWAadkVo_P6d9K8u-FUc6UVZknxFV989XfEhEF_h5-9gr2pBN3SMqcS4ew3GFlGF1S9DCwZREIAEQoTEYh06goMf7Es',
    'user-agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  }

  response = requests.request("GET", url, headers=headers, data=payload)

  print(response.text)
  time.sleep(1)
