# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/9/10 16:33
# <AUTHOR> shaocanfan
# @File    : HW_test.py
# @explain : 

# import json
# import time
# import paho.mqtt.client as mqtt
# from threading import Timer
#
# # MQTT 配置信息
# MQTT_SERVER = "b698a7d81d.st1.iotda-device.cn-south-1.myhuaweicloud.com"
# MQTT_PORT = 1883
# CLIENT_ID = "66dde66b1837002b28b3bbef_esp8266_test02_0_0_2024090903"
# USERNAME = "66dde66b1837002b28b3bbef_esp8266_test02"
# PASSWORD = "133a86a9bc32a546aea01a61c595b54bfad9e2226161cbad9c3cd9d1d8b0760d"
#
# # 订阅命令的Topic (从平台获取命令)
# COMMAND_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/commands/#"
# # 响应命令结果的Topic (发送处理结果到平台)
# RESPONSE_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/commands/response/request_id="
#
# # 模拟设备处理完命令后，回复的结果
# shadow_data_send = {
#     "result_code": 0,  # 0表示成功，非0表示失败
#     "paras": {
#         "result": "success"  # 自定义的回复参数，可根据设备的业务逻辑定义
#     }
# }
#
#
# # 当连接到MQTT服务器时回调
# def on_connect(client, userdata, flags, rc):
#     print(f"Connected with result code {rc}")
#     # 订阅命令下发Topic
#     # client.subscribe(COMMAND_TOPIC)
#     # print(f"Subscribed to topic: {COMMAND_TOPIC}")
#
#
# # 回调函数 - 当收到服务器下发的命令时被调用
# def on_message(client, userdata, msg):
#     print(f"Received message from topic '{msg.topic}': {msg.payload.decode()}")
#
#     # 解析接收到的命令
#     try:
#         payload = json.loads(msg.payload.decode())
#         print(f"Command payload: {payload}")
#
#         # 提取请求ID (假设命令包含了request_id)
#         # 在实际场景中，request_id 可能是通过msg.topic提取出来的。
#         request_id = msg.topic.split("request_id=")[-1]  # 从Topic中提取request_id
#         print(f"Extracted request_id: {request_id}")
#
#         # 模拟处理命令逻辑 (例如调整设备状态)
#         # 您可以在这里根据接收到的命令内容，执行不同的操作
#
#         # 回复命令结果，发布到响应Topic
#         response_topic = RESPONSE_TOPIC + request_id  # 确保响应的request_id与请求的request_id相同
#         client.publish(response_topic)
#         print(f"Published response to topic: {response_topic}")
#
#     except json.JSONDecodeError:
#         print("Failed to decode JSON from command message")
#
#
# # 创建MQTT客户端并设置回调函数
# client = mqtt.Client(CLIENT_ID, protocol=mqtt.MQTTv311)
# client.username_pw_set(USERNAME, PASSWORD)
# client.on_connect = on_connect
# client.on_message = on_message
#
# # 连接到MQTT服务器
# client.connect(MQTT_SERVER, MQTT_PORT, 60)
#
# # 启动网络循环
# client.loop_start()
#
# try:
#     while True:
#         time.sleep(1)
# except KeyboardInterrupt:
#     print("Disconnecting from MQTT server...")
#     client.loop_stop()
#     client.disconnect()




import json
import time
import paho.mqtt.client as mqtt
from tkinter import Tk, Label, Button, StringVar
from threading import Thread

# MQTT 配置信息
MQTT_SERVER = "b698a7d81d.st1.iotda-device.cn-south-1.myhuaweicloud.com"
MQTT_PORT = 1883
CLIENT_ID = "66dde66b1837002b28b3bbef_esp8266_test02_0_0_2024090903"
USERNAME = "66dde66b1837002b28b3bbef_esp8266_test02"
PASSWORD = "133a86a9bc32a546aea01a61c595b54bfad9e2226161cbad9c3cd9d1d8b0760d"

# 订阅命令的Topic (从平台获取命令)
COMMAND_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/commands/#"
# 响应命令结果的Topic (发送处理结果到平台)
RESPONSE_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/commands/response/request_id="

# 模拟设备处理完命令后，回复的结果
shadow_data_send = {
    "result_code": 0,  # 0表示成功，非0表示失败
    "paras": {
        "result": "success"  # 自定义的回复参数，可根据设备的业务逻辑定义
    }
}

# 创建 Tkinter GUI 主窗口
root = Tk()
root.title("MQTT Target Num Display")

# 创建一个 StringVar 变量用于显示 target_num 和连接状态
target_num_var = StringVar()
target_num_var.set("Waiting for target_num...")

connection_status_var = StringVar()
connection_status_var.set("Disconnected")

# 创建并放置标签用于显示 target_num
target_num_label = Label(root, textvariable=target_num_var, font=("Arial", 16))
target_num_label.pack(pady=10)

# 创建并放置标签用于显示连接状态
connection_status_label = Label(root, textvariable=connection_status_var, font=("Arial", 12))
connection_status_label.pack(pady=10)

# MQTT 连接状态
mqtt_connected = False
client = None

# 当连接到MQTT服务器时回调
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"Connected with result code {rc}")
        connection_status_var.set("Connected successfully")
        client.subscribe(COMMAND_TOPIC)
        print(f"Subscribed to topic: {COMMAND_TOPIC}")
    else:
        connection_status_var.set("Failed to connect")

# 回调函数 - 当收到服务器下发的命令时被调用
def on_message(client, userdata, msg):
    print(f"Received message from topic '{msg.topic}': {msg.payload.decode()}")

    # 解析接收到的命令
    try:
        payload = json.loads(msg.payload.decode())
        print(f"Command payload: {payload}")

        # 获取 target_num
        target_num = payload.get("paras", {}).get("target_num", "N/A")
        print(f"Received target_num: {target_num}")

        # 更新界面上的 target_num
        target_num_var.set(f"target_num: {target_num}")

        # 提取 request_id 并构造响应
        request_id = msg.topic.split("request_id=")[-1]
        response_topic = RESPONSE_TOPIC + request_id
        client.publish(response_topic, json.dumps(shadow_data_send))
        print(f"Published response to topic: {response_topic}")

    except json.JSONDecodeError:
        print("Failed to decode JSON from command message")

# 连接到MQTT服务器
def connect_mqtt():
    global client, mqtt_connected
    if not mqtt_connected:
        connection_status_var.set("Connecting...")
        client = mqtt.Client(CLIENT_ID, protocol=mqtt.MQTTv311)
        client.username_pw_set(USERNAME, PASSWORD)
        client.on_connect = on_connect
        client.on_message = on_message
        try:
            client.connect(MQTT_SERVER, MQTT_PORT, 60)
            client.loop_start()
            mqtt_connected = True
            print("Connecting to MQTT Broker...")
        except Exception as e:
            connection_status_var.set(f"Connection failed: {e}")
    else:
        print("Already connected")

# 断开MQTT连接
def disconnect_mqtt():
    global client, mqtt_connected
    if mqtt_connected and client:
        client.loop_stop()
        client.disconnect()
        mqtt_connected = False
        connection_status_var.set("Disconnected")
        print("Disconnected from MQTT Broker")
    else:
        print("Already disconnected")

# 创建“连接”按钮
connect_button = Button(root, text="连接", command=connect_mqtt, font=("Arial", 12))
connect_button.pack(pady=10)

# 创建“断开”按钮
disconnect_button = Button(root, text="断开", command=disconnect_mqtt, font=("Arial", 12))
disconnect_button.pack(pady=10)

# 运行 Tkinter GUI 主循环
root.mainloop()



