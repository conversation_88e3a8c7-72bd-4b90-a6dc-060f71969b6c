#
# import test1
#
#
#
#
#
#
# def test2():
#     print("this is test2")
#
# if __name__ == '__main__':
#     test1.search_baidu()

import requests

url = "https://api.stripe.com/v1/payment_pages/cs_live_b1pmJIdeNKpEw2cc0pvFniAiX1PVav2PPp2lgqJR7GYaJU9YDSwSpJkxwZ#fidpamZkaWAnPydgaycpJ3ZwZ3Zmd2x1cWxqa1BrbHRwYGtgdnZAa2RnaWBhJz9jZGl2YCknZHVsTmB8Jz8ndW5aaWxzYFowNEs8XHRHQl9yb3RVY31uX3FQYmhHU3BUTTc0NTVpf1Q3fElcX3UyaGl3PTVgUHxKPWs2Y0dyXUhiYEhMbURXf2AyR0t3RE1vbUpdV0E0cDBEZHJyRko9PTU1dkNMNENHU0QnKSdjd2poVmB3c2B3Jz9xd3BgKSdpZHxqcHFRfHVgJz8naHBpcWxabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl"

payload = 'eid=13609780-f9c8-41bf-a11d-dbd55fe67010&promotion_code=V89KPHA8&key=pk_live_51N9YqBGZwjqPfxkZtUgmBVuQH2100lzQ2yLYZp7mlr80eUyO8n3fBwXMgeMIhARze7BNrAHjhOXRD1u5AawwCO8800sFI1FBVA'
headers = {
  'accept': 'application/json',
  'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'content-type': 'application/x-www-form-urlencoded',
  'dnt': '1',
  'origin': 'https://stripe.perplexity.ai',
  'priority': 'u=1, i',
  'referer': 'https://stripe.perplexity.ai/',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'cross-site',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)