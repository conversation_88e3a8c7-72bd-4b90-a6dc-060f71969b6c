import requests

def search_baidu():

    url = 'https://www.baidu.com'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Connection': 'keep-alive'
    }

    response = requests.get(url=url, headers=headers)

    print(response.text)

def test1():
    print("this is test1")



if __name__ == '__main__':
    print('my_test1')




# class Car:
#     def __init__(self, make, model, year):
#         self.make = make
#         self.model = model
#         self.year = year
#         self.odometer_reading = 0  # 初始里程数
#
#     def description(self):
#         """返回汽车的描述性信息"""
#         return f"{self.year} {self.make} {self.model}"
#
#     def read_odometer(self):
#         """打印一条指出汽车里程的消息"""
#         print(f"This car has {self.odometer_reading} miles on it.")
#
#     def update_odometer(self, mileage):
#         """
#         将里程表读数设置为指定的值
#         禁止里程表读数往回调
#         """
#         if mileage >= self.odometer_reading:
#             self.odometer_reading = mileage
#         else:
#             print("You can't roll back an odometer!")
#
#     def increment_odometer(self, miles):
#         """将里程表读数增加指定的量"""
#         self.odometer_reading += miles
#
# my_car = Car('audi', 'a4', 2022)
# print(my_car.description())  # 输出车辆基本信息
#
#
# my_car.update_odometer(23500)  # 更新里程表
#
# print(my_car.read_odometer())
#
# my_car.increment_odometer(100)
#
# print(my_car.read_odometer())
# # my_car.read_odometer()  # 读取并打印里程表信息