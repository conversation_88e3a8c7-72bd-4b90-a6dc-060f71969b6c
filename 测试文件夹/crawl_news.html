<html lang="zh-cn"><head>
    <meta charset="utf-8">
    <title>美国地址生成器 - 美国身份生成器 - 美国信用卡生成器</title>
    <meta name="keywords" content="美国地址,随机地址,生成地址,信用卡生成器,美国身份生成器,获取美国地址,美国人身份信息获取，获取美国身份">
    <meta name="description" content="随机生成美国,中国,英国,日本等国身份，包括姓名，地址，电话，职称，信用卡，身高，体重等信息，获取美国人信息，获取美国人地址生成器。">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/layui/2.8.4/css/layui.min.css" integrity="sha512-gYnT2bdWXg9kSl/wFtOGREN3hHPklo0m7L3InAcpexBE+L7WWqU7DcqAOPQ33G2Fz8KxRq9kIZy4xsYFDsO40g==" crossorigin="anonymous" referrerpolicy="no-referrer">
<meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script async="" src="https://fundingchoicesmessages.google.com/i/ca-pub-5832698961587394?href=https%3A%2F%2Fwww.addressgenerator.net&amp;ers=2"></script><script async="" src="https://fundingchoicesmessages.google.com/f/AGSKWxU67CCBQq-J3-dIqBc0Vkx_cl376ndEa-7bIWOujI6ld3kYDWW2Tm5MoQZ6jHqpj_N6y8_YKpjh20b8jYA066Rc28Rrb8nEJho7nhTr7FHRdXw1IrSrOZAaq7htB2Azt2Cs512sGA==?fccs=***************************************************************************************************************************************************************************************************************************************"></script><script async="" src="https://fundingchoicesmessages.google.com/f/AGSKWxX9j-MCRHLAJdiK6DpDDFh6nNqxkP0PX8jnJ8Jchc3NzHrx4qQWQaABSyEZgU6CRxRpjg51fkezq7sA3iflME7Xfi5nCmEHHzOp9s4RxiXTDUpMsvfYTHnXdcMI0_zxvLyNQvetxg==?fccs=W251bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLFsxNzM1Mzg0ODA5LDYzNTAwMDAwMF0sbnVsbCxudWxsLG51bGwsW251bGwsWzcsOV0sbnVsbCwyLG51bGwsInpoLUNOIl0sImh0dHBzOi8vd3d3LmFkZHJlc3NnZW5lcmF0b3IubmV0LyIsbnVsbCxbWzgsIklNejU3eWM1aFZ3Il0sWzksInpoLUNOIl0sWzE5LCIyIl0sWzE3LCJbMF0iXV1d"></script><script async="" src="https://fundingchoicesmessages.google.com/f/AGSKWxUJOl6bbzPxERI9rAHw_qt3JzuleQxo4ry20sVwnLVCOta3fk46XPi1AQhHsDpkfcj3Ndhi77n9a7POYk6IVCA0PJFbqwXW1prXEqhiWltl0Z6yP5WhGKK_CWOmtqfmX3rHiCQpEw==?fccs=W251bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLFsxNzM1Mzg0ODEwLDUxMDAwMDAwMF0sbnVsbCxudWxsLG51bGwsW251bGwsWzcsOSw2XSxudWxsLDIsbnVsbCwiemgtQ04iLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCwxXSwiaHR0cHM6Ly93d3cuYWRkcmVzc2dlbmVyYXRvci5uZXQvIixudWxsLFtbOCwiSU16NTd5YzVoVnciXSxbOSwiemgtQ04iXSxbMTksIjIiXSxbMTcsIlswXSJdXV0"></script></head>
<body aria-hidden="false" style="padding: 0px 0px 154px;">
<script src="https://pagead2.googlesyndication.com/pagead/managed/js/adsense/m202412090101/reactive_library_fy2021.js"></script><script src="https://pagead2.googlesyndication.com/pagead/managed/js/adsense/m202412090101/show_ads_impl_fy2021.js"></script><script async="" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5832698961587394" crossorigin="anonymous" data-checked-head="true"></script>
<div class="layui-layout">
    <div class="">
        
        <div style="padding: 15px; height: auto !important;" class="layui-col-md-offset2 layui-col-md8">
            <div style="text-align: right;">
                <a class="layui-btn layui-btn-normal" href="/en/us.html">English</a>
                <a class="layui-btn layui-btn-normal" href="/zh-cn/us.html">中文</a>
                <a class="layui-btn layui-btn-normal" href="/de/us.html">Deutsch</a>
            </div>
            <blockquote class="layui-elem-quote layui-text">
                <h2>美国地址生成器 - 美国身份生成器 - 美国信用卡生成器</h2>
            </blockquote>

            <ul class="layui-nav layui-bg-cyan">
                
                <li class="layui-nav-item"><a href="/zh-cn/us.html">美国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/cn.html">中国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/au.html">澳大利亚地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/jp.html">日本地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/kr.html">韩国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/uk.html">英国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/de.html">德国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/fr.html">法国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/sg.html">新加坡地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/it.html">意大利地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/es.html">西班牙地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/nl.html">荷兰地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/my.html">马来西亚地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/ru.html">俄罗斯地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/th.html">泰国地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/ph.html">菲律宾地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/ar.html">阿根廷地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/tr.html">土耳其地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/vn.html">越南地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/tw.html">台湾地址</a></li>
                
                <li class="layui-nav-item"><a href="/zh-cn/hk.html">香港地址</a></li>
                
                
            <span class="layui-nav-bar"></span></ul>
            <div class="layui-bg-gray" style="padding-top: 30px; height: auto !important;">
                <div class="google-auto-placed" style="width: 100%; height: auto; clear: both; text-align: center;"><ins data-ad-format="auto" class="adsbygoogle adsbygoogle-noablate" data-ad-client="ca-pub-5832698961587394" data-adsbygoogle-status="done" style="display: block; margin: 10px auto; background-color: transparent; height: 280px;" data-ad-status="filled"><div id="aswift_1_host" style="border: none; height: 280px; width: 967px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;"><iframe id="aswift_1" name="aswift_1" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:967px;height:280px;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="967" height="280" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-5832698961587394&amp;output=html&amp;h=280&amp;adk=2779711680&amp;adf=2289084161&amp;pi=t.aa~a.3403044894~rp.1&amp;w=967&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1735384808&amp;rafmt=1&amp;to=qs&amp;pwprc=7351060718&amp;format=967x280&amp;url=https%3A%2F%2Fwww.addressgenerator.net%2F&amp;fwr=0&amp;pra=3&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;fa=40&amp;uach=**********************************************************************************************************************************************************************************************************************..&amp;dt=1735384808255&amp;bpp=3&amp;bdt=404&amp;idt=300&amp;shv=r20241212&amp;mjsv=m202412090101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie_enabled=1&amp;eoidce=1&amp;prev_fmts=0x0&amp;nras=2&amp;correlator=2465479838469&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=2&amp;u_h=864&amp;u_w=1536&amp;u_ah=824&amp;u_aw=1536&amp;u_cd=24&amp;u_sd=1.25&amp;dmc=8&amp;adx=184&amp;ady=416&amp;biw=1013&amp;bih=654&amp;scr_x=0&amp;scr_y=0&amp;eid=31089326%2C31089327%2C31089338%2C95332924%2C95344791%2C95345967&amp;oid=2&amp;pvsid=760930823005664&amp;tmod=970634376&amp;wsm=1&amp;uas=0&amp;nvt=1&amp;fc=1920&amp;brdim=9%2C9%2C9%2C9%2C1536%2C0%2C1051%2C805%2C1028%2C670&amp;vis=1&amp;rsz=%7C%7Cs%7C&amp;abl=NS&amp;fu=1152&amp;bc=31&amp;bz=1.02&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=2&amp;uci=a!2&amp;fsb=1&amp;dtd=306" data-google-container-id="a!2" tabindex="0" title="Advertisement" aria-label="Advertisement" data-google-query-id="CN_3nZisyooDFYhEwgUdi4QLQw" data-load-complete="true"></iframe></div></ins></div><div class="google-auto-placed" style="width: 100%; height: auto; clear: both; text-align: center;"><ins data-ad-format="auto" class="adsbygoogle adsbygoogle-noablate" data-ad-client="ca-pub-5832698961587394" data-adsbygoogle-status="done" style="display: block; margin: 10px auto; background-color: transparent; height: 280px;" data-ad-status="unfilled"><div id="aswift_2_host" style="border: none; height: 280px; width: 645px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;"><iframe id="aswift_2" name="aswift_2" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:645px;height:280px;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="645" height="280" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/ads?gdpr=0&amp;client=ca-pub-5832698961587394&amp;output=html&amp;h=280&amp;adk=3834119489&amp;adf=4106258812&amp;pi=t.aa~a.3403044894~rp.1&amp;w=645&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1735384809&amp;rafmt=1&amp;to=qs&amp;pwprc=7351060718&amp;format=645x280&amp;url=https%3A%2F%2Fwww.addressgenerator.net%2F&amp;fwr=0&amp;pra=3&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;fa=40&amp;uach=**********************************************************************************************************************************************************************************************************************..&amp;dt=1735384809315&amp;bpp=2&amp;bdt=1464&amp;idt=-M&amp;shv=r20241212&amp;mjsv=m202412090101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie_enabled=1&amp;eoidce=1&amp;prev_fmts=0x0%2C967x280&amp;nras=3&amp;correlator=2465479838469&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=2&amp;u_h=864&amp;u_w=1536&amp;u_ah=824&amp;u_aw=1536&amp;u_cd=24&amp;u_sd=1.25&amp;dmc=8&amp;adx=184&amp;ady=706&amp;biw=1013&amp;bih=654&amp;scr_x=0&amp;scr_y=0&amp;eid=31089326%2C31089327%2C31089338%2C95332924%2C95344791%2C95345967&amp;oid=2&amp;pvsid=760930823005664&amp;tmod=970634376&amp;wsm=1&amp;uas=0&amp;nvt=1&amp;fc=1920&amp;brdim=9%2C9%2C9%2C9%2C1536%2C0%2C1051%2C805%2C1028%2C670&amp;vis=1&amp;rsz=%7C%7Cs%7C&amp;abl=NS&amp;fu=1152&amp;bc=31&amp;bz=1.02&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=3&amp;uci=a!3&amp;btvi=1&amp;fsb=1&amp;dtd=200" data-google-container-id="a!3" tabindex="0" title="Advertisement" aria-label="Advertisement" data-load-complete="true" data-google-query-id="CKuH05isyooDFd5FwgUdcNsCCg"></iframe></div></ins></div><div class="layui-row">
                    <div class="layui-col-md3">

                        <div class="">
                            <form class="layui-form" action="" style="width: 200px; margin: 21px auto 0;">
                                
                                <div class="layui-form-item">
                                    <div class="layui-input-wrap">
                                        <div class="layui-input-prefix">
                                            <i class="layui-icon layui-icon-location"></i>
                                        </div>
                                        <select name="url" lay-search="">
                                            
                                            <option value="/usa-address">请选择州</option>
                                            
                                            <option value="/usa-address/alabama">阿拉巴马州地址</option>
                                            
                                            <option value="/usa-address/alaska">阿拉斯加州地址</option>
                                            
                                            <option value="/usa-address/arizona">亚利桑那州地址</option>
                                            
                                            <option value="/usa-address/arkansas">阿肯色州地址</option>
                                            
                                            <option value="/usa-address/california">加利福尼亚州地址</option>
                                            
                                            <option value="/usa-address/colorado">科罗拉多州地址</option>
                                            
                                            <option value="/usa-address/connecticut">康涅狄格州地址</option>
                                            
                                            <option value="/usa-address/delaware">特拉华州地址</option>
                                            
                                            <option value="/usa-address/florida">佛罗里达州地址</option>
                                            
                                            <option value="/usa-address/georgia">佐治亚州地址</option>
                                            
                                            <option value="/usa-address/hawaii">夏威夷州地址</option>
                                            
                                            <option value="/usa-address/idaho">爱达荷州地址</option>
                                            
                                            <option value="/usa-address/illinois">伊利诺伊州地址</option>
                                            
                                            <option value="/usa-address/indiana">印第安纳州地址</option>
                                            
                                            <option value="/usa-address/iowa">艾奥瓦州地址</option>
                                            
                                            <option value="/usa-address/kansas">堪萨斯州地址</option>
                                            
                                            <option value="/usa-address/kentucky">肯塔基州地址</option>
                                            
                                            <option value="/usa-address/lousiana">路易斯安那州地址</option>
                                            
                                            <option value="/usa-address/maine">缅因州地址</option>
                                            
                                            <option value="/usa-address/maryland">马里兰州地址</option>
                                            
                                            <option value="/usa-address/massachusetts">麻萨诸塞州地址</option>
                                            
                                            <option value="/usa-address/michigan">密歇根州地址</option>
                                            
                                            <option value="/usa-address/minnesota">明尼苏达州地址</option>
                                            
                                            <option value="/usa-address/mississippi">密西西比州地址</option>
                                            
                                            <option value="/usa-address/missouri">密苏里州地址</option>
                                            
                                            <option value="/usa-address/montana">蒙大拿州地址</option>
                                            
                                            <option value="/usa-address/nebraska">内布拉斯加州地址</option>
                                            
                                            <option value="/usa-address/nevada">内华达州地址</option>
                                            
                                            <option value="/usa-address/new-hampshire">新罕布什尔州地址</option>
                                            
                                            <option value="/usa-address/new-jersey">新泽西州地址</option>
                                            
                                            <option value="/usa-address/new-mexico">新墨西哥州地址</option>
                                            
                                            <option value="/usa-address/new-york">纽约州地址</option>
                                            
                                            <option value="/usa-address/north-carolina">北卡罗来纳州地址</option>
                                            
                                            <option value="/usa-address/north-dakota">北达科他州地址</option>
                                            
                                            <option value="/usa-address/ohio">俄亥俄州地址</option>
                                            
                                            <option value="/usa-address/oklahoma">俄克拉何马州地址</option>
                                            
                                            <option value="/usa-address/oregon">俄勒冈州地址</option>
                                            
                                            <option value="/usa-address/pennsylvania">宾夕法尼亚州地址</option>
                                            
                                            <option value="/usa-address/rhode-island">罗得岛州地址</option>
                                            
                                            <option value="/usa-address/south-carolina">南卡罗来纳州地址</option>
                                            
                                            <option value="/usa-address/south-dakota">南达科他州地址</option>
                                            
                                            <option value="/usa-address/tennessee">田纳西州地址</option>
                                            
                                            <option value="/usa-address/texas">得克萨斯州地址</option>
                                            
                                            <option value="/usa-address/utah">犹他州地址</option>
                                            
                                            <option value="/usa-address/vermont">佛蒙特州地址</option>
                                            
                                            <option value="/usa-address/virginia">弗吉尼亚州地址</option>
                                            
                                            <option value="/usa-address/washington">华盛顿州地址</option>
                                            
                                            <option value="/usa-address/west-virginia">西弗吉尼亚州地址</option>
                                            
                                            <option value="/usa-address/wisconsin">威斯康星州地址</option>
                                            
                                            <option value="/usa-address/wyoming">怀俄明州地址</option>
                                            
                                        </select><div class="layui-form-select"><div class="layui-select-title"><input type="text" placeholder="请选择" value="请选择州" class="layui-input" name=""><i class="layui-edge"></i></div><dl class="layui-anim layui-anim-upbit"><dd lay-value="/usa-address" class="layui-this">请选择州</dd><dd lay-value="/usa-address/alabama" class="">阿拉巴马州地址</dd><dd lay-value="/usa-address/alaska" class="">阿拉斯加州地址</dd><dd lay-value="/usa-address/arizona" class="">亚利桑那州地址</dd><dd lay-value="/usa-address/arkansas" class="">阿肯色州地址</dd><dd lay-value="/usa-address/california" class="">加利福尼亚州地址</dd><dd lay-value="/usa-address/colorado" class="">科罗拉多州地址</dd><dd lay-value="/usa-address/connecticut" class="">康涅狄格州地址</dd><dd lay-value="/usa-address/delaware" class="">特拉华州地址</dd><dd lay-value="/usa-address/florida" class="">佛罗里达州地址</dd><dd lay-value="/usa-address/georgia" class="">佐治亚州地址</dd><dd lay-value="/usa-address/hawaii" class="">夏威夷州地址</dd><dd lay-value="/usa-address/idaho" class="">爱达荷州地址</dd><dd lay-value="/usa-address/illinois" class="">伊利诺伊州地址</dd><dd lay-value="/usa-address/indiana" class="">印第安纳州地址</dd><dd lay-value="/usa-address/iowa" class="">艾奥瓦州地址</dd><dd lay-value="/usa-address/kansas" class="">堪萨斯州地址</dd><dd lay-value="/usa-address/kentucky" class="">肯塔基州地址</dd><dd lay-value="/usa-address/lousiana" class="">路易斯安那州地址</dd><dd lay-value="/usa-address/maine" class="">缅因州地址</dd><dd lay-value="/usa-address/maryland" class="">马里兰州地址</dd><dd lay-value="/usa-address/massachusetts" class="">麻萨诸塞州地址</dd><dd lay-value="/usa-address/michigan" class="">密歇根州地址</dd><dd lay-value="/usa-address/minnesota" class="">明尼苏达州地址</dd><dd lay-value="/usa-address/mississippi" class="">密西西比州地址</dd><dd lay-value="/usa-address/missouri" class="">密苏里州地址</dd><dd lay-value="/usa-address/montana" class="">蒙大拿州地址</dd><dd lay-value="/usa-address/nebraska" class="">内布拉斯加州地址</dd><dd lay-value="/usa-address/nevada" class="">内华达州地址</dd><dd lay-value="/usa-address/new-hampshire" class="">新罕布什尔州地址</dd><dd lay-value="/usa-address/new-jersey" class="">新泽西州地址</dd><dd lay-value="/usa-address/new-mexico" class="">新墨西哥州地址</dd><dd lay-value="/usa-address/new-york" class="">纽约州地址</dd><dd lay-value="/usa-address/north-carolina" class="">北卡罗来纳州地址</dd><dd lay-value="/usa-address/north-dakota" class="">北达科他州地址</dd><dd lay-value="/usa-address/ohio" class="">俄亥俄州地址</dd><dd lay-value="/usa-address/oklahoma" class="">俄克拉何马州地址</dd><dd lay-value="/usa-address/oregon" class="">俄勒冈州地址</dd><dd lay-value="/usa-address/pennsylvania" class="">宾夕法尼亚州地址</dd><dd lay-value="/usa-address/rhode-island" class="">罗得岛州地址</dd><dd lay-value="/usa-address/south-carolina" class="">南卡罗来纳州地址</dd><dd lay-value="/usa-address/south-dakota" class="">南达科他州地址</dd><dd lay-value="/usa-address/tennessee" class="">田纳西州地址</dd><dd lay-value="/usa-address/texas" class="">得克萨斯州地址</dd><dd lay-value="/usa-address/utah" class="">犹他州地址</dd><dd lay-value="/usa-address/vermont" class="">佛蒙特州地址</dd><dd lay-value="/usa-address/virginia" class="">弗吉尼亚州地址</dd><dd lay-value="/usa-address/washington" class="">华盛顿州地址</dd><dd lay-value="/usa-address/west-virginia" class="">西弗吉尼亚州地址</dd><dd lay-value="/usa-address/wisconsin" class="">威斯康星州地址</dd><dd lay-value="/usa-address/wyoming" class="">怀俄明州地址</dd></dl></div>
                                    </div>
                                </div>
                                
                                <div class="layui-form-item">
                                    <button type="submit" id="submit" class="layui-btn layui-btn-fluid" lay-submit="" lay-filter="demo1">生成
                                    </button>
                                </div>

                            </form>
                        </div>

                        <div class="layui-col-md-offset1">
                            <div class="layui-card-header">
                                <h3>热门城市</h3>
                            </div>
                            <ul class="layui-nav layui-nav-tree layui-bg-gray">
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-New-York"><a href="javascript:void(0);">纽约地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Los-Angeles"><a href="javascript:void(0);">洛杉矶地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Chicago"><a href="javascript:void(0);">芝加哥地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Houston"><a href="javascript:void(0);">休斯敦地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Phoenix"><a href="javascript:void(0);">菲尼克斯地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Philadelphia"><a href="javascript:void(0);">费城地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-San-Antonio"><a href="javascript:void(0);">圣安东尼奥地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-San-Diego"><a href="javascript:void(0);">圣地亚哥地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Dallas"><a href="javascript:void(0);">达拉斯地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-San-Jose"><a href="javascript:void(0);">圣何塞地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Austin"><a href="javascript:void(0);">奥斯汀地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Jacksonville"><a href="javascript:void(0);">杰克逊维尔地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Fort-Worth"><a href="javascript:void(0);">福和市地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Columbus"><a href="javascript:void(0);">哥伦布地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Charlotte"><a href="javascript:void(0);">夏洛特地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-San-Francisco"><a href="javascript:void(0);">旧金山地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Indianapolis"><a href="javascript:void(0);">印第安纳波利斯地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Seattle"><a href="javascript:void(0);">西雅图地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Denver"><a href="javascript:void(0);">丹佛地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Washington"><a href="javascript:void(0);">华盛顿地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Boston"><a href="javascript:void(0);">波士顿地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-El-Paso"><a href="javascript:void(0);">艾尔帕索地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Nashville"><a href="javascript:void(0);">纳什维尔 地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Detroit"><a href="javascript:void(0);">底特律地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Oklahoma-City"><a href="javascript:void(0);">俄克拉荷马城地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Portland"><a href="javascript:void(0);">波特兰地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Las-Vegas"><a href="javascript:void(0);">拉斯维加斯地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Memphis"><a href="javascript:void(0);">孟菲斯地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Louisville"><a href="javascript:void(0);">路易维尔地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Baltimore"><a href="javascript:void(0);">巴尔的摩地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Milwaukee"><a href="javascript:void(0);">密尔沃基地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Albuquerque"><a href="javascript:void(0);">阿尔伯克基地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Tucson"><a href="javascript:void(0);">图森地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Fresno"><a href="javascript:void(0);">弗雷斯诺地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Mesa"><a href="javascript:void(0);">梅萨地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Sacramento"><a href="javascript:void(0);">萨克拉门托地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Atlanta"><a href="javascript:void(0);">亚特兰大地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Kansas-City"><a href="javascript:void(0);">堪萨斯城地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Colorado-Springs"><a href="javascript:void(0);">科羅拉多泉地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Omaha"><a href="javascript:void(0);">奥马哈地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Raleigh"><a href="javascript:void(0);">罗利地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Miami"><a href="javascript:void(0);">迈阿密地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Long-Beach"><a href="javascript:void(0);">长滩地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Virginia-Beach"><a href="javascript:void(0);">弗吉尼亚海滩地址</a></li>
                                
                                <li class="layui-nav-item hotCity" attr-url="/usa-address/hot-city-Oakland"><a href="javascript:void(0);">奥克兰地址</a></li>
                                
                            <span class="layui-nav-bar"></span></ul>
                        </div>
                    </div>
                    <div class="layui-col-md9">
                        <div class="layui-card-header">
                            <h3>美国地址生成器 - 美国身份生成器 - 美国信用卡生成器</h3>
                        </div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">姓名</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="full_name" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">性别</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="gender" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">称呼</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="salutation" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">出生日期</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="birthday" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">州</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="state" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">州全称</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="state_full" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">城市</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="city" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">邮编</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="zip_code" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">街道地址</label>
                                <div class="layui-input-block">
                                    <input type="text" id="street" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">电话</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="phone_number" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                
                                
                                
                                
                                
                                
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">信用卡类型</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="credit_card_type" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">信用卡号</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="credit_card_number" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">信用卡CVV2</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="credit_card_cvv2" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">信用卡有效期</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="credit_card_expire" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div><div class="google-auto-placed" style="width: 100%; height: auto; clear: both; text-align: center;"><ins data-ad-format="auto" class="adsbygoogle adsbygoogle-noablate" data-ad-client="ca-pub-5832698961587394" data-adsbygoogle-status="done" style="display: block; margin: 10px auto; background-color: transparent; height: 280px;" data-ad-status="filled"><div id="aswift_3_host" style="border: none; height: 280px; width: 645px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;"><iframe id="aswift_3" name="aswift_3" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:645px;height:280px;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="645" height="280" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/ads?gdpr=0&amp;client=ca-pub-5832698961587394&amp;output=html&amp;h=280&amp;adk=3834119489&amp;adf=852203437&amp;pi=t.aa~a.3403044894~rp.4&amp;w=645&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1735384809&amp;rafmt=1&amp;to=qs&amp;pwprc=7351060718&amp;format=645x280&amp;url=https%3A%2F%2Fwww.addressgenerator.net%2F&amp;fwr=0&amp;pra=3&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;fa=40&amp;uach=**********************************************************************************************************************************************************************************************************************..&amp;dt=1735384809267&amp;bpp=1&amp;bdt=1415&amp;idt=1&amp;shv=r20241212&amp;mjsv=m202412090101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3D4fe768c5b113a7d7%3AT%3D1735384807%3ART%3D1735384807%3AS%3DALNI_MaELslNeeLW6EtnLiVgtkn4OEaTng&amp;gpic=UID%3D00000fbee0cd9eff%3AT%3D1735384807%3ART%3D1735384807%3AS%3DALNI_MbVBCf3y2xZ8BCDdytthYYtYmy-RQ&amp;eo_id_str=ID%3Da5d5aeff59d94c6e%3AT%3D1735384807%3ART%3D1735384807%3AS%3DAA-Afjag6BSco67z6Z1yWROkggFg&amp;prev_fmts=0x0%2C967x280%2C645x280%2C1013x654%2C1005x124&amp;nras=6&amp;correlator=2465479838469&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=2&amp;u_h=864&amp;u_w=1536&amp;u_ah=824&amp;u_aw=1536&amp;u_cd=24&amp;u_sd=1.25&amp;dmc=8&amp;adx=184&amp;ady=2988&amp;biw=1013&amp;bih=654&amp;scr_x=0&amp;scr_y=382&amp;eid=31089326%2C31089327%2C31089338%2C95332924%2C95344791%2C95345967&amp;oid=2&amp;psts=AOrYGsnUqSp3Xw1XCHN7VLnjOWXDbpiDFdeafma7fEcaCEXHxYXdSDPUqVGzUdvYnXUlIVZQZt-vAt0UqhntlM04Wb9njBe8&amp;pvsid=760930823005664&amp;tmod=970634376&amp;wsm=1&amp;uas=0&amp;nvt=1&amp;fc=1920&amp;brdim=9%2C9%2C9%2C9%2C1536%2C0%2C1051%2C805%2C1028%2C670&amp;vis=1&amp;rsz=%7C%7Cs%7C&amp;abl=NS&amp;fu=1152&amp;bc=31&amp;bz=1.02&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=4&amp;uci=a!4&amp;btvi=3&amp;fsb=1&amp;dtd=424" data-google-container-id="a!4" tabindex="0" title="Advertisement" aria-label="Advertisement" data-google-query-id="CMO13ZisyooDFRVZwgUdHnQAFw" data-load-complete="true"></iframe></div></ins></div>
            </div><div class="google-auto-placed" style="width: 100%; height: auto; clear: both; text-align: center;"><ins data-ad-format="auto" class="adsbygoogle adsbygoogle-noablate" data-ad-client="ca-pub-5832698961587394" data-adsbygoogle-status="done" style="display: block; margin: 10px auto; background-color: transparent; height: 0px;" data-ad-status="unfilled"><div id="aswift_4_host" style="border: none; height: 0px; width: 645px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: hidden; opacity: 0;"><iframe id="aswift_4" name="aswift_4" browsingtopics="true" style="left: 0px; position: absolute; top: 0px; border: 0px; width: 645px; height: 0px;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="645" height="0" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/ads?gdpr=0&amp;client=ca-pub-5832698961587394&amp;output=html&amp;h=100&amp;adk=794537774&amp;adf=3059942456&amp;pi=t.aa~a.3700854777~rp.4&amp;w=645&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1735384810&amp;rafmt=1&amp;to=qs&amp;pwprc=7351060718&amp;format=645x100&amp;url=https%3A%2F%2Fwww.addressgenerator.net%2F&amp;fwr=0&amp;pra=3&amp;rpe=1&amp;resp_fmts=3&amp;wgl=1&amp;fa=40&amp;uach=**********************************************************************************************************************************************************************************************************************..&amp;dt=1735384809276&amp;bpp=1&amp;bdt=1424&amp;idt=1&amp;shv=r20241212&amp;mjsv=m202412090101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3D4fe768c5b113a7d7%3AT%3D1735384807%3ART%3D1735384807%3AS%3DALNI_MaELslNeeLW6EtnLiVgtkn4OEaTng&amp;gpic=UID%3D00000fbee0cd9eff%3AT%3D1735384807%3ART%3D1735384807%3AS%3DALNI_MbVBCf3y2xZ8BCDdytthYYtYmy-RQ&amp;eo_id_str=ID%3Da5d5aeff59d94c6e%3AT%3D1735384807%3ART%3D1735384807%3AS%3DAA-Afjag6BSco67z6Z1yWROkggFg&amp;prev_fmts=0x0%2C967x280%2C645x280%2C1013x654%2C1005x124%2C645x280&amp;nras=7&amp;correlator=2465479838469&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=2&amp;u_h=864&amp;u_w=1536&amp;u_ah=824&amp;u_aw=1536&amp;u_cd=24&amp;u_sd=1.25&amp;dmc=8&amp;adx=184&amp;ady=3278&amp;biw=1013&amp;bih=654&amp;scr_x=0&amp;scr_y=692&amp;eid=31089326%2C31089327%2C31089338%2C95332924%2C95344791%2C95345967&amp;oid=2&amp;psts=AOrYGsnUqSp3Xw1XCHN7VLnjOWXDbpiDFdeafma7fEcaCEXHxYXdSDPUqVGzUdvYnXUlIVZQZt-vAt0UqhntlM04Wb9njBe8%2CAOrYGslbriocN7TU_MMGT523jhAoXsbupAmQzaPF_oTUxA9bklz1GEiFotliiJedFx8C-wakIMNcTrl4qMx3221R8ToRh3n4qWshOg96rtW2R7szyhlLlg%2CAOrYGslEBGOqsZjYOwWmgPcMBCSC3gFS4mRDBP_w2vnps23ovrUh7XiTe7ngaJdbyDHxH2LRJuYZ1Bf-OsdYs1pJZrMQxBz6&amp;pvsid=760930823005664&amp;tmod=970634376&amp;wsm=1&amp;uas=0&amp;nvt=1&amp;fc=1920&amp;brdim=9%2C9%2C9%2C9%2C1536%2C0%2C1051%2C805%2C1028%2C670&amp;vis=1&amp;rsz=%7C%7Cs%7C&amp;abl=NS&amp;fu=1152&amp;bc=31&amp;bz=1.02&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=5&amp;uci=a!5&amp;btvi=4&amp;fsb=1&amp;dtd=1152" data-google-container-id="a!5" tabindex="0" title="Advertisement" aria-label="Advertisement" data-load-complete="true" data-google-query-id="CLuii5msyooDFSJFwgUdmDQNXg"></iframe></div></ins></div>
            <br><br>
            <blockquote class="layui-text layui-footer">
                <p style="text-align: center">
                    Copyright © 2023 addressgenerator.net
                </p>
                <p style="text-align: center">
                    在线信息生成，包含美国，中国，英国，日本，澳大利亚等全世界各个国家及地区的个人信息生成、街道地址生成，美国SSN生成以及其他个人身份生成。
该地址生成器的目的只是为了大家学习，学习各国的地址格式，了解地名和街道的名称，请不要用于非法用途。
                </p>
            </blockquote>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/layui/2.8.4/layui.min.js" integrity="sha512-mEdCms+HGgsYbUy8daQZbNCVMUyB6EmYkPghnol/oE+veRTbMIcHhMiD7w2Urp66klyNVKLN27S/ByN0i4+Jxg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script><ins class="adsbygoogle adsbygoogle-noablate" data-adsbygoogle-status="done" style="display: none !important;" data-ad-status="unfilled"><div id="aswift_0_host" style="border: none; height: 0px; width: 0px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block;"><iframe id="aswift_0" name="aswift_0" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:undefinedpx;height:undefinedpx;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-5832698961587394&amp;output=html&amp;adk=1812271804&amp;adf=3025194257&amp;abgtt=6&amp;lmt=1735384808&amp;plat=3%3A65536%2C4%3A65536%2C9%3A32776%2C16%3A8388608%2C17%3A32%2C24%3A32%2C25%3A32%2C30%3A1048576%2C32%3A32%2C41%3A32%2C42%3A32&amp;format=0x0&amp;url=https%3A%2F%2Fwww.addressgenerator.net%2F&amp;pra=5&amp;wgl=1&amp;aihb=0&amp;aiof=3&amp;asro=0&amp;ailel=1~2~4~6~7~8~9~10~11~12~13~14~15~16~17~18~19~20~21~24~29~30~34&amp;aiael=1~2~3~4~6~7~8~9~10~11~12~13~14~15~16~17~18~19~20~21~24~29~30~34&amp;aifxl=29_18~30_19&amp;aiixl=29_5~30_6&amp;aiapm=0.3221&amp;aiapmi=0.33938&amp;aiombap=1&amp;aief=1&amp;uach=**********************************************************************************************************************************************************************************************************************..&amp;dt=1735384808134&amp;bpp=121&amp;bdt=282&amp;idt=379&amp;shv=r20241212&amp;mjsv=m202412090101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie_enabled=1&amp;eoidce=1&amp;nras=1&amp;correlator=2465479838469&amp;frm=20&amp;pv=2&amp;u_tz=480&amp;u_his=2&amp;u_h=864&amp;u_w=1536&amp;u_ah=824&amp;u_aw=1536&amp;u_cd=24&amp;u_sd=1.25&amp;dmc=8&amp;adx=-12245933&amp;ady=-12245933&amp;biw=1013&amp;bih=654&amp;scr_x=0&amp;scr_y=0&amp;eid=31089326%2C31089327%2C31089338%2C95332924%2C95344791%2C95345967&amp;oid=2&amp;pvsid=760930823005664&amp;tmod=970634376&amp;wsm=1&amp;uas=0&amp;nvt=1&amp;fsapi=1&amp;fc=1920&amp;brdim=9%2C9%2C9%2C9%2C1536%2C0%2C1051%2C805%2C1028%2C670&amp;vis=1&amp;rsz=%7C%7Cs%7C&amp;abl=NS&amp;fu=33792&amp;bc=31&amp;bz=1.02&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=1&amp;uci=a!1&amp;fsb=1&amp;dtd=413" data-google-container-id="a!1" tabindex="0" title="Advertisement" aria-label="Advertisement" data-load-complete="true"></iframe></div></ins>
<script>
    layui.use(['form', 'upload'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        var util = layui.util;

        util.on('lay-on', {
            msg: function () {
                layer.msg('敬请期待');
            },
        });

        form.on('submit(demo1)', function (data) {
            $.ajax({
                url: "https://generator.addressgenerator.net/api/generator",
                type: "get",
                data: {url: data.field.url},
                success: function (data) {
                    ad = JSON.parse(data)
                    document.getElementById("full_name").value = ad.Full_Name
                    document.getElementById("gender").value = ad.Gender
                    document.getElementById("birthday").value = ad.Birthday
                    document.getElementById("salutation").value = ad.Title
                    document.getElementById("street").value = ad.Address
                    document.getElementById("city").value = ad.City
                    document.getElementById("state").value = ad.State
                    document.getElementById("state_full").value = ad.State_Full
                    document.getElementById("zip_code").value = ad.Zip_Code
                    document.getElementById("phone_number").value = ad.Telephone
                    document.getElementById("credit_card_type").value = ad.Credit_Card_Type
                    document.getElementById("credit_card_number").value = ad.Credit_Card_Number
                    document.getElementById("credit_card_cvv2").value = ad.CVV2
                    document.getElementById("credit_card_expire").value = ad.Expires
                },
                failed: function (data) {
                    layer.msg('获取失败');
                    $("#submit").attr("disabled", false);
                }
            });
            return false;
        });

        $(".hotCity").click(function () {
            console.log($(this).attr("attr-url"));
            $.ajax({
                url: "https://generator.addressgenerator.net/api/generator",
                type: "get",
                data: {url: $(this).attr("attr-url")},
                success: function (data) {
                    ad = JSON.parse(data)
                    document.getElementById("full_name").value = ad.Full_Name
                    document.getElementById("gender").value = ad.Gender
                    document.getElementById("birthday").value = ad.Birthday
                    document.getElementById("salutation").value = ad.Title
                    document.getElementById("street").value = ad.Address
                    document.getElementById("city").value = ad.City
                    document.getElementById("state").value = ad.State
                    document.getElementById("state_full").value = ad.State_Full
                    document.getElementById("zip_code").value = ad.Zip_Code
                    document.getElementById("phone_number").value = ad.Telephone
                    document.getElementById("credit_card_type").value = ad.Credit_Card_Type
                    document.getElementById("credit_card_number").value = ad.Credit_Card_Number
                    document.getElementById("credit_card_cvv2").value = ad.CVV2
                    document.getElementById("credit_card_expire").value = ad.Expires
                    $("#submit").attr("disabled", false);
                },
                failed: function (data) {
                    layer.msg('获取失败');
                    $("#submit").attr("disabled", false);
                }
            });
        });
    });

</script>

<script async="" src="https://www.googletagmanager.com/gtag/js?id=G-B74F1D2NS6"></script>
<script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        dataLayer.push(arguments);
    }

    gtag('js', new Date());

    gtag('config', 'G-B74F1D2NS6');
</script>
<script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;8f9128c388bf7bfd&quot;,&quot;version&quot;:&quot;2024.10.5&quot;,&quot;r&quot;:1,&quot;token&quot;:&quot;2468020a7c2c431aa6facc2c88ff4c9d&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}}}" crossorigin="anonymous"></script>



<iframe name="googlefcPresent" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><ins class="adsbygoogle adsbygoogle-noablate" data-adsbygoogle-status="done" data-anchor-status="displayed" data-ad-status="filled" style="display: block; width: 1028px !important; height: 129px !important; bottom: 0px; clear: none !important; float: none !important; left: 0px; margin: 0px !important; max-height: none !important; max-width: none !important; opacity: 1; overflow: visible !important; padding: 0px !important; position: fixed; right: auto !important; top: auto !important; vertical-align: baseline !important; visibility: visible !important; z-index: 2147483647; background: rgb(250, 250, 250) !important;" data-anchor-shown="true"><div class="grippy-host"></div><div id="aswift_6_host" style="border: none !important; height: 124px !important; width: 100% !important; margin: 0px !important; padding: 0px !important; position: relative !important; visibility: visible !important; background-color: transparent !important; display: inline-block !important; inset: auto !important; clear: none !important; float: none !important; max-height: none !important; max-width: none !important; opacity: 1 !important; overflow: visible !important; vertical-align: baseline !important; z-index: auto !important;"><iframe id="aswift_6" name="aswift_6" browsingtopics="true" style="width: 1005px !important; height: 124px !important; display: block; margin: 0px auto;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="1005" height="124" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/html/r20241212/r20190131/zrt_lookup_fy2021.html#RS-1-&amp;adk=1812271801&amp;client=ca-pub-5832698961587394&amp;fa=1&amp;ifi=7&amp;uci=a!7&amp;btvi=2" data-google-container-id="a!7" tabindex="0" title="Advertisement" aria-label="Advertisement" data-google-query-id="CKP8nZisyooDFS5IwgUdtRAZaQ" data-load-complete="true"></iframe></div></ins><iframe name="__tcfapiLocator" src="about:blank" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><iframe name="googlefcInactive" src="about:blank" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><iframe name="googlefcLoaded" src="about:blank" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><iframe src="https://ep2.adtrafficquality.google/sodar/sodar2/232/runner.html" width="0" height="0" style="display: none;"></iframe><iframe src="https://www.google.com/recaptcha/api2/aframe" width="0" height="0" style="display: none;"></iframe></body><iframe id="google_esf" name="google_esf" src="https://googleads.g.doubleclick.net/pagead/html/r20241212/r20190131/zrt_lookup_fy2021.html" style="display: none;"></iframe><ins class="adsbygoogle adsbygoogle-noablate" data-adsbygoogle-status="done" style="display: none !important; width: 100vw !important; height: 100vh !important; inset: 0px auto auto 0px !important; clear: none !important; float: none !important; margin: 0px !important; max-height: none !important; max-width: none !important; opacity: 1 !important; overflow: visible !important; padding: 0px !important; position: fixed !important; vertical-align: baseline !important; visibility: visible !important; z-index: 2147483647 !important; background: transparent !important;" aria-hidden="true" data-ad-status="filled" data-vignette-loaded="true"><div id="aswift_5_host" style="border: none !important; height: 100vh !important; width: 100vw !important; margin: 0px !important; padding: 0px !important; position: relative !important; visibility: visible !important; background-color: transparent !important; display: inline-block !important; inset: auto !important; clear: none !important; float: none !important; max-height: none !important; max-width: none !important; opacity: 1 !important; overflow: visible !important; vertical-align: baseline !important; z-index: auto !important;"><iframe id="aswift_5" name="aswift_5" browsingtopics="true" style="width: 100vw !important; height: 100vh !important; inset: 0px auto auto 0px !important; position: absolute !important; clear: none !important; display: inline !important; float: none !important; margin: 0px !important; max-height: none !important; max-width: none !important; opacity: 1 !important; overflow: visible !important; padding: 0px !important; vertical-align: baseline !important; visibility: visible !important; z-index: auto !important;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="" height="" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/html/r20241212/r20190131/zrt_lookup_fy2021.html#RS-0-&amp;adk=1812271808&amp;client=ca-pub-5832698961587394&amp;fa=8&amp;ifi=6&amp;uci=a!6" data-google-container-id="a!6" tabindex="0" title="Advertisement" aria-label="Advertisement" data-google-query-id="CKH8nZisyooDFS5IwgUdtRAZaQ" data-load-complete="true"></iframe></div></ins></html>