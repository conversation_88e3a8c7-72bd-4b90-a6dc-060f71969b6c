# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/10/25 20:52
# <AUTHOR> shaocanfan
# @File    : my_claude.py
# @explain : 

# import requests
#
#
# url = 'https://www.snaily.top/v1/chat/completions'  # claud key
# headers = {
#     'Authorization': 'sk-wJ5brS9KSRupxHDgLrXk9II7hBfyQqgdCPsJfa4RuVHfcsFP',  # claud key
#     'Content-Type': 'application/json',
# }
#
# data = {
#
#     'model': 'claude-3-5-sonnet-20241022',
#     'messages': [
#         {
#             'role': 'system',
#             # 'content': '你是一位技艺精湛的新闻撰稿人，擅长编写引人入胜但不失真实性的新闻文章。你的作品既要忠实于提供的参考材料，又要在不歪'
#             #            '曲事实的前提下增添一些有助于理解的背景信息和细节，使文章更为完整和动人。现在，请你根据我提供的一篇新闻文章，首先总'
#             #            '结其要点，然后利用你的知识和文笔，创作一篇约100字的新闻文章。文章应该保持客观、准确且语句流畅,且不能偏离原文的本意，'
#             #            '分为一到两个段落。\n\n要求输出的格式如下：\n\n1、新闻要点总结：\n\n2、新闻文章撰写：\n标题：\n正文：（分为一段或者'
#             #            '两段）\n\n3、结语：'
#             'content': "",
#         },
#
#         {
#             'role': 'user',
#             'content': "告诉我现在东八区的时间，然后从0到100随机抽取一个数",
#         },
#
#     ],
#
# }
#
# response = requests.post(url, headers=headers, json=data)
# data = response.json()
# txt = data['choices'][0]['message']['content']
#
#
# print(data)

# -----------------------------------------------------azure openai-----------------------------------------------------------------

import requests


url = 'https://free2openapi.openai.azure.com/openai/deployments/gpt-4/chat/completions?api-version=2024-08-01-preview'
headers = {
    'API-KEY': '3Ui9yX4bk4jY2lOIpvjrXFeMV5HxYKghQ6tIjYCzL9koVk2J2ebFJQQJ99AKACYeBjFXJ3w3AAABACOG3WWv',
    'Content-Type': 'application/json',
}

data = {

    'model': 'gpt-4',
    'messages': [
        {
            'role': 'system',
            # 'content': '你是一位技艺精湛的新闻撰稿人，擅长编写引人入胜但不失真实性的新闻文章。你的作品既要忠实于提供的参考材料，又要在不歪'
            #            '曲事实的前提下增添一些有助于理解的背景信息和细节，使文章更为完整和动人。现在，请你根据我提供的一篇新闻文章，首先总'
            #            '结其要点，然后利用你的知识和文笔，创作一篇约100字的新闻文章。文章应该保持客观、准确且语句流畅,且不能偏离原文的本意，'
            #            '分为一到两个段落。\n\n要求输出的格式如下：\n\n1、新闻要点总结：\n\n2、新闻文章撰写：\n标题：\n正文：（分为一段或者'
            #            '两段）\n\n3、结语：'
            'content': "",
        },

        {
            'role': 'user',
            'content': "who are u",
        },

    ],

}

response = requests.post(url, headers=headers, json=data)
data = response.json()
content = data['choices'][0]['message']['content']

print(content)