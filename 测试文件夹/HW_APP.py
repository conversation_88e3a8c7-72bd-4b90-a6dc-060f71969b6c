# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/9/9 16:54
# <AUTHOR> shaocanfan
# @File    : HW_APP.py
# @explain : 

# import paho.mqtt.client as mqtt
# import json
# import time
# from threading import Timer
#
# # MQTT 配置信息
# MQTT_SERVER = "b698a7d81d.st1.iotda-device.cn-south-1.myhuaweicloud.com"
# MQTT_PORT = 1883
# CLIENT_ID = "66dde66b1837002b28b3bbef_esp8266_test02_0_0_2024090903"
# USERNAME = "66dde66b1837002b28b3bbef_esp8266_test02"
# PASSWORD = "133a86a9bc32a546aea01a61c595b54bfad9e2226161cbad9c3cd9d1d8b0760d"
# SUBSCRIBE_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/messages/down"
# PUBLISH_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/properties/report"
#
# # 设备A的设备影子相关主题
# # SHADOW_GET_TOPIC_A = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/shadow/get/request_id=123"
# # SHADOW_RESPONSE_TOPIC_A = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/shadow/get/response/#"
#
# SHADOW_GET_TOPIC_A = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/commands/request_id=123"
# SHADOW_RESPONSE_TOPIC_A = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/commands/response/#"
#
# QUERY_INTERVAL = 30  # 查询间隔时间（1秒）
#
# # shadow_data_send = { # 指定获取test01设备的影子数据
# # 	"object_device_id": "66dde66b1837002b28b3bbef_esp8266_test01",
# # 	"service_id": "test01"
# # }
# # shadow_data_send = 	{"paras":{"target_num":333},"service_id":"test01","command_name":"control"}
# shadow_data_send = {
#     "result_code": 0,  # 0表示成功，非0表示失败
#     "paras": {
#         "result": "success"  # 自定义的回复参数，可根据设备的业务逻辑定义
#     }
# }
#
# # 当连接到MQTT服务器时回调
# def on_connect(client, userdata, flags, rc):
#     print(f"Connected with result code {rc}")
#     # 订阅设备A的影子响应主题
#     client.subscribe(SHADOW_RESPONSE_TOPIC_A)
#     print(f"Subscribed to topic: {SHADOW_RESPONSE_TOPIC_A}")
#     # 启动定时查询设备A的设备影子
#     query_device_shadow()
#
# # 回调函数 - 当收到服务器下发的影子响应时被调用
# def on_message(client, userdata, msg):
#     print(f"Received message from topic '{msg.topic}': {msg.payload.decode()}")
#     client.publish(SHADOW_RESPONSE_TOPIC_A, json.dumps(shadow_data_send))
#     # 如果消息来自设备A的影子响应主题，解析其中的temp值
#     if msg.topic == SHADOW_RESPONSE_TOPIC_A:
#         try:
#             # 将消息解析为 JSON
#             payload = json.loads(msg.payload.decode())
#             # 检查是否包含 services 字段
#             if "services" in payload:
#                 services = payload["services"]
#                 # 遍历所有服务，查找 temp 属性
#                 for service in services:
#                     if "properties" in service and "temp" in service["properties"]:
#                         temp_value = service["properties"]["temp"]
#                         print(f"Received temp value from device A: {temp_value}")
#         except json.JSONDecodeError:
#             print("Failed to decode JSON from shadow response")
#
# # 查询设备A的设备影子，每隔1秒钟查询一次
# def query_device_shadow():
#     client.publish(SHADOW_GET_TOPIC_A, json.dumps(shadow_data_send))
#     print(f"Published shadow get request to topic: {SHADOW_GET_TOPIC_A}")
#     # 设置定时器，以继续定期请求设备A的影子数据
#     Timer(QUERY_INTERVAL, query_device_shadow).start()
#
# # 创建MQTT客户端并设置回调函数
# client = mqtt.Client(CLIENT_ID, protocol=mqtt.MQTTv311)
# client.username_pw_set(USERNAME, PASSWORD)
# client.on_connect = on_connect
# client.on_message = on_message
#
# # 连接到MQTT服务器
# client.connect(MQTT_SERVER, MQTT_PORT, 60)
#
# # 启动网络循环
# client.loop_start()
#
# try:
#     while True:
#         time.sleep(1)
# except KeyboardInterrupt:
#     print("Disconnecting from MQTT server...")
#     client.loop_stop()
#     client.disconnect()

# ------------------------------------------------------------------------------------------------------------------------

# import paho.mqtt.client as mqtt
# import json
# import time
# from threading import Timer
#
# # MQTT 配置信息
# MQTT_SERVER = "b698a7d81d.st1.iotda-device.cn-south-1.myhuaweicloud.com"
# MQTT_PORT = 1883
# CLIENT_ID = "66dde66b1837002b28b3bbef_esp8266_test02_0_0_2024090903"
# USERNAME = "66dde66b1837002b28b3bbef_esp8266_test02"
# PASSWORD = "133a86a9bc32a546aea01a61c595b54bfad9e2226161cbad9c3cd9d1d8b0760d"
# SUBSCRIBE_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/messages/down"
# PUBLISH_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/properties/report"
#
# shadow_topic = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/shadow/get/response"
# SHADOW_GET_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/sys/shadow/get"
#
# COMMAND_TOPIC = "$oc/devices/66dde66b1837002b28b3bbef_esp8266_test02/user/shuzhi"
# PUBLISH_INTERVAL = 10  # 定时器间隔，单位：秒
#
# # 定义发布的命令消息
# command_payload = {
#     "command": {
#         "value": 100
#     }
# }
#
# # 当连接到MQTT服务器时回调
# def on_connect(client, userdata, flags, rc):
#     print(f"Connected with result code {rc}")
#     # 订阅命令主题
#     client.subscribe(COMMAND_TOPIC)
#     print(f"Subscribed to topic: {COMMAND_TOPIC}")
#
# # 当收到消息时回调
# def on_message(client, userdata, msg):
#     print(f"Received message from topic '{msg.topic}': {msg.payload.decode()}")
#
#     # 解析消息并提取命令数值
#     try:
#         payload = json.loads(msg.payload.decode())
#         if "command" in payload and "value" in payload["command"]:
#             command_value = payload["command"]["value"]
#             print(f"Received command value: {command_value}")
#     except json.JSONDecodeError:
#         print("Failed to decode JSON from received message")
#
# # 定时发布命令消息
# def publish_message():
#     client.publish(COMMAND_TOPIC, json.dumps(command_payload))
#     print(f"Published message: {json.dumps(command_payload)}")
#     # 设置定时器，以继续定期发布消息
#     Timer(PUBLISH_INTERVAL, publish_message).start()
#
# # 创建MQTT客户端并设置回调函数
# client = mqtt.Client(CLIENT_ID, protocol=mqtt.MQTTv311)
# client.username_pw_set(USERNAME, PASSWORD)
# client.on_connect = on_connect
# client.on_message = on_message
#
# # 连接到MQTT服务器
# client.connect(MQTT_SERVER, MQTT_PORT, 60)
#
# # 启动网络循环
# client.loop_start()
#
# # 启动定时发布消息
# publish_message()
#
# try:
#     while True:
#         time.sleep(1)
# except KeyboardInterrupt:
#     print("Disconnecting from MQTT server...")
#     client.loop_stop()
#     client.disconnect()
# ------------------------------------------------------------------------------------------------------------------------

# --------------------------------------------------------------------------------------------------------------------
# coding: utf-8

# import os
# from huaweicloudsdkcore.auth.credentials import BasicCredentials
# from huaweicloudsdkcore.auth.credentials import DerivedCredentials
# from huaweicloudsdkcore.region.region import Region as coreRegion
# from huaweicloudsdkcore.exceptions import exceptions
# from huaweicloudsdkiotda.v5 import *
#
# if __name__ == "__main__":
#     ak = os.environ["HUAWEICLOUD_SDK_AK"]
#     sk = os.environ["HUAWEICLOUD_SDK_SK"]
#
#     iotdaEndpoint = "https://b698a7d81d.st1.iotda-app.cn-south-1.myhuaweicloud.com:443/v5/iot/f487046c26bc40399bcc4d3f5a730401/devices/66dde66b1837002b28b3bbef_esp8266_test02/commands"
#
#     credentials = BasicCredentials(ak, sk).with_derived_predicate(DerivedCredentials.get_default_derived_predicate())
#
#     client = IoTDAClient.new_builder().with_credentials(credentials).with_region(coreRegion(id="cn-south-1", endpoint=iotdaEndpoint)).build()
#
#
#
#     try:
#         request = CreateCommandRequest()
#         request.device_id = "66dde66b1837002b28b3bbef_esp8266_test02"
#         request.body = DeviceCommandRequest(
#             paras={"target_num":330},
#             command_name="control",
#             service_id="test01"
#         )
#         response = client.create_command(request)
#         print(response)
#     except exceptions.ClientRequestException as e:
#         print(e.status_code)
#         print(e.request_id)
#         print(e.error_code)
#         print(e.error_msg)



import os
import tkinter as tk
from tkinter import messagebox
from huaweicloudsdkcore.auth.credentials import BasicCredentials
from huaweicloudsdkcore.auth.credentials import DerivedCredentials
from huaweicloudsdkcore.region.region import Region as coreRegion
from huaweicloudsdkcore.exceptions import exceptions
from huaweicloudsdkiotda.v5 import *

# 配置华为云的AK、SK
ak = os.environ["HUAWEICLOUD_SDK_AK"]
sk = os.environ["HUAWEICLOUD_SDK_SK"]

# IoTDA的Endpoint
iotdaEndpoint = "https://b698a7d81d.st1.iotda-app.cn-south-1.myhuaweicloud.com:443/v5/iot/f487046c26bc40399bcc4d3f5a730401/devices/66dde66b1837002b28b3bbef_esp8266_test01/commands"

# 配置华为云认证信息
credentials = BasicCredentials(ak, sk).with_derived_predicate(DerivedCredentials.get_default_derived_predicate())

# 创建华为云IoTDA客户端
client = IoTDAClient.new_builder().with_credentials(credentials).with_region(coreRegion(id="cn-south-1", endpoint=iotdaEndpoint)).build()

# 发送控制命令到设备
def send_command(target_num):
    try:
        request = CreateCommandRequest()
        request.device_id = "66dde66b1837002b28b3bbef_esp8266_test01"
        request.body = DeviceCommandRequest(
            paras={"target_num": target_num},
            command_name="control",
            service_id="test01"
        )
        response = client.create_command(request)
        if target_num == 1:
            messagebox.showinfo("Success", "空调已开！")
        if target_num == 0:
            messagebox.showinfo("Success", "空调已关！")
        # messagebox.showinfo("Success", f"Command sent successfully with target_num={target_num}")
        print(response)
    except exceptions.ClientRequestException as e:
        messagebox.showerror("Error", f"Error: {e.error_msg}")
        print(e.status_code)
        print(e.request_id)
        print(e.error_code)
        print(e.error_msg)

# 开关按钮的事件处理函数
def on_toggle():
    if switch_var.get():
        send_command(1)  # 发送target_num=1
        status_label.config(text="空调已开")  # 更新标签显示
    else:
        send_command(0)  # 发送target_num=0
        status_label.config(text="空调已关")  # 更新标签显示

# 创建主界面窗口
root = tk.Tk()
root.title("Huawei IoT to Control air conditioner")
root.geometry("300x200")

# 创建一个变量用于保存开关状态
switch_var = tk.BooleanVar()

# 创建一个按钮，当按钮打开时显示空调状态
switch_button = tk.Checkbutton(root, text="Device Control", variable=switch_var, command=on_toggle, onvalue=True, offvalue=False)
switch_button.pack(pady=20)

# 创建一个标签来显示状态信息
status_label = tk.Label(root, text="空调已关", font=("Arial", 12))
status_label.pack(pady=20)

# 运行主循环
root.mainloop()


