# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/12/28 17:04
# <AUTHOR> shaocanfan
# @File    : fake_identity.py
# @explain :
import time

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from bs4 import BeautifulSoup
from webdriver_manager.microsoft import EdgeChromiumDriverManager

class WebScraper:
    def __init__(self, headless=True):
        # 设置浏览器选项
        options = webdriver.EdgeOptions()
        options.use_chromium = True  # 确保使用基于 Chromium 的 Edge
        # options.add_argument("disable-gpu")  # 如果在无头模式下运行，需要禁用GPU
        # if headless:
        #     options.add_argument('--headless')
        self.driver = webdriver.Edge(service=Service(EdgeChromiumDriverManager().install()), options=options)


    def load_page(self, url, wait_time=20):
        # 加载页面
        self.driver.get(url)
        self.driver.implicitly_wait(wait_time)
        self.driver.find_element(By.XPATH,'//*[@id="submit"]').click()
        time.sleep(2)

    def get_page_source(self):
        # 获取页面html内容
        with open('crawl_news.html', 'w', encoding='utf-8') as f:
            f.write(self.driver.page_source)
        return self.driver.page_source

    def parse_html(self, page_source):
        # 解析HTML
        soup = BeautifulSoup(page_source,'lxml')
        return soup

    def extract_click(self, soup):
        # self.Firstname = soup.find('div', class_="row no-margin")[2].find('table', cellspacing="0").find('tbody').find_all('tr')[num].find('a').get(
        #     'href')
        self.name = soup.find_all('div',id='full_name')
        # self.address = soup.find()
        return self.name

    def close_brower(self):
        # 关闭浏览器
        self.driver.quit()




if __name__ == '__main__':
    scraper = WebScraper()
    scraper.load_page('https://www.addressgenerator.net/')
    page_source = scraper.get_page_source()
    soup = scraper.parse_html(page_source)

    data = scraper.extract_click(soup)
    print(data)
    scraper.close_brower()