# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/9/23 10:19
# <AUTHOR> shaocanfan
# @File    : 库迪.py
# @explain : 

import requests

url = 'https://md-h5-gateway.shuxinyc.com/marketing/minip/activity/join/password'
sign = 1

header = {
    'referer': 'https://servicewechat.com/wx2804355dbf8d15c3/786/page-frame.html',
    'CSESSION':'1728194058|m5jboS0jfxoMClw6.y6UFGor9IXPPc4l1Mm/26KxeASYOzLIfnpwS8QX+ikasQru0wiKnax/Vq01X6yuG2oibNE6rBRG4ruxCKGcWzg==.2746a1345d7993ae',
    'Host': 'md-h5-gateway.shuxinyc.com',
    'User-Agent': 'Mozilla/5.0 (Linux; Android 12; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.62 Mobile Safari/537.36',
    'Content-Type': 'application/json',
    'cache-control': 'no-cache',
    'versionCode': '33573',



}

data = {
    'activetype': 4,
    'businessId': '6DatjQMnzNHF',
    'activityJoinSource': 0,
    'password': 'C',
    'shopId': -1,
    'id': '',
}

respone = requests.post(url=url, headers=header, data=data,timeout=10)
print(respone.text)
