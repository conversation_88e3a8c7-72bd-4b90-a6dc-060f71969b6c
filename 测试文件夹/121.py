# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/8/24 0:52
# <AUTHOR> shaocanfan
# @File    : 121.py
# @explain :

# **********************************************************************************************************************

# import smtplib
# from email.mime.text import MIMEText
# from email.mime.multipart import MIMEMultipart
#
# # 邮件发送和接收地址
# sender_email = "<EMAIL>"
# receiver_email = "<EMAIL>"
# password = "yopbtzkeqofthbdh"  # 使用生成的授权码
#
# # 创建邮件对象
# msg = MIMEMultipart()
# msg['From'] = sender_email
# msg['To'] = receiver_email
# msg['Subject'] = 'Hello from Python'
#
# # 邮件正文内容
# body = "This is a test email sent from Python script. Hello World!"
# msg.attach(MIMEText(body, 'plain'))
#
# # 连接到 SMTP 服务器
# server = smtplib.SMTP_SSL("smtp.qq.com", 465)  # 使用 SSL
# server.login(sender_email, password)
# server.sendmail(sender_email, receiver_email, msg.as_string())
# server.quit()
#
# print("邮件已发送!")

# **********************************************************************************************************************

# import os
# import smtplib
# import datetime
# from email import encoders
# from email.mime.base import MIMEBase
# from email.mime.text import MIMEText
# from email.mime.multipart import MIMEMultipart
#
# send_data = datetime.datetime.now()
# formatted_date = send_data.strftime("%Y-%m-%d %H:%M:%S")
#
# sender_email = '<EMAIL>'
# sender_key = '5109SmWYMHt4iwBf'
# receiver_email = '<EMAIL>'
# subject_topic = rf'{formatted_date}时发送的新闻'
# context_body = 'Hello, 以下为您汇报ai生成的新闻~'
# filename_path = 'output_news.txt'
#
# def send_email(sender_email, receiver_email, password, subject, body,filename):
#     msg = MIMEMultipart()
#     msg['From'] = sender_email
#     msg['To'] = receiver_email
#     msg['Subject'] = subject
#
#     # 邮件正文
#     msg.attach(MIMEText(body, 'plain'))
#
#     # 文件路径
#     filepath = os.path.join(os.getcwd(), filename)
#
#     # 检查文件是否存在
#     if not os.path.isfile(filepath):
#         print(f"Error: The file output_news.txt does not exist.")
#         return
#
#     # 附件
#     with open(filepath, "rb") as attachment:
#         part = MIMEBase('application', 'octet-stream')
#         part.set_payload(attachment.read())
#         encoders.encode_base64(part)
#         part.add_header('Content-Disposition', f"attachment; filename= {os.path.basename(filename)}")
#         msg.attach(part)
#
#
#     # 每次发送都重新建立连接
#     try:
#         server = smtplib.SMTP_SSL('smtp.mxhichina.com', 465)
#         server.login(sender_email, password)
#         server.sendmail(sender_email, receiver_email, msg.as_string())
#         server.quit()
#         print("邮件已成功发送！")
#     except smtplib.SMTPException as e:
#         print(f"邮件发送失败：{e}")
#
#
# # 调用发送函数
# send_email(sender_email, receiver_email, sender_key, subject_topic,context_body,filename_path)


import requests
import json
url = 'https://api.qqsuu.cn/api/dm-xjj2?type=json'

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
    'Content-Type': 'application/json'
}

response = requests.get(url, headers=headers)
context = response.text
json_data = json.loads(context)

print(json_data)

