## -*- coding: utf-8 -*-

# proxy_url = "https://api.linux-do-proxy.com/get/"
# ---------------------------------------------------------------------------------------------
import time
import requests
from bs4 import BeautifulSoup
import xlwt

def main():
    url = 'https://ssr1.scrape.center/'
    datalist = getData(url)
    savepath = './电影.xls'
    Save_data(datalist,savepath)

def getData(basurl):
    dataList = []
    for i in range(1,3):
        url = basurl + f'page/{i}'
        html = askUrl(url)

        soup = BeautifulSoup(html,"html.parser")
        for item in soup.find_all('div', class_="el-card item m-t is-hover-shadow"):
            data = []

            Name = item.find('a',class_="name").get_text().strip('\n')
            data.append(Name)

            Time = item.find('div',class_="m-v-sm info").find_all('span')[2].get_text()
            data.append(Time)

            dataList.append(data)
        time.sleep(1)
    # print(dataList)
    return dataList

def askUrl(Url):
    html = ''
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Connection': 'keep-alive'
    }

    # proxy = get_proxy_pool()
    # proxies = {
    #     'http': fr"http://{proxy}",
    #     # 'https': fr"http://{proxy}"
    # }

    # response = requests.get(Url, headers=headers,proxies=proxies)
    response = requests.get(Url, headers=headers)
    if response.status_code == 200:
        html = response.content.decode()
    else:
        print(f"Failed to askUrl: {response.status_code}")

    return html


# def get_proxy_pool():  # 通过api获取ip
#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#         'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
#         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
#         'Connection': 'keep-alive'
#     }
#     response = requests.get('https://api-china.linux-do-proxy.com:20961/get/',headers=headers)
#     if response.status_code == 200:
#         ip_pool = response.json()['proxy']  # 提取ip
#         print(response.json()['region'] +" : "+response.json()['proxy'])
#         return ip_pool
#     else:
#         print(f"Failed to retrieve IP pool: {response.status_code}")


# def validate_proxy(proxy):  # 验证ip是否有效
#     test_url = 'http://httpbin.org/ip'
#     proxies = {
#         'http': proxy,
#         'https': proxy
#     }
#     print(f"Testing proxy: {proxy}")  # 调试信息：打印传入的代理
#     try:
#         response = requests.get(test_url,proxies=proxies,timeout=5)
#         if response.status_code == 200:
#             print(f"Valid proxy:{proxy}")
#             return True
#         else:
#             print(f"Invalid proxy:{proxy}")
#             return False
#     except requests.RequestException as e:
#         print(f"proxy validation error:{e}")
#         return False



def count_sublists(lst):
    count = 0
    for item in lst:
        if isinstance(item, list):
            count += 1
    return count

def Save_data(DataList,SavePath):
    book = xlwt.Workbook(encoding='utf-8')
    sheet = book.add_sheet("电影爬虫",cell_overwrite_ok=True)
    col = ('movice_name','movice_time')
    for i in range(0,2):
        sheet.write(0,i,col[i])
    for i in range(0,count_sublists(DataList)):
        print("第{0}".format(i+1))
        data = DataList[i]
        for j in range(0,2):
            sheet.write(i+1,j,data[j])
    book.save(SavePath)

if __name__ == '__main__':
    main()
    # proxy = get_proxy_pool()
    # validate_proxy(proxy)
    print("任务完成")

# ---------------------------------------------------------------------------------------------

# import time
# import requests
#
# def get_proxy_pool():  # 通过api获取ip
#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#         'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
#         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
#         'Connection': 'keep-alive'
#     }
#     response = requests.get('https://api-china.linux-do-proxy.com:20961/get/',headers=headers)
#     if response.status_code == 200:
#         ip_pool = response.json()['proxy']  # 提取ip
#         print(response.json()['region'])
#         print(response.json())
#         return ip_pool
#     else:
#         print(f"Failed to retrieve IP pool: {response.status_code}")
#
# def main():
#     proxy = get_proxy_pool()
#     # 定义代理IP
#     proxies = {
#         'http': 'http://'+proxy,
#         'https': 'https://'+proxy,
#     }
#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#         'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
#         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
#         'Connection': 'keep-alive'
#     }
#     # 目标URL
#     url = 'http://httpbin.org/ip'
#
#     try:
#         response = requests.get(url,headers=headers, proxies=proxies, timeout=5)  # 增加超时设置
#         response.raise_for_status()  # 检查请求是否成功
#         print(response.text)
#     except requests.exceptions.ProxyError as e:
#         print(f"代理错误: {e}")
#     except requests.exceptions.RequestException as e:
#         print(f"请求失败: {e}")
#     except Exception as e:
#         print(f"其他错误: {e}")
#
# def test1():
#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#         'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
#         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
#         'Connection': 'keep-alive'
#     }
#     response = requests.get('https://api-china.linux-do-proxy.com:20961/get/',headers=headers)
#     if response.status_code == 200:
#         ip_pool = response.json() # 提取ip
#         if ip_pool['https'] == True:
#             print(ip_pool)
#             return ip_pool
#     # else:
#     #     print(f"Failed to retrieve IP pool: {response.status_code}")
#
# if __name__ == '__main__':
#
#     main()
#     # get_proxy_pool()
#     print("finish")
