<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>邮箱管理系统</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <!-- 登录/注册区 -->
  <div id="authPanel" style="display:none;">
    <h2>登录邮箱系统</h2>
    <input id="loginUsername" placeholder="用户名" autocomplete="username">
    <input id="loginPassword" type="password" placeholder="密码" autocomplete="current-password">
    <br>
    <button onclick="login()">登录</button>
    <div style="margin-top:18px;font-size:14px;">
      <span style="color:#888;">还没有账号？</span>
      <span id="toReg" style="cursor:pointer;color:#6a4de2;font-weight:500;" onclick="showRegister()">注册新账号</span>
    </div>
  </div>
  <div id="registerPanel" style="display:none;">
    <h2>注册新账号</h2>
    <input id="regUsername" placeholder="用户名" autocomplete="username">
    <input id="regPassword" type="password" placeholder="密码" autocomplete="new-password">
    <br>
    <button onclick="register()">注册</button>
    <div style="margin-top:18px;font-size:14px;">
      <span style="color:#888;">已有账号？</span>
      <span id="toLogin" style="cursor:pointer;color:#6a4de2;font-weight:500;" onclick="showLogin()">返回登录</span>
    </div>
  </div>

  <!-- 退出按钮 -->
  <button id="logoutBtn" style="display:none;" onclick="logout()">退出登录</button>

  <!-- 主界面 -->
  <div class="container" style="display:none;">
    <!-- 左侧：邮箱列表 -->
    <div class="panel left-panel">
      <div class="panel-header">
        <h3>我的邮箱</h3>
        <button id="refreshEmails" onclick="fetchEmails()">🔄</button>
      </div>
      <div class="create-email">
        <input type="text" id="prefix" placeholder="邮箱前缀">
        <select id="domainSelect"></select>
        <button id="createEmailBtn" onclick="createEmail()">+ 创建新邮箱</button>
      </div>
      <div id="createResult" style="margin-top: 8px; font-size: 14px; color: #666;"></div>
      <div class="email-list" id="emailList"></div>
    </div>
    <!-- 中间：邮件列表 -->
    <div class="panel middle-panel">
      <div class="mailbox-top">
        <div class="mailbox-address">
          <span id="mailboxAddr"></span>
<!--          <button id="copyMailboxBtn" title="复制邮箱">📋</button>-->
        </div>
        <button class="send-mail-btn" id="showComposeBtn">
          <span>✈️ 发送邮件</span>
        </button>
      </div>
      <div class="mailbox-tabs">
        <button id="inboxTab" class="tab-btn active"><span>📥 收件箱</span></button>
        <button id="sentTab" class="tab-btn"><span>📤 已发送</span></button>
      </div>
      <div id="mailList" class="mailbox-list"></div>
    </div>

    <!-- 发信弹窗区域，复用前文提供的 -->
    <div class="modal-bg" id="composeModal" style="display:none;">
      <div class="modal">
        <div class="modal-header">
          <span>发邮件</span>
          <span class="close-modal" onclick="closeComposeModal()">✕</span>
        </div>
        <div class="modal-body">
          <div>
            <label>发件邮箱</label>
            <input id="fromEmailInput" disabled>
          </div>
          <div>
            <label>收件人</label>
            <input id="toEmailInput" placeholder="收件人邮箱">
          </div>
          <div>
            <label>主题</label>
            <input id="subjectInput" placeholder="主题">
          </div>
          <div>
            <label>内容</label>
            <textarea id="contentInput" rows="7" placeholder="邮件内容"></textarea>
          </div>
          <div id="sendMailResult" style="color:#a33;margin:7px 0 0 0;"></div>
          <button id="sendBtn" onclick="sendMail()">发送</button>
        </div>
      </div>
    </div>

    <!-- 右侧：邮件详情 -->
    <div class="panel right-panel">
      <h3>选择邮件查看详情</h3>
      <div class="mail-content" id="mailContent"></div>
    </div>
  </div>
  <script src="scripts.js"></script>
</body>
</html>
