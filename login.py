# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'login.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(499, 199)
        Form.setMinimumSize(QtCore.QSize(499, 199))
        Form.setMaximumSize(QtCore.QSize(499, 199))
        self.login_Button = QtWidgets.QPushButton(Form)
        self.login_Button.setGeometry(QtCore.QRect(40, 160, 93, 28))
        self.login_Button.setObjectName("login_Button")
        self.cancel_Button = QtWidgets.QPushButton(Form)
        self.cancel_Button.setGeometry(QtCore.QRect(160, 160, 93, 28))
        self.cancel_Button.setObjectName("cancel_Button")
        self.pwd_label = QtWidgets.QLabel(Form)
        self.pwd_label.setGeometry(QtCore.QRect(20, 80, 72, 15))
        self.pwd_label.setObjectName("pwd_label")
        self.user_label = QtWidgets.QLabel(Form)
        self.user_label.setGeometry(QtCore.QRect(20, 40, 72, 15))
        self.user_label.setObjectName("user_label")
        self.user_lineEdit = QtWidgets.QLineEdit(Form)
        self.user_lineEdit.setGeometry(QtCore.QRect(110, 40, 113, 21))
        self.user_lineEdit.setObjectName("user_lineEdit")

        self.pwd_lineEdit = QtWidgets.QLineEdit(Form)
        self.pwd_lineEdit.setGeometry(QtCore.QRect(110, 80, 113, 71))  # 密码输入框71本来是21
        self.pwd_lineEdit.setObjectName("pwd_lineEdit")

        # 使用QPlainTextEdit代替QLineEdit
        # self.pwd_lineEdit = QtWidgets.QPlainTextEdit(Form)
        # self.pwd_lineEdit.setGeometry(QtCore.QRect(110, 80, 113, 71))
        # self.pwd_lineEdit.setObjectName("pwd_plainTextEdit")
        # self.pwd_lineEdit.setLineWrapMode(QtWidgets.QPlainTextEdit.WidgetWidth)  # 设置自动换行

        self.user_textBrowser = QtWidgets.QTextBrowser(Form)
        self.user_textBrowser.setGeometry(QtCore.QRect(240, 30, 231, 111))
        self.user_textBrowser.setObjectName("user_textBrowser")

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.login_Button.setText(_translate("Form", "查询"))
        self.cancel_Button.setText(_translate("Form", "退出"))
        self.pwd_label.setText(_translate("Form", "问题"))
        self.user_label.setText(_translate("Form", "用户名"))


