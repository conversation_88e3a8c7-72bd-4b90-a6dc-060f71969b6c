#------------------------------------------------------------------------------------------------------
# import time
# from selenium import webdriver
# from selenium.webdriver.common.keys import Keys
# from selenium.webdriver.common.by import By
# import pyaudio
# import os
# from pocketsphinx import LiveSpeech, get_model_path
#
# # 配置音频流
# CHUNK = 1024
# FORMAT = pyaudio.paInt16
# CHANNELS = 1
# RATE = 16000
# p = pyaudio.PyAudio()
# stream = p.open(format=FORMAT,
#                 channels=CHANNELS,
#                 rate=RATE,
#                 input=True,
#                 frames_per_buffer=CHUNK)
#
# # 配置Selenium
# driver = webdriver.Edge()
#
# # 打开抖音登录页面
# driver.get("https://live.douyin.com/223792018128")
# time.sleep(20)  # 等待页面加载
#
# # 模拟登录
# def login_douyin():
#     # 请根据实际情况填写登录信息
#     username = "15627201430"  # 替换为实际的用户名
#     password = "Zyy1509418342"  # 替换为实际的密码
#
#     # 找到并点击登录按钮（假设使用手机号登录）
#     driver.find_element(By.XPATH,'//*[@id="Cn2CzO_Q"]/div').click()
#     time.sleep(10)  # 等待登录页面加载
#
#     # 输入手机号（假设使用手机号登录）
#     driver.find_element(By.XPATH,'//*[@id="web-login-container"]/article/article/article/div[1]/ul[1]/li[3]').click()
#     driver.find_element(By.XPATH,'//*[@id="web-login-container"]/article/article/article/form/div[1]/div/input').send_keys(username)
#     time.sleep(2)
#     driver.find_element(By.XPATH,'//*[@id="web-login-container"]/article/article/article/form/div[2]/div/div/input').send_keys(password)
#     time.sleep(2)
#
#     driver.find_element(By.XPATH,'//*[@id="web-login-container"]/article/article/article/form/div[5]/button').click()
#     time.sleep(5)  # 等待登录完成
#
# login_douyin()
#
# # 打开抖音直播页面
# driver.get("https://www.douyin.com/live/123456")  # 替换为实际直播URL
# time.sleep(5)  # 等待页面加载
#
# comment_box = driver.find_element_by_css_selector("textarea")  # 替换为实际的评论区选择器
#
# # 配置PocketSphinx
# model_path = get_model_path()
# speech = LiveSpeech(
#     audio_device=None,
#     sampling_rate=RATE,
#     buffer_size=2048,
#     no_search=False,
#     full_utt=False,
#     hmm=os.path.join(model_path, 'en-us'),
#     lm=os.path.join(model_path, 'en-us.lm.bin'),
#     dic=os.path.join(model_path, 'cmudict-en-us.dict')
# )
#
# def recognize_and_comment():
#     for phrase in speech:
#         comment = str(phrase).strip()
#         comment_box.send_keys(comment)
#         comment_box.send_keys(Keys.RETURN)
#
# # 持续运行识别和评论
# try:
#     while True:
#         recognize_and_comment()
#         time.sleep(5)  # 可根据需要调整间隔时间
# except KeyboardInterrupt:
#     pass
#
# stream.stop_stream()
# stream.close()
# p.terminate()
# driver.quit()

#------------------------------------------------------------------------------------------------------
#获取登录页面的cookie值，将cookies值存放到cookies.pkl文件里面
# from selenium import webdriver
# import pickle
# import time
#
# driver = webdriver.Edge()
#
# # 访问任意页面以初始化会话
# driver.get("https://www.douyin.com")
# time.sleep(10)
# # 加载cookie
# with open("cookies.pkl", "rb") as cookiesfile:
#     cookies = pickle.load(cookiesfile)
#     for cookie in cookies:
#         try:
#             driver.add_cookie(cookie)
#         except Exception as e:
#             print(f"Error adding cookie: {e}")
# # 重新访问主页或任何需要登录的页面
# driver.get("https://www.douyin.com")
# time.sleep(5)  # 适当的等待时间
#
# # 你的代码操作...
# print(driver.title)  # 确认登录成功
#
# driver.quit()

#------------------------------------------------------------------------------------------------------

import time
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
import pyaudio
import wave
from aip import AipSpeech
import pickle
from selenium.webdriver.common.by import By

# 百度语音识别配置
APP_ID = '73921835'
API_KEY = 'wgaQQSZINWjjceEmPFeNB7bx'
SECRET_KEY = '6OO3I7CCDuQMu7QUnNTdk7P2kRdU6vMt'
client = AipSpeech(APP_ID, API_KEY, SECRET_KEY)

# 配置音频流
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000
RECORD_SECONDS = 5
p = pyaudio.PyAudio()
stream = p.open(format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK)

# 配置Selenium
driver = webdriver.Edge()

# 打开抖音登录页面
driver.get("https://live.douyin.com/163823390463")
time.sleep(10)  # 等待页面加载

# 模拟登录
def login_douyin():
    with open("cookies.pkl", "rb") as cookiesfile:
        cookies = pickle.load(cookiesfile)
        for cookie in cookies:
            try:
                driver.add_cookie(cookie)
            except Exception as e:
                print(f"Error adding cookie: {e}")

login_douyin()

# 打开抖音直播页面
driver.get("https://live.douyin.com/163823390463")  # 替换为实际直播URL
time.sleep(5)  # 等待页面加载

comment_box = driver.find_element(By.XPATH,'//*[@id="chat-textarea"]')  # 替换为实际的评论区选择器
time.sleep(5)  # 等待页面加载
# comment_box.send_keys('abcdefg')
# time.sleep(5)  # 等待页面加载
# 录制音频并进行语音识别
def recognize_and_comment():
    frames = []
    for _ in range(0, int(RATE / CHUNK * RECORD_SECONDS)):
        data = stream.read(CHUNK)
        frames.append(data)

    wf = wave.open('output.wav', 'wb')
    wf.setnchannels(CHANNELS)
    wf.setsampwidth(p.get_sample_size(FORMAT))
    wf.setframerate(RATE)
    wf.writeframes(b''.join(frames))
    wf.close()

    with open('output.wav', 'rb') as fp:
        audio_data = fp.read()

    result = client.asr(audio_data, 'wav', 16000, {'dev_pid': 1537})  # 1537是普通话 PID
    if result['err_no'] == 0:
        comment = result['result'][0]
        print(comment)
        comment_box.send_keys(comment)
        comment_box.send_keys(Keys.RETURN)
    else:
        print('fault')

# 持续运行识别和评论
try:
    while True:
        recognize_and_comment()
        time.sleep(5)  # 可根据需要调整间隔时间
except KeyboardInterrupt:
    pass

stream.stop_stream()
stream.close()
p.terminate()
driver.quit()

#------------------------------------------------------------------------------------------------------
# deepseek注册机
from DrissionPage import ChromiumPage
import random
import string

def generate_password(length=12):
    characters = string.ascii_letters + string.digits + string.punctuation
    password = ''.join(random.choices(characters, k=length))
    return password

with open('output.txt', 'w') as file:
    while True:
        try:
            password = generate_password()

            page = ChromiumPage()

            mailTab = page.new_tab(new_context=True)
            mailTab.get(url='https://ihotmails.com')
            page.wait(3)

            mailValue = mailTab.ele('css:#shortid').value

            deepseekTab = page.new_tab(new_context=True)
            deepseekTab.get(url='https://platform.deepseek.com/sign_up')
            page.wait(6)

            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div:nth-child(3) > div.ds-form-item__content > div > input').input(mailValue)
            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div:nth-child(4) > div.ds-form-item__content > div > input').input(password)
            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div:nth-child(5) > div.ds-form-item__content > div > input').input(password)
            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div:nth-child(6) > div.ds-form-item__content > div > div.ds-button.ds-button--secondary.ds-button--bordered.ds-button--rect.ds-button--l.dsds-verify-code-form-item__verify-code-button').click()

            page.wait(3)

            mailTab.ele('css:#maillist > tr').click()
            codeVal = mailTab.ele('css:#mailcard > div:nth-child(2) > div > div:nth-child(6) > table > tbody > tr > td > div > table > tbody > tr > td > table > tbody > tr > td > div').text

            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div:nth-child(6) > div.ds-form-item__content > div > div.ds-input.ds-input--none.ds-input--bordered.ds-input--l.ds-verify-code-form-item__verify-code-input > input').input(codeVal)
            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div:nth-child(7) > div.ds-form-item__content > div').click()
            deepseekTab.ele('css:#root > div > div > div._1e91608 > div > div.ds-button.ds-button--primary.ds-button--filled.ds-button--rect.ds-button--block.ds-button--l.ds-sign-up-form__register-button').click()

            page.wait(3)

            deepseekTab.ele('css:#root > div > div > div > aside > div > div.ds-flex > div.ds-menu > div:nth-child(2) > a').click()

            page.wait(3)

            deepseekTab.ele('css:#root > div > div > div > main > div > div.ds-flex > div.ds-button.ds-button--primary.ds-button--filled.ds-button--rect.ds-button--m').click()

            page.wait(2)

            deepseekTab.ele('css:body > div.ds-theme.ds-modal-wrapper > div.ds-modal > div:nth-child(2) > div > div:nth-child(2) > div > div > div > input').input('testAPI')

            deepseekTab.ele('css:body > div.ds-theme.ds-modal-wrapper > div.ds-modal > div:nth-child(2) > div > div.ds-modal-content__footer > div > div.ds-button.ds-button--primary.ds-button--filled.ds-button--rect.ds-button--m').click()

            page.wait(3)

            keyValue = deepseekTab.ele('css:body > div.ds-theme.ds-modal-wrapper > div.ds-modal > div:nth-child(2) > div > div:nth-child(2) > div > div > div > input').value

            mailTab.close()
            deepseekTab.close()

            print("u:", mailValue, file=file)
            print("p:", password, file=file)
            print("k:", keyValue, file=file)

            # 关闭浏览器
            page.close()
        except Exception as e:
            print(f"An error occurred in run: {e}")
            mailTab.close()
            deepseekTab.close()
            page.close()
            continue
