<html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="Content-type" content="text/html; charset=utf-8">
<title>热门新闻每日排行_新闻中心_新浪网</title>
<meta name="keywords" content="新闻排行榜,新闻浏览量">
<meta name="description" content="新闻中心排行，实时新闻排行榜，显示从当前时间起24小时内各频道新闻浏览量最高的排行情况，每小时更新1次。">
<!-- 修改substr匹配汉字截取(测试) -->
<script type="text/javascript">
String.prototype.substr2=function(a,b){a=this.getStartPst(a);var c=this.substr(a,b);var d=c.replace(/[^\x00-\xff]/g,"aa").length;var e=d-b;var f;var g=b;var i=0;while(e>0){c=this.substr(a,g);d=c.replace(/[^\x00-\xff]/g,"aa").length;e=d-b;if(e<=0){break}f=Math.floor(e/2);if(f>1){g=b-f}else{i++;g=b-i}}return this.substr(a,g)};String.prototype.getStartPst=function(a){var b=this.substr(0,a);var c=b.replace(/[^\x00-\xff]/g,"aa").length;var d=c-a;var e;var f=a;var i=0;while(d>0){b=this.substr(0,f);c=b.replace(/[^\x00-\xff]/g,"aa").length;d=c-a;if(d<=0){break}e=Math.floor(d/2);if(e>1){f=a-e}else{i++;f=a-i}}return f};
</script>
<!-- 080215 ws end -->
<style type="text/css">
<!--
body,ul,ol,li,p,h1,h2,h3,h4,h5,h6,form,fieldset,table,td,img,div{margin:0;padding:0;border:0;}
body{background:#fff;color:#000; margin-top:5px;}
td,p,li,select,input,textarea,div{font-size:12px;}

ul{list-style-type:none;}
select,input{vertical-align:middle;}

a:link{color:#009;text-decoration:none;}
a:visited{color:#800080;text-decoration:none;}
a:hover,a:active,a:focus{color:#f00;text-decoration:underline;}

.clearit{clear:both;}

#page{width:950px; margin:0 auto;}

/* 二级导航 */
#level2header {border-top:1px solid #000;border-bottom:1px solid #000;width:950px;height:34px;font-size:12px; background:#fff;}
#level2header img{border:none;}
#level2header #logo {float:left;text-align:left; padding-top:3px;}
#level2header #l2navlnk {float:right;text-align:center;line-height:34px;}
#level2header #l2navlnk a:link,#level2header #l2navlnk a:visited {color:#000;text-decoration:none;}
#level2header #l2navlnk a:hover,#level2header #l2navlnk a:active {color:#f00;text-decoration:underline;}
#level2header #iask {float:right;width:307px;height:34px;background-color:#FFF;text-align:left;padding-left:6px; color:#000;}
#level2header #iask a:link,#level2header #iask a:visited {color:#f00;text-decoration:none;}
#level2header #iask a:hover,#level2header #iask a:active {color:#f00;text-decoration:underline;}
#level2header #iask #k {width:65px;border:1px solid #7F9DB9;margin:0 0 0 7px;}
#level2header #iask .sebtn{width:45px; height:18px;}
#level2header .iaskRedTxt{color:#aaa; border:1px #f00 solid;}
#level2headerborder{background:#fff; height:5px; overflow:hidden; clear:both; width:950px;}

/* banner */
#banner{width:950px; height:90px;  background:url(https://www.sinaimg.cn/dy/2012/1030/U8360P1DT20121030140701.jpg) no-repeat; margin:2px 0 0 0; position:relative;}
#wrhg{width:140px; position:absolute; top:52px; right:5px;}
#date{position:absolute; top:31px; left:209px; font-weight:bold; color:#fff;}

/* 快速导航 */
#quickNav{ height:34px; line-height:34px; overflow:hidden; background:url(https://www.sinaimg.cn/dy/20091231/topbg.jpg) repeat-x; color:#999;border:1px solid #cecece;}
#quickNav a,#quickNav a:visited{color:#000; text-decoration:none;}
#quickNav a:hover,#quickNav a:active{color:#c00; text-decoration:underline;}
#qN1{float:left; padding:0 0 0 20px;}
#qN2{float:right; padding:0 8px 0 0;}

/* 排行表格 */
.loopblk{margin-top:10px;background:url(https://www.sinaimg.cn/dy/20091231/nt_cl_03.png) no-repeat 0 100%;padding-bottom:6px; }
.lbti{height:40px; overflow:hidden; background:url(https://www.sinaimg.cn/dy/20091231/nt_cl_01.png);}
.lbti h2{float:left; width:88px; overflow:hidden; line-height:40px; font-size:14px; color:#900; padding:0 0 0 15px;font-weight:normal;}
.Tabs{float:left;}

.Tabs li{float:left; text-align:center; font-size:12px; color:#900; cursor:pointer; background:url(https://www.sinaimg.cn/dy/deco/2012/0712/nt_cl_04.png) no-repeat; line-height:34px;width:85px;height:34px; margin:6px 0 0 5px; color:#000;}
.Tabs li.on{background:url(https://www.sinaimg.cn/dy/deco/2012/0712/nt_cl_04.png) 0 -100px no-repeat;color:#7c0a0a;}

/*
.Tabs li{float:left; text-align:center; font-size:12px;cursor:pointer; background:url(https://i0.sinaimg.cn/dy/20091231/nt_cl_04.png) 0 -100px no-repeat; line-height:34px;width:85px;height:34px; margin:6px 0 0 5px; color:#7c0a0a;}
.Tabs li.on{background:url(https://i0.sinaimg.cn/dy/20091231/nt_cl_04.png) 0 0 no-repeat;color:#fff;}
*/
.Cons{background:url(https://www.sinaimg.cn/dy/20091231/nt_cl_02.png) repeat-y;padding:10px 8px; }
.Cons table{width:918px; border-bottom:1px #efefef solid; border-left:1px #efefef solid;margin:0 auto;}
.Cons th{text-align:center; line-height:26px; padding-top:2px; color:#494949; background:#fff; font-weight:normal;border:1px solid #efefef;border-width:1px 1px 0 0;}
.Cons td{text-align:center; line-height:21px; padding:2px 0 0 0; border-right:1px #efefef solid; border-top:1px #efefef solid; color:#333;}
.Cons td.ConsTi{text-align:left; padding:2px 0 0 6px;font-size:14px;line-height:28px; }
.caplink{float:right; padding:0 10px 0 0; color:#900; line-height:40px;}
.caplink a,.caplink a:visited{color:#900; text-decoration:none;}
.caplink a:hover,.caplink a:active{color:#f00; text-decoration:underline;}

#shm{border:1px #d3d3d3 solid; line-height:27px; padding-top:2px; padding-left:48px;text-align:left; margin:10px 0 0 0;}

.videoNewsLeft {background: url("https://i1.sinaimg.cn/dy/deco/2012/0619/videoicon.png") no-repeat scroll 0 50% transparent;padding-left: 19px;}
	
	
#Tab53{display:none;}
#Tab63{display:none;}
#Tab73{display:none;}
#Tab83{display:none;}
#Tab93{display:none;}
	
	
-->
</style>

<script type="text/javascript">
function GetObj(objName){
	if(document.getElementById){
		return eval('document.getElementById("' + objName + '")');
	}else if(document.layers){
		return eval("document.layers['" + objName +"']");
	}else{
		return eval('document.all.' + objName);
	}
}

//舌签
/*
function chgTab(id1,id2){
	for(var i=1;i<=2;i++){
		GetObj("Tab"+id1+i).className = "";
		GetObj("Con"+id1+i).style.display = "none";
	}
	GetObj("Tab"+id1+id2).className = "on";
	GetObj("Con"+id1+id2).style.display = "block";
}
*/
function chgTab(id1,id2){
	for(var i=1;i<=5;i++){
		if(!GetObj("Tab"+id1+i)){continue}
		GetObj("Tab"+id1+i).className = "";
		GetObj("Con"+id1+i).style.display = "none";
	}
	GetObj("Tab"+id1+id2).className = "on";
	GetObj("Con"+id1+id2).style.display = "block";
}
</script>

<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7">

<script type="text/javascript" src="//i.sso.sina.com.cn/js/genvisitor.js"></script>


<meta name="sudameta" content="dataid:wpcomos:86507"><iframe src="https://sbeacon.sina.com.cn/ckctl.html" id="ckctlFrame" scrolling="no" style="height: 0px; width: 1px; overflow: hidden;"></iframe></head>
<body id="body"><!-- body code begin -->

<!-- SUDA_CODE_START --> 
<script type="text/javascript"> 
//<!--
(function(){var an="V=2.1.16";var ah=window,F=document,s=navigator,W=s.userAgent,ao=ah.screen,j=ah.location.href;var aD="https:"==ah.location.protocol?"https://s":"http://",ay="beacon.sina.com.cn";var N=aD+ay+"/a.gif?",z=aD+ay+"/g.gif?",R=aD+ay+"/f.gif?",ag=aD+ay+"/e.gif?",aB=aD+"beacon.sinauda.com/i.gif?";var aA=F.referrer.toLowerCase();var aa="SINAGLOBAL",Y="FSINAGLOBAL",H="Apache",P="ULV",l="SUP",aE="UOR",E="_s_acc",X="_s_tentry",n=false,az=false,B=(document.domain=="sina.com.cn")?true:false;var o=0;var aG=false,A=false;var al="";var m=16777215,Z=0,C,K=0;var r="",b="",a="";var M=[],S=[],I=[];var u=0;var v=0;var p="";var am=false;var w=false;function O(){var e=document.createElement("iframe");e.src=aD+ay+"/data.html?"+new Date().getTime();e.id="sudaDataFrame";e.style.height="0px";e.style.width="1px";e.style.overflow="hidden";e.frameborder="0";e.scrolling="no";document.getElementsByTagName("head")[0].appendChild(e)}function k(){var e=document.createElement("iframe");e.src=aD+ay+"/ckctl.html";e.id="ckctlFrame";e.style.height="0px";e.style.width="1px";e.style.overflow="hidden";e.frameborder="0";e.scrolling="no";document.getElementsByTagName("head")[0].appendChild(e)}function q(){var e=document.createElement("script");e.src=aD+ay+"/h.js";document.getElementsByTagName("head")[0].appendChild(e)}function h(aH,i){var D=F.getElementsByName(aH);var e=(i>0)?i:0;return(D.length>e)?D[e].content:""}function aF(){var aJ=F.getElementsByName("sudameta");var aR=[];for(var aO=0;aO<aJ.length;aO++){var aK=aJ[aO].content;if(aK){if(aK.indexOf(";")!=-1){var D=aK.split(";");for(var aH=0;aH<D.length;aH++){var aP=aw(D[aH]);if(!aP){continue}aR.push(aP)}}else{aR.push(aK)}}}var aM=F.getElementsByTagName("meta");for(var aO=0,aI=aM.length;aO<aI;aO++){var aN=aM[aO];if(aN.name=="tags"){aR.push("content_tags:"+encodeURI(aN.content))}}var aL=t("vjuids");aR.push("vjuids:"+aL);var e="";var aQ=j.indexOf("#");if(aQ!=-1){e=escape(j.substr(aQ+1));aR.push("hashtag:"+e)}return aR}function V(aK,D,aI,aH){if(aK==""){return""}aH=(aH=="")?"=":aH;D+=aH;var aJ=aK.indexOf(D);if(aJ<0){return""}aJ+=D.length;var i=aK.indexOf(aI,aJ);if(i<aJ){i=aK.length}return aK.substring(aJ,i)}function t(e){if(undefined==e||""==e){return""}return V(F.cookie,e,";","")}function at(aI,e,i,aH){if(e!=null){if((undefined==aH)||(null==aH)){aH="sina.com.cn"}if((undefined==i)||(null==i)||(""==i)){F.cookie=aI+"="+e+";domain="+aH+";path=/"}else{var D=new Date();var aJ=D.getTime();aJ=aJ+86400000*i;D.setTime(aJ);aJ=D.getTime();F.cookie=aI+"="+e+";domain="+aH+";expires="+D.toUTCString()+";path=/"}}}function f(D){try{var i=document.getElementById("sudaDataFrame").contentWindow.storage;return i.get(D)}catch(aH){return false}}function ar(D,aH){try{var i=document.getElementById("sudaDataFrame").contentWindow.storage;i.set(D,aH);return true}catch(aI){return false}}function L(){var aJ=15;var D=window.SUDA.etag;if(!B){return"-"}if(u==0){O();q()}if(D&&D!=undefined){w=true}ls_gid=f(aa);if(ls_gid===false||w==false){return false}else{am=true}if(ls_gid&&ls_gid.length>aJ){at(aa,ls_gid,3650);n=true;return ls_gid}else{if(D&&D.length>aJ){at(aa,D,3650);az=true}var i=0,aI=500;var aH=setInterval((function(){var e=t(aa);if(w){e=D}i+=1;if(i>3){clearInterval(aH)}if(e.length>aJ){clearInterval(aH);ar(aa,e)}}),aI);return w?D:t(aa)}}function U(e,aH,D){var i=e;if(i==null){return false}aH=aH||"click";if((typeof D).toLowerCase()!="function"){return}if(i.attachEvent){i.attachEvent("on"+aH,D)}else{if(i.addEventListener){i.addEventListener(aH,D,false)}else{i["on"+aH]=D}}return true}function af(){if(window.event!=null){return window.event}else{if(window.event){return window.event}var D=arguments.callee.caller;var i;var aH=0;while(D!=null&&aH<40){i=D.arguments[0];if(i&&(i.constructor==Event||i.constructor==MouseEvent||i.constructor==KeyboardEvent)){return i}aH++;D=D.caller}return i}}function g(i){i=i||af();if(!i.target){i.target=i.srcElement;i.pageX=i.x;i.pageY=i.y}if(typeof i.layerX=="undefined"){i.layerX=i.offsetX}if(typeof i.layerY=="undefined"){i.layerY=i.offsetY}return i}function aw(aH){if(typeof aH!=="string"){throw"trim need a string as parameter"}var e=aH.length;var D=0;var i=/(\u3000|\s|\t|\u00A0)/;while(D<e){if(!i.test(aH.charAt(D))){break}D+=1}while(e>D){if(!i.test(aH.charAt(e-1))){break}e-=1}return aH.slice(D,e)}function c(e){return Object.prototype.toString.call(e)==="[object Array]"}function J(aH,aL){var aN=aw(aH).split("&");var aM={};var D=function(i){if(aL){try{return decodeURIComponent(i)}catch(aP){return i}}else{return i}};for(var aJ=0,aK=aN.length;aJ<aK;aJ++){if(aN[aJ]){var aI=aN[aJ].split("=");var e=aI[0];var aO=aI[1];if(aI.length<2){aO=e;e="$nullName"}if(!aM[e]){aM[e]=D(aO)}else{if(c(aM[e])!=true){aM[e]=[aM[e]]}aM[e].push(D(aO))}}}return aM}function ac(D,aI){for(var aH=0,e=D.length;aH<e;aH++){aI(D[aH],aH)}}function ak(i){var e=new RegExp("^http(?:s)?://([^/]+)","im");if(i.match(e)){return i.match(e)[1].toString()}else{return""}}function aj(aO){try{var aL="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var D="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=";var aQ=function(e){var aR="",aS=0;for(;aS<e.length;aS++){aR+="%"+aH(e[aS])}return decodeURIComponent(aR)};var aH=function(e){var i="0"+e.toString(16);return i.length<=2?i:i.substr(1)};var aP=function(aY,aV,aR){if(typeof(aY)=="string"){aY=aY.split("")}var aX=function(a7,a9){for(var a8=0;a8<a7.length;a8++){if(a7[a8]==a9){return a8}}return -1};var aS=[];var a6,a4,a1="";var a5,a3,a0,aZ="";if(aY.length%4!=0){}var e=/[^A-Za-z0-9\+\/\=]/g;var a2=aL.split("");if(aV=="urlsafe"){e=/[^A-Za-z0-9\-_\=]/g;a2=D.split("")}var aU=0;if(aV=="binnary"){a2=[];for(aU=0;aU<=64;aU++){a2[aU]=aU+128}}if(aV!="binnary"&&e.exec(aY.join(""))){return aR=="array"?[]:""}aU=0;do{a5=aX(a2,aY[aU++]);a3=aX(a2,aY[aU++]);a0=aX(a2,aY[aU++]);aZ=aX(a2,aY[aU++]);a6=(a5<<2)|(a3>>4);a4=((a3&15)<<4)|(a0>>2);a1=((a0&3)<<6)|aZ;aS.push(a6);if(a0!=64&&a0!=-1){aS.push(a4)}if(aZ!=64&&aZ!=-1){aS.push(a1)}a6=a4=a1="";a5=a3=a0=aZ=""}while(aU<aY.length);if(aR=="array"){return aS}var aW="",aT=0;for(;aT<aS.lenth;aT++){aW+=String.fromCharCode(aS[aT])}return aW};var aI=[];var aN=aO.substr(0,3);var aK=aO.substr(3);switch(aN){case"v01":for(var aJ=0;aJ<aK.length;aJ+=2){aI.push(parseInt(aK.substr(aJ,2),16))}return decodeURIComponent(aQ(aP(aI,"binnary","array")));break;case"v02":aI=aP(aK,"urlsafe","array");return aQ(aP(aI,"binnary","array"));break;default:return decodeURIComponent(aO)}}catch(aM){return""}}var ap={screenSize:function(){return(m&8388608==8388608)?ao.width+"x"+ao.height:""},colorDepth:function(){return(m&4194304==4194304)?ao.colorDepth:""},appCode:function(){return(m&2097152==2097152)?s.appCodeName:""},appName:function(){return(m&1048576==1048576)?((s.appName.indexOf("Microsoft Internet Explorer")>-1)?"MSIE":s.appName):""},cpu:function(){return(m&524288==524288)?(s.cpuClass||s.oscpu):""},platform:function(){return(m&262144==262144)?(s.platform):""},jsVer:function(){if(m&131072!=131072){return""}var aI,e,aK,D=1,aH=0,i=(s.appName.indexOf("Microsoft Internet Explorer")>-1)?"MSIE":s.appName,aJ=s.appVersion;if("MSIE"==i){e="MSIE";aI=aJ.indexOf(e);if(aI>=0){aK=window.parseInt(aJ.substring(aI+5));if(3<=aK){D=1.1;if(4<=aK){D=1.3}}}}else{if(("Netscape"==i)||("Opera"==i)||("Mozilla"==i)){D=1.3;e="Netscape6";aI=aJ.indexOf(e);if(aI>=0){D=1.5}}}return D},network:function(){if(m&65536!=65536){return""}var i="";i=(s.connection&&s.connection.type)?s.connection.type:i;try{F.body.addBehavior("#default#clientCaps");i=F.body.connectionType}catch(D){i="unkown"}return i},language:function(){return(m&32768==32768)?(s.systemLanguage||s.language):""},timezone:function(){return(m&16384==16384)?(new Date().getTimezoneOffset()/60):""},flashVer:function(){if(m&8192!=8192){return""}var aK=s.plugins,aH,aL,aN;if(aK&&aK.length){for(var aJ in aK){aL=aK[aJ];if(aL.description==null){continue}if(aH!=null){break}aN=aL.description.toLowerCase();if(aN.indexOf("flash")!=-1){aH=aL.version?parseInt(aL.version):aN.match(/\d+/);continue}}}else{if(window.ActiveXObject){for(var aI=10;aI>=2;aI--){try{var D=new ActiveXObject("ShockwaveFlash.ShockwaveFlash."+aI);if(D){aH=aI;break}}catch(aM){}}}else{if(W.indexOf("webtv/2.5")!=-1){aH=3}else{if(W.indexOf("webtv")!=-1){aH=2}}}}return aH},javaEnabled:function(){if(m&4096!=4096){return""}var D=s.plugins,i=s.javaEnabled(),aH,aI;if(i==true){return 1}if(D&&D.length){for(var e in D){aH=D[e];if(aH.description==null){continue}if(i!=null){break}aI=aH.description.toLowerCase();if(aI.indexOf("java plug-in")!=-1){i=parseInt(aH.version);continue}}}else{if(window.ActiveXObject){i=(new ActiveXObject("JavaWebStart.IsInstalled")!=null)}}return i?1:0}};var ad={pageId:function(i){var D=i||r,aK="-9999-0-0-1";if((undefined==D)||(""==D)){try{var aH=h("publishid");if(""!=aH){var aJ=aH.split(",");if(aJ.length>0){if(aJ.length>=3){aK="-9999-0-"+aJ[1]+"-"+aJ[2]}D=aJ[0]}}else{D="0"}}catch(aI){D="0"}D=D+aK}return D},sessionCount:function(){var e=t("_s_upa");if(e==""){e=0}return e},excuteCount:function(){return SUDA.sudaCount},referrer:function(){if(m&2048!=2048){return""}var e=/^[^\?&#]*.swf([\?#])?/;if((aA=="")||(aA.match(e))){var i=V(j,"ref","&","");if(i!=""){return escape(i)}}return escape(aA)},isHomepage:function(){if(m&1024!=1024){return""}var D="";try{F.body.addBehavior("#default#homePage");D=F.body.isHomePage(j)?"Y":"N"}catch(i){D="unkown"}return D},PGLS:function(){return(m&512==512)?h("stencil"):""},ZT:function(){if(m&256!=256){return""}var e=h("subjectid");e.replace(",",".");e.replace(";",",");return escape(e)},mediaType:function(){return(m&128==128)?h("mediaid"):""},domCount:function(){return(m&64==64)?F.getElementsByTagName("*").length:""},iframeCount:function(){return(m&32==32)?F.getElementsByTagName("iframe").length:""}};var av={visitorId:function(){var i=15;var e=t(aa);if(e.length>i&&u==0){return e}else{return}},fvisitorId:function(e){if(!e){var e=t(Y);return e}else{at(Y,e,3650)}},sessionId:function(){var e=t(H);if(""==e){var i=new Date();e=Math.random()*10000000000000+"."+i.getTime()}return e},flashCookie:function(e){if(e){}else{return p}},lastVisit:function(){var D=t(H);var aI=t(P);var aH=aI.split(":");var aJ="",i;if(aH.length>=6){if(D!=aH[4]){i=new Date();var e=new Date(window.parseInt(aH[0]));aH[1]=window.parseInt(aH[1])+1;if(i.getMonth()!=e.getMonth()){aH[2]=1}else{aH[2]=window.parseInt(aH[2])+1}if(((i.getTime()-e.getTime())/86400000)>=7){aH[3]=1}else{if(i.getDay()<e.getDay()){aH[3]=1}else{aH[3]=window.parseInt(aH[3])+1}}aJ=aH[0]+":"+aH[1]+":"+aH[2]+":"+aH[3];aH[5]=aH[0];aH[0]=i.getTime();at(P,aH[0]+":"+aH[1]+":"+aH[2]+":"+aH[3]+":"+D+":"+aH[5],360)}else{aJ=aH[5]+":"+aH[1]+":"+aH[2]+":"+aH[3]}}else{i=new Date();aJ=":1:1:1";at(P,i.getTime()+aJ+":"+D+":",360)}return aJ},userNick:function(){if(al!=""){return al}var D=unescape(t(l));if(D!=""){var i=V(D,"ag","&","");var e=V(D,"user","&","");var aH=V(D,"uid","&","");var aJ=V(D,"sex","&","");var aI=V(D,"dob","&","");al=i+":"+e+":"+aH+":"+aJ+":"+aI;return al}else{return""}},userOrigin:function(){if(m&4!=4){return""}var e=t(aE);var i=e.split(":");if(i.length>=2){return i[0]}else{return""}},advCount:function(){return(m&2==2)?t(E):""},setUOR:function(){var aL=t(aE),aP="",i="",aO="",aI="",aM=j.toLowerCase(),D=F.referrer.toLowerCase();var aQ=/[&|?]c=spr(_[A-Za-z0-9]{1,}){3,}/;var aK=new Date();if(aM.match(aQ)){aO=aM.match(aQ)[0]}else{if(D.match(aQ)){aO=D.match(aQ)[0]}}if(aO!=""){aO=aO.substr(3)+":"+aK.getTime()}if(aL==""){if(t(P)==""){aP=ak(D);i=ak(aM)}at(aE,aP+","+i+","+aO,365)}else{var aJ=0,aN=aL.split(",");if(aN.length>=1){aP=aN[0]}if(aN.length>=2){i=aN[1]}if(aN.length>=3){aI=aN[2]}if(aO!=""){aJ=1}else{var aH=aI.split(":");if(aH.length>=2){var e=new Date(window.parseInt(aH[1]));if(e.getTime()<(aK.getTime()-86400000*30)){aJ=1}}}if(aJ){at(aE,aP+","+i+","+aO,365)}}},setAEC:function(e){if(""==e){return}var i=t(E);if(i.indexOf(e+",")<0){i=i+e+","}at(E,i,7)},ssoInfo:function(){var D=unescape(aj(t("sso_info")));if(D!=""){if(D.indexOf("uid=")!=-1){var i=V(D,"uid","&","");return escape("uid:"+i)}else{var e=V(D,"u","&","");return escape("u:"+unescape(e))}}else{return""}},subp:function(){return t("SUBP")}};var ai={CI:function(){var e=["sz:"+ap.screenSize(),"dp:"+ap.colorDepth(),"ac:"+ap.appCode(),"an:"+ap.appName(),"cpu:"+ap.cpu(),"pf:"+ap.platform(),"jv:"+ap.jsVer(),"ct:"+ap.network(),"lg:"+ap.language(),"tz:"+ap.timezone(),"fv:"+ap.flashVer(),"ja:"+ap.javaEnabled()];return"CI="+e.join("|")},PI:function(e){var i=["pid:"+ad.pageId(e),"st:"+ad.sessionCount(),"et:"+ad.excuteCount(),"ref:"+ad.referrer(),"hp:"+ad.isHomepage(),"PGLS:"+ad.PGLS(),"ZT:"+ad.ZT(),"MT:"+ad.mediaType(),"keys:","dom:"+ad.domCount(),"ifr:"+ad.iframeCount()];return"PI="+i.join("|")},UI:function(){var e=["vid:"+av.visitorId(),"sid:"+av.sessionId(),"lv:"+av.lastVisit(),"un:"+av.userNick(),"uo:"+av.userOrigin(),"ae:"+av.advCount(),"lu:"+av.fvisitorId(),"si:"+av.ssoInfo(),"rs:"+(n?1:0),"dm:"+(B?1:0),"su:"+av.subp()];return"UI="+e.join("|")},EX:function(i,e){if(m&1!=1){return""}i=(null!=i)?i||"":b;e=(null!=e)?e||"":a;return"EX=ex1:"+i+"|ex2:"+e},MT:function(){return"MT="+aF().join("|")},V:function(){return an},R:function(){return"gUid_"+new Date().getTime()}};function ax(){var aK="-",aH=F.referrer.toLowerCase(),D=j.toLowerCase();if(""==t(X)){if(""!=aH){aK=ak(aH)}at(X,aK,"","weibo.com")}var aI=/weibo.com\/reg.php/;if(D.match(aI)){var aJ=V(unescape(D),"sharehost","&","");var i=V(unescape(D),"appkey","&","");if(""!=aJ){at(X,aJ,"","weibo.com")}at("appkey",i,"","weibo.com")}}function d(e,i){G(e,i)}function G(i,D){D=D||{};var e=new Image(),aH;if(D&&D.callback&&typeof D.callback=="function"){e.onload=function(){clearTimeout(aH);aH=null;D.callback(true)}}SUDA.img=e;e.src=i;aH=setTimeout(function(){if(D&&D.callback&&typeof D.callback=="function"){D.callback(false);e.onload=null}},D.timeout||2000)}function x(e,aH,D,aI){SUDA.sudaCount++;if(!av.visitorId()&&!L()){if(u<3){u++;setTimeout(x,500);return}}var i=N+[ai.V(),ai.CI(),ai.PI(e),ai.UI(),ai.MT(),ai.EX(aH,D),ai.R()].join("&");G(i,aI)}function y(e,D,i){if(aG||A){return}if(SUDA.sudaCount!=0){return}x(e,D,i)}function ab(e,aH){if((""==e)||(undefined==e)){return}av.setAEC(e);if(0==aH){return}var D="AcTrack||"+t(aa)+"||"+t(H)+"||"+av.userNick()+"||"+e+"||";var i=ag+D+"&gUid_"+new Date().getTime();d(i)}function aq(aI,e,i,aJ){aJ=aJ||{};if(!i){i=""}else{i=escape(i)}var aH="UATrack||"+t(aa)+"||"+t(H)+"||"+av.userNick()+"||"+aI+"||"+e+"||"+ad.referrer()+"||"+i+"||"+(aJ.realUrl||"")+"||"+(aJ.ext||"");var D=ag+aH+"&gUid_"+new Date().getTime();d(D,aJ)}function aC(aK){var i=g(aK);var aI=i.target;var aH="",aL="",D="";var aJ;if(aI!=null&&aI.getAttribute&&(!aI.getAttribute("suda-uatrack")&&!aI.getAttribute("suda-actrack")&&!aI.getAttribute("suda-data"))){while(aI!=null&&aI.getAttribute&&(!!aI.getAttribute("suda-uatrack")||!!aI.getAttribute("suda-actrack")||!!aI.getAttribute("suda-data"))==false){if(aI==F.body){return}aI=aI.parentNode}}if(aI==null||aI.getAttribute==null){return}aH=aI.getAttribute("suda-actrack")||"";aL=aI.getAttribute("suda-uatrack")||aI.getAttribute("suda-data")||"";sudaUrls=aI.getAttribute("suda-urls")||"";if(aL){aJ=J(aL);if(aI.tagName.toLowerCase()=="a"){D=aI.href}opts={};opts.ext=(aJ.ext||"");aJ.key&&SUDA.uaTrack&&SUDA.uaTrack(aJ.key,aJ.value||aJ.key,D,opts)}if(aH){aJ=J(aH);aJ.key&&SUDA.acTrack&&SUDA.acTrack(aJ.key,aJ.value||aJ.key)}}if(window.SUDA&&Object.prototype.toString.call(window.SUDA)==="[object Array]"){for(var Q=0,ae=SUDA.length;Q<ae;Q++){switch(SUDA[Q][0]){case"setGatherType":m=SUDA[Q][1];break;case"setGatherInfo":r=SUDA[Q][1]||r;b=SUDA[Q][2]||b;a=SUDA[Q][3]||a;break;case"setPerformance":Z=SUDA[Q][1];break;case"setPerformanceFilter":C=SUDA[Q][1];break;case"setPerformanceInterval":K=SUDA[Q][1]*1||0;K=isNaN(K)?0:K;break;case"setGatherMore":M.push(SUDA[Q].slice(1));break;case"acTrack":S.push(SUDA[Q].slice(1));break;case"uaTrack":I.push(SUDA[Q].slice(1));break}}}aG=(function(D,i){if(ah.top==ah){return false}else{try{if(F.body.clientHeight==0){return false}return((F.body.clientHeight>=D)&&(F.body.clientWidth>=i))?false:true}catch(aH){return true}}})(320,240);A=(function(){return false})();av.setUOR();var au=av.sessionId();window.SUDA=window.SUDA||[];SUDA.sudaCount=SUDA.sudaCount||0;SUDA.log=function(){x.apply(null,arguments)};SUDA.acTrack=function(){ab.apply(null,arguments)};SUDA.uaTrack=function(){aq.apply(null,arguments)};U(F.body,"click",aC);window.GB_SUDA=SUDA;GB_SUDA._S_pSt=function(){};GB_SUDA._S_acTrack=function(){ab.apply(null,arguments)};GB_SUDA._S_uaTrack=function(){aq.apply(null,arguments)};window._S_pSt=function(){};window._S_acTrack=function(){ab.apply(null,arguments)};window._S_uaTrack=function(){aq.apply(null,arguments)};window._S_PID_="";if(!window.SUDA.disableClickstream){y()}try{k()}catch(T){}})();
//-->
</script> 
<noscript> 
<div style='position:absolute;top:0;left:0;width:0;height:0;visibility:hidden'><img width=0 height=0 src='//beacon.sina.com.cn/a.gif?noScript' border='0' alt='' /></div> 
</noscript> 
<!-- SUDA_CODE_END -->

<!-- SSO_GETCOOKIE_START -->
<script type="text/javascript">var sinaSSOManager=sinaSSOManager||{};sinaSSOManager.getSinaCookie=function(){function dc(u){if(u==undefined){return""}var decoded=decodeURIComponent(u);return decoded=="null"?"":decoded}function ps(str){var arr=str.split("&");var arrtmp;var arrResult={};for(var i=0;i<arr.length;i++){arrtmp=arr[i].split("=");arrResult[arrtmp[0]]=dc(arrtmp[1])}return arrResult}function gC(name){var Res=eval("/"+name+"=([^;]+)/").exec(document.cookie);return Res==null?null:Res[1]}var sup=dc(gC("SUP"));if(!sup){sup=dc(gC("SUR"))}if(!sup){return null}return ps(sup)};</script>
<!-- SSO_GETCOOKIE_END -->

<script type="text/javascript">new function(r,s,t){this.a=function(n,t,e){if(window.addEventListener){n.addEventListener(t,e,false);}else if(window.attachEvent){n.attachEvent("on"+t,e);}};this.b=function(f){var t=this;return function(){return f.apply(t,arguments);};};this.c=function(){var f=document.getElementsByTagName("form");for(var i=0;i<f.length;i++){var o=f[i].action;if(this.r.test(o)){f[i].action=o.replace(this.r,this.s);}}};this.r=r;this.s=s;this.d=setInterval(this.b(this.c),t);this.a(window,"load",this.b(function(){this.c();clearInterval(this.d);}));}(/http:\/\/www\.google\.c(om|n)\/search/, "http://keyword.sina.com.cn/searchword.php", 250);</script>
<!-- body code end -->


<div id="page">
<!-- 新闻中心 head begin -->
<!-- 标准二级导航_新闻中心 begin -->
<style type="text/css">
.secondaryHeader{height:33px;overflow:hidden;background:url(https://i2.sinaimg.cn/dy/images/header/2008/standardl2nav_bg.gif) repeat-x #fff;color:#000;font-size:12px;font-weight:100;width:950px;}
.secondaryHeader a,.secondaryHeader a:visited{color:#000;text-decoration:none;}
.secondaryHeader a:hover,.secondaryHeader a:active{color:#c00;text-decoration:underline;}
.sHBorder{border:1px #e3e3e3 solid;padding:0 10px 0 12px;overflow:hidden;zoom:1;}
.sHLogo{float:left;height:31px;line-height:31px;overflow:hidden;}
.sHLogo span{display:block;float:left;display:table-cell;vertical-align:middle;*display:block;*font-size:27px;*font-family:Arial;height:31px;}
.sHLogo span img{vertical-align:middle;border:0px;}
.sHLinks{float:right;line-height:31px;}
</style>
<div class="secondaryHeader">
	<div class="sHBorder">
		<div class="sHLogo"><span><a href="https://www.sina.com.cn/"><img src="https://i1.sinaimg.cn/dy/images/header/2009/standardl2nav_sina_new.gif" alt="新浪网"></a><a href="https://news.sina.com.cn/"><img src="https://i1.sinaimg.cn/dy/images/header/2009/standardl2nav_news.gif" alt="新闻中心"></a></span></div>
		<div class="sHLinks"><a href="https://news.sina.com.cn/">新闻首页</a>&nbsp;|&nbsp;<a href="https://www.sina.com.cn/">新浪首页</a>&nbsp;|&nbsp;<a href="https://news.sina.com.cn/guide/">新浪导航</a></div>
	</div>
</div>
<!-- 标准二级导航_新闻中心 end -->
<div style="height:5px;overflow:hidden;"></div>
<!-- 新闻中心 head end -->

<!-- banner begin -->
<div id="banner">
<div id="wrhg">
<style type="text/css">
.SinaCalendar_Cnl{width:140px; line-height:22px; padding:2px 0 0 0; text-align:center; color:#576379; background:url(https://i2.sinaimg.cn/dy/20061220_calendar/RLT_ws_001.gif) no-repeat; cursor: pointer; font-size:12px;}
</style>
<script type="text/javascript">
function SinaCalendar_GetObj(a){if(document.getElementById){return eval('document.getElementById("'+a+'")')}else if(document.layers){return eval("document.layers['"+a+"']")}else{return eval('document.all.'+a)}}function SinaCalendar_GetOffsetPos(a){var b=0,posLeft=0;do{b+=a.offsetTop||0;posLeft+=a.offsetLeft||0;a=a.offsetParent}while(a);return[posLeft,b]}function SinaCalendar_ie(){var a=navigator.appVersion;if(a.indexOf("MSIE")>=1){return true}else{return false}}function SinaCalendar_ChangeCnl(a){var b="SinaCalendar_CnlCon_"+a;var c="SinaCalendar_Cnl_"+a;if(SinaCalendar_GetObj(b).style.display=='block'){SinaCalendar_GetObj(b).style.display='none'}else if(SinaCalendar_GetObj(b).style.display=='none'){var i=0;while(1){if(document.getElementById("SinaCalendar_CnlCon_"+i)){document.getElementById("SinaCalendar_CnlCon_"+i).style.display="none"}else{break}i++}SinaCalendar_GetObj(b).style.display='block'}}function SinaCalendar_closeDiv(a){document.getElementById("SinaCalendar_CnlCon_"+a).style.display='none'}function CreateDiv(a,b,c,d,e,f,g,h){document.write("<div style='width:140px; margin:0 auto;'>");document.write("<div style='position:relative;' style='z-index:"+h+";'>");document.write("	<div class='SinaCalendar_Cnl' id='SinaCalendar_Cnl_"+a+"' onclick='javascript:SinaCalendar_ChangeCnl("+a+");'>&nbsp;&nbsp;"+b+"</div>");document.write("	<div name='SinaCalendar_CnlCon' id='SinaCalendar_CnlCon_"+a+"' style='display:none;position:absolute;top:"+f+"px; left:"+g+"px;width:140px; height:176px;'>");document.write("		<div style='background:#cde0f4; width:140px; height:15px;' align='right'><img src='https://i1.sinaimg.cn/dy/20061220_calendar/RLT_ws_002.gif' width='15' height='15' alt='关闭' title='关闭' style='cursor:pointer;' onclick='SinaCalendar_closeDiv("+a+")'/></div>");document.write("<iframe name='RL' frameborder='0' height='168' width='140' marginheight='0' marginwidth='0' scrolling='no' src='https://news.sina.com.cn/iframe/pc/calendar08.html?startDate=2004-07-05&url=" + encodeURIComponent("https://news.sina.com.cn/blank/hotnews_review.d.html?date=yearmonthday") + "&defaultUrl=https://news.sina.com.cn'></iframe>");document.write("	</div>");document.write("</div>");document.write("</div>")}
CreateDiv(0,"查看往日回顾","http://news.sina.com.cn/hotnews/yearmonthday.shtml","http://news.sina.com.cn","2004-07-05",20,0,99);
</script><div style="width:140px; margin:0 auto;"><div style="position:relative;">	<div class="SinaCalendar_Cnl" id="SinaCalendar_Cnl_0" onclick="javascript:SinaCalendar_ChangeCnl(0);">&nbsp;&nbsp;查看往日回顾</div>	<div name="SinaCalendar_CnlCon" id="SinaCalendar_CnlCon_0" style="display:none;position:absolute;top:20px; left:0px;width:140px; height:176px;">		<div style="background:#cde0f4; width:140px; height:15px;" align="right"><img src="https://i1.sinaimg.cn/dy/20061220_calendar/RLT_ws_002.gif" width="15" height="15" alt="关闭" title="关闭" style="cursor:pointer;" onclick="SinaCalendar_closeDiv(0)"></div><iframe name="RL" frameborder="0" height="168" width="140" marginheight="0" marginwidth="0" scrolling="no" src="https://news.sina.com.cn/iframe/pc/calendar08.html?startDate=2004-07-05&amp;url=https%3A%2F%2Fnews.sina.com.cn%2Fblank%2Fhotnews_review.d.html%3Fdate%3Dyearmonthday&amp;defaultUrl=https://news.sina.com.cn"></iframe>	</div></div></div>
</div>
<div id="date"></div>
</div>
<!-- banner end -->

<!-- 快速导航 begin -->
<div id="quickNav">
	<!-- <div id="qN1"><a href="https://news.sina.com.cn/hotnews/index_weekly.shtml">每周新闻排行</a></div> -->
	<div id="qN2">快速跳转：<a href="#1">总排行</a> | <a href="#a_video">视频</a> | <a href="#a_slide">图片</a> | <a href="#2">国内</a> | <a href="#3">国际</a> | <a href="#4">社会</a> | <a href="#5">体育</a> | <a href="#6">财经</a> | <a href="#8">娱乐</a> | <a href="#7">科技</a> | <a href="#9">军事</a> </div>
</div>
<!-- 快速导航 end -->


	
<a name="1"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>新闻总排行</h2>
		<ul class="Tabs">
			<li id="Tab11" class="on" onmouseover="chgTab(1,1)">点击量排行</li>
			<li id="Tab12" onmouseover="chgTab(1,2)">评论数排行</li>
      <li id="Tab13" onmouseover="chgTab(1,3)">分享数排行</li>
      <li id="Tab14" style="  " onmouseover="chgTab(1,4)">视频排行</li>
      <li id="Tab15" onmouseover="chgTab(1,5)">图片排行</li>

		</ul>
    <span class="caplink"><a href="http://news.sina.com.cn/hotnews/index_weekly.shtml">每周新闻排行</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con11">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=www_www_all_suda_suda&amp;top_time=20241117&amp;top_show_num=100&amp;top_order=DESC&amp;js_var=all_1_data01"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in all_1_data01.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=all_1_data01.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
  MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");

	var url=MyNewsList['url'];
    var sports = new RegExp("sports.sina.com.cn");
    	var oly = new RegExp("2012.sina.com.cn");
	var ent = new RegExp("ent.sina.com.cn");
	var finance = new RegExp("finance.sina.com.cn");
	var news = new RegExp("news.sina.com.cn");
	var mil = new RegExp("mil.news.sina.com.cn");
	var society=new RegExp("news.sina.com.cn/s/");
	var bbs=new RegExp("/bbs/");
            if(sports.test(url) || oly.test(url)){
				if(sCnt>3){
					continue;
				}else{
					sCnt++;
				}
			}
			if(ent.test(url)){
				if(eCnt>1){
					continue;
				}else{
					eCnt++;
				}
			}
			if(finance.test(url)){
				if(fCnt>5){
					continue;
				}else{
					fCnt++;
				}
			}
			if(!sports.test(url) && !oly.test(url) && !ent.test(url) && !finance.test(url) && !news.test(url) && !mil.test(url)){
				continue;
       }
	        if(society.test(url) || bbs.test(url))
	       {
				continue;
			}
	        
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank'"+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwinct8190709.shtml" target="_blank">会见拜登，中方这5句话很意味深长</a></td><td>京报网</td><td>11-17 08:41</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincq4570351.shtml" target="_blank">广州车展，面子是雷军余承东，里子又是谁？</a></td><td>界面新闻</td><td>11-17 07:56</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincn7792843.shtml" target="_blank">间谍利用视频监控漏洞伺机窃取我国家秘密，国安机关提示</a></td><td>北京日报客户端</td><td>11-17 08:14</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwismr8066511.shtml" target="_blank">军迷直呼过瘾！200秒视频详解歼-20航展飞行动作</a></td><td>央视</td><td>11-17 09:50</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwkazk1071233.shtml" target="_blank">张家界家住三楼的一家七口在火灾中遇难，邻居：起火点为二楼手机配件仓库</a></td><td>极目新闻</td><td>11-17 15:48</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/2024-11-16/doc-incwhqyc8677143.shtml" target="_blank">江苏高校持刀伤人事件致8死17伤</a></td><td>央视</td><td>11-16 23:31</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwincn7789337.shtml" target="_blank">特朗普宣布她为白宫新闻秘书！年仅27岁，7月才当上新手妈妈</a></td><td>每日经济新闻</td><td>11-17 07:29</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwiwtp7959903.shtml" target="_blank">新加坡“第一家族”拆房风云</a></td><td>新浪新闻综合</td><td>11-17 12:28</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincs1406734.shtml" target="_blank">十字路口的客运站｜路在何方，县城客运司机的苦恼与困惑</a></td><td>澎湃新闻</td><td>11-17 07:30</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/o/2024-11-17/doc-incwismk7677493.shtml" target="_blank">商务部部长王文涛会见加拿大国贸部长伍凤仪</a></td><td>新京报</td><td>11-17 09:15</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con12" style="display:none;">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=qbpdpl&amp;top_time=20241117&amp;top_show_num=100&amp;top_order=DESC&amp;js_var=comment_all_data">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in comment_all_data.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=comment_all_data.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];

	var url=MyNewsList['url'];
  var sports = new RegExp("sports.sina.com.cn");
  var oly = new RegExp("2012.sina.com.cn");
	var ent = new RegExp("ent.sina.com.cn");
	var tech = new RegExp("tech.sina.com.cn");
	var finance = new RegExp("finance.sina.com.cn");
	var news = new RegExp("news.sina.com.cn");
	var mil = new RegExp("mil.news.sina.com.cn");
	var society=new RegExp("news.sina.com.cn/s/");
	var bbs=new RegExp("/bbs/");
            if(sports.test(url) || oly.test(url)){
				if(sCnt>3){
					continue;
				}else{
					sCnt++;
				}
			}
			if(ent.test(url)){
				if(eCnt>1){
					continue;
				}else{
					eCnt++;
				}
			}
			if(tech.test(url)){
				if(eCnt>20){
					continue;
				}else{
					eCnt++;
				}
			}
			if(finance.test(url)){
				if(fCnt>5){
					continue;
				}else{
					fCnt++;
				}
			}
			if(!sports.test(url) && !oly.test(url) && !ent.test(url) && !tech.test(url) && !finance.test(url) && !news.test(url) && !mil.test(url)){
				continue;
       }
	        if(society.test(url) || bbs.test(url))
	       {
				continue;
			}
	        
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2020-03-22/doc-iimxxsth0871469.shtml" target="_blank">意大利单日新增6557例确诊病例，累计确诊破5万</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-imxxsth0871469&amp;style=0" target="_blank">180,481</a></td><td>03-22 01:17</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-imxxsth0871469&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/gov/xlxw/2024-11-15/doc-incwcqvh9563813.shtml" target="_blank">习近平同秘鲁总统博鲁阿尔特举行会谈</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwcqvh9563813&amp;style=0" target="_blank">1,756</a></td><td>11-15 15:24</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwcqvh9563813&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-09-28/doc-incqtiyq6737094.shtml" target="_blank">为了可爱的中国丨平凡铸就伟大，英雄来自人民</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncqtiyq6737094&amp;style=0" target="_blank">1,752</a></td><td>09-28 18:57</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncqtiyq6737094&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/gov/xlxw/2024-11-07/doc-incvfcme9306223.shtml" target="_blank">第一观察·瞬间 | 总书记的手总是紧紧同人民握在一起</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncvfcme9306223&amp;style=0" target="_blank">1,749</a></td><td>11-07 11:47</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncvfcme9306223&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/gov/xlxw/2024-11-07/doc-incvfpyy9114739.shtml" target="_blank">时政微纪录丨习近平总书记湖北行</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncvfpyy9114739&amp;style=0" target="_blank">1,664</a></td><td>11-07 15:48</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncvfpyy9114739&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-17/doc-incwhvfw4911610.shtml" target="_blank">习近平出席亚太经合组织第三十一次领导人非正式会议并发表重要讲话</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwhvfw4911610&amp;style=0" target="_blank">1,574</a></td><td>11-17 01:43</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwhvfw4911610&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-17/doc-incwifvq7911630.shtml" target="_blank">习近平会见美国总统拜登</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwifvq7911630&amp;style=0" target="_blank">1,404</a></td><td>11-17 06:14</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwifvq7911630&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-17/doc-incwhvfy1778095.shtml" target="_blank">习近平在亚太经合组织第三十一次领导人非正式会议上的讲话（全文）</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwhvfy1778095&amp;style=0" target="_blank">1,247</a></td><td>11-17 01:14</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwhvfy1778095&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-09-02/doc-incmupaa3858638.shtml" target="_blank">习近平会见几内亚总统敦布亚</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncmupaa3858638&amp;style=0" target="_blank">674</a></td><td>09-02 21:05</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncmupaa3858638&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-09-02/doc-incmupai7752768.shtml" target="_blank">习近平会见塞舌尔总统拉姆卡拉旺</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncmupai7752768&amp;style=0" target="_blank">673</a></td><td>09-02 21:03</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncmupai7752768&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con13" style="display:none;">
		<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

    if (data[i].url.match(/http:\/\/you.video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }


	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<!--<script type="text/javascript" src="//top.collection.sina.com.cn/ws/GetTopDataList.php?top_type=day&top_cat=wbrmzf_qz&top_time=20241117&top_show_num=10&top_order=DESC&js_var=wbrmzf_qz_1_data&call_back=showContent"></script>-->
<script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?app_key=2953667869&amp;top_cat=total_sharenews_48h&amp;top_time=today&amp;top_type=day&amp;top_channel=news&amp;top_order=desc&amp;top_show_num=10&amp;get_new=1&amp;format=json&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="https://finance.sina.com.cn/stock/roll/2024-11-17/doc-incwkiim4506452.shtml" target="_blank">十大机构看后市：短期波动不改中长期趋势 A股未来两年出现大级别趋势的概率进一步加大</a></td><td>11</td><td>11-17 17:15</td><!-- <td><a href='https://finance.sina.com.cn/stock/roll/2024-11-17/doc-incwkiim4506452.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-17/doc-incwkiip1274487.shtml" target="_blank">越权、过于高调、激进推进自己议程——马斯克与特朗普圈子已经产生裂痕？</a></td><td>8</td><td>11-17 17:05</td><!-- <td><a href='https://finance.sina.com.cn/roll/2024-11-17/doc-incwkiip1274487.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwkiih0954635.shtml" target="_blank">A股新纪录！2100亿资金火速集结</a></td><td>7</td><td>11-17 17:52</td><!-- <td><a href='https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwkiih0954635.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://finance.sina.com.cn/stock/e/2024-11-17/doc-incwkiih0951052.shtml" target="_blank">2024年11月18日涨停板早知道：七大利好有望发酵</a></td><td>7</td><td>11-17 17:14</td><!-- <td><a href='https://finance.sina.com.cn/stock/e/2024-11-17/doc-incwkiih0951052.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://finance.sina.com.cn/stock/marketresearch/2024-11-17/doc-incwiwtn1174291.shtml" target="_blank">“国家队”四季度重仓股提前曝光！</a></td><td>6</td><td>11-17 11:21</td><!-- <td><a href='https://finance.sina.com.cn/stock/marketresearch/2024-11-17/doc-incwiwtn1174291.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-17/doc-incwkaze7498958.shtml" target="_blank">并购重组利好政策频出！本周披露并购重组进展的A股名单一览</a></td><td>5</td><td>11-17 15:17</td><!-- <td><a href='https://finance.sina.com.cn/roll/2024-11-17/doc-incwkaze7498958.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-16/doc-incwfyah5326958.shtml" target="_blank">重大信号+一个重要预言</a></td><td>4</td><td>11-17 01:06</td><!-- <td><a href='https://finance.sina.com.cn/roll/2024-11-16/doc-incwfyah5326958.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwismq1290793.shtml" target="_blank">段永平、李录，190亿美元最新持仓！</a></td><td>4</td><td>11-17 09:46</td><!-- <td><a href='https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwismq1290793.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-16/doc-incwkiip1283128.shtml" target="_blank">尹中立：把市场波动的原因归结为散户投资者是不符合事实的</a></td><td>4</td><td>11-17 17:12</td><!-- <td><a href='https://finance.sina.com.cn/jjxw/2024-11-16/doc-incwkiip1283128.shtml' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwiwtk4385158.shtml" target="_blank">ETF 20载｜被动投资崛起</a></td><td>2</td><td>11-17 16:51</td><!-- <td><a href='https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwiwtk4385158.shtml' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
	<div class="Cons" id="Con14" style="display:none;">
					<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">播放数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=video_news_all_by_vv&amp;top_time=20241117&amp;top_show_num=50&amp;top_order=DESC&amp;get_new=1&amp;js_var=sp_xw_yypdpx_1_data"> 


   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyVideoList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyVideo = new Object();
  for (i in sp_xw_yypdpx_1_data.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {

   if(j>9)
	{
	   break;
   }
   MyVideoList=sp_xw_yypdpx_1_data.data[i];

	var month = MyVideoList['create_date'].split("-")[1];
	var day = MyVideoList['create_date'].split("-")[2];
    var hour= MyVideoList['create_time'].split(":")[0];
    var min= MyVideoList['create_time'].split(":")[1];
  MyVideoList['comment_url'] = MyVideoList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");

	var url=MyVideoList['url'];

    var videoNewsLeft = '';
    if (MyVideoList['url'].match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

    if (MyVideoList['url'].match(/http:\/\/you.video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }



     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyVideoList['url']+"' target='_blank'"+videoNewsLeft+">"+MyVideoList['title'].replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+MyVideoList['top_num']+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td></tr>");


 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvtuh7871763.d.html" target="_blank">小哥送完外卖站路边吃饭，被店家“吼”进屋吹空调：最凶的语气说最温柔的话</a></td><td>9,440</td><td>07-12 10:08</td></tr><tr><td>2</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvtuh7873299.d.html" target="_blank">女子切5斤五花肉后进屋，邻居家狗进门连吃带拿清光，女子看空盘自我怀疑</a></td><td>4,731</td><td>07-12 10:27</td></tr><tr><td>3</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvtuc4724737.d.html" target="_blank">15岁女孩玩恋爱手游充值一万多：为抽卡与“男主”互动，事后觉得被引诱</a></td><td>2,392</td><td>07-12 12:59</td></tr><tr><td>4</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvttz7947666.d.html" target="_blank">画面曝光！因故障滞留太空超一个月，美宇航员坚信飞船能安全返航</a></td><td>2,178</td><td>07-12 10:09</td></tr><tr><td>5</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccwkrt7737321.d.html" target="_blank">河北承德一村庄突遭泥浆侵袭：街道变河道，有车辆被冲走</a></td><td>1,821</td><td>07-12 16:08</td></tr><tr><td>6</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccwkrx0861815.d.html" target="_blank">监拍飞石落入客车砸中乘客 亲历者：石头比头还大 玻璃碴子全飞起来了</a></td><td>244</td><td>07-12 16:53</td></tr><tr><td>7</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-11/detail-inccteff8778603.d.html" target="_blank">4岁抗癌网红“小苹果”去世，殡仪馆20盆花收费13800元气哭母亲，官方回应</a></td><td>74</td><td>07-12 10:26</td></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con15" style="display:none;">
					<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=total_slide_suda&amp;top_time=20241117&amp;top_show_num=100&amp;top_order=DESC&amp;js_var=slide_image_1_data"> 



   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MySlideList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MySlide = new Object();
  for (i in slide_image_1_data.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {

   if(j>9)
	{
	   break;
   }
   MySlideList=slide_image_1_data.data[i];

	var month = MySlideList['create_date'].split("-")[1];
	var day = MySlideList['create_date'].split("-")[2];
    var hour= MySlideList['create_time'].split(":")[0];
    var min= MySlideList['create_time'].split(":")[1];
  MySlideList['comment_url'] = MySlideList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");

	var url=MySlideList['url'];
    var sports = new RegExp("sports.sina.com.cn");
    	var oly = new RegExp("2012.sina.com.cn");
	var ent = new RegExp("ent.sina.com.cn");
	var finance = new RegExp("finance.sina.com.cn");
	var news = new RegExp("news.sina.com.cn");
	var mil = new RegExp("mil.news.sina.com.cn");
	var society=new RegExp("news.sina.com.cn/s/");
	var bbs=new RegExp("/bbs/");
            if(sports.test(url) || oly.test(url)){
				if(sCnt>3){
					continue;
				}else{
					sCnt++;
				}
			}
			if(ent.test(url)){
				if(eCnt>1){
					continue;
				}else{
					eCnt++;
				}
			}
			if(finance.test(url)){
				if(fCnt>5){
					continue;
				}else{
					fCnt++;
				}
			}
			if(!sports.test(url) && !oly.test(url) && !ent.test(url) && !finance.test(url) && !news.test(url) && !mil.test(url)){
				continue;
       }
	        if(society.test(url) || bbs.test(url))
	       {
				continue;
			}

     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MySlideList['url']+"' target='_blank'"+">"+MySlideList['title']+"</a></td><td>"+MySlideList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MySlideList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");

 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/c/slide_1_2841_602591.html" target="_blank">第十五届中国航展第五日：俄罗斯“勇士”飞行表演队炫舞蓝天</a></td><td>视觉中国</td><td>11-17 08:17</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/c/slide_1_2841_602593.html" target="_blank">北京：银杏树下遍地金黄 市民撒起“银杏雨”</a></td><td>视觉中国</td><td>11-17 08:21</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/w/slide_1_2841_602595.html" target="_blank">超级月亮升起 映照加沙地带难民营残垣断壁</a></td><td>视觉中国</td><td>11-17 08:32</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/w/slide_1_2841_602596.html" target="_blank">奥地利秋日雾霭缭绕</a></td><td>视觉中国</td><td>11-17 08:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/star/slide_4_704_377160.html" target="_blank">组图：《我们的翻译官》官宣开机 宋茜陈星旭联袂出演</a></td><td>其他</td><td>10-27 11:27</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/go/slide_2_820_287555.html" target="_blank">高清-第六届中日韩聂卫平杯大师赛首日 古力李昌镐等出战</a></td><td>新浪体育</td><td>11-16 22:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/g_seriea/slide_2_730_262373.html" target="_blank">伊布30岁后至今攻入297粒俱乐部进球</a></td><td>SIPA图片社</td><td>02-08 10:14</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/g/slide_2_730_282293.html" target="_blank">[欧冠]那不勒斯3-0格拉斯哥流浪者</a></td><td>视觉中国</td><td>10-27 09:36</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/tv/slide_4_704_389163.html" target="_blank">组图：TVB将翻拍韩剧《黑话律师》 原剧由李钟硕林允儿主演</a></td><td>其他</td><td>09-25 19:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/go/slide_2_820_286732.html" target="_blank">图集-亚运围棋男团决赛 中国-韩国</a></td><td>新浪体育</td><td>10-03 16:44</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>


</div>
<!-- loopblk end -->


<a name="a_video"></a>
<!-- loopblk begin -->
<div class="loopblk" style="  ">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>视频排行</h2>

    <span class="caplink"><a href="http://video.sina.com.cn/news/" target="_blank">更多视频新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons">
					<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">播放数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=video_news_all_by_vv&amp;top_time=20241117&amp;top_show_num=50&amp;top_order=DESC&amp;get_new=1&amp;js_var=sp_xw_yypdpx_1_data"> 


   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyVideoList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyVideo = new Object();
  for (i in sp_xw_yypdpx_1_data.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {

   if(j>9)
	{
	   break;
   }
   MyVideoList=sp_xw_yypdpx_1_data.data[i];

	var month = MyVideoList['create_date'].split("-")[1];
	var day = MyVideoList['create_date'].split("-")[2];
    var hour= MyVideoList['create_time'].split(":")[0];
    var min= MyVideoList['create_time'].split(":")[1];
  MyVideoList['comment_url'] = MyVideoList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");

	var url=MyVideoList['url'];

    var videoNewsLeft = '';
    if (MyVideoList['url'].match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

    if (MyVideoList['url'].match(/http:\/\/you.video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }



     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyVideoList['url']+"' target='_blank'"+videoNewsLeft+">"+MyVideoList['title'].replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+MyVideoList['top_num']+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td></tr>");


 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvtuh7871763.d.html" target="_blank">小哥送完外卖站路边吃饭，被店家“吼”进屋吹空调：最凶的语气说最温柔的话</a></td><td>9,440</td><td>07-12 10:08</td></tr><tr><td>2</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvtuh7873299.d.html" target="_blank">女子切5斤五花肉后进屋，邻居家狗进门连吃带拿清光，女子看空盘自我怀疑</a></td><td>4,731</td><td>07-12 10:27</td></tr><tr><td>3</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvtuc4724737.d.html" target="_blank">15岁女孩玩恋爱手游充值一万多：为抽卡与“男主”互动，事后觉得被引诱</a></td><td>2,392</td><td>07-12 12:59</td></tr><tr><td>4</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccvttz7947666.d.html" target="_blank">画面曝光！因故障滞留太空超一个月，美宇航员坚信飞船能安全返航</a></td><td>2,178</td><td>07-12 10:09</td></tr><tr><td>5</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccwkrt7737321.d.html" target="_blank">河北承德一村庄突遭泥浆侵袭：街道变河道，有车辆被冲走</a></td><td>1,821</td><td>07-12 16:08</td></tr><tr><td>6</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-12/detail-inccwkrx0861815.d.html" target="_blank">监拍飞石落入客车砸中乘客 亲历者：石头比头还大 玻璃碴子全飞起来了</a></td><td>244</td><td>07-12 16:53</td></tr><tr><td>7</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-07-11/detail-inccteff8778603.d.html" target="_blank">4岁抗癌网红“小苹果”去世，殡仪馆20盆花收费13800元气哭母亲，官方回应</a></td><td>74</td><td>07-12 10:26</td></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
</div>
<!-- loopblk end -->


<a name="a_slide"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>图片排行</h2>

    <span class="caplink"><a href="http://news.sina.com.cn/photo/" target="_blank">更多图片&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons">
					<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=total_slide_suda&amp;top_time=20241117&amp;top_show_num=100&amp;top_order=DESC&amp;js_var=slide_image_1_data"> 



   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MySlideList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MySlide = new Object();
  for (i in slide_image_1_data.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {

   if(j>9)
	{
	   break;
   }
   MySlideList=slide_image_1_data.data[i];

	var month = MySlideList['create_date'].split("-")[1];
	var day = MySlideList['create_date'].split("-")[2];
    var hour= MySlideList['create_time'].split(":")[0];
    var min= MySlideList['create_time'].split(":")[1];
  MySlideList['comment_url'] = MySlideList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");

	var url=MySlideList['url'];
    var sports = new RegExp("sports.sina.com.cn");
    	var oly = new RegExp("2012.sina.com.cn");
	var ent = new RegExp("ent.sina.com.cn");
	var finance = new RegExp("finance.sina.com.cn");
	var news = new RegExp("news.sina.com.cn");
	var mil = new RegExp("mil.news.sina.com.cn");
	var society=new RegExp("news.sina.com.cn/s/");
	var bbs=new RegExp("/bbs/");
            if(sports.test(url) || oly.test(url)){
				if(sCnt>3){
					continue;
				}else{
					sCnt++;
				}
			}
			if(ent.test(url)){
				if(eCnt>1){
					continue;
				}else{
					eCnt++;
				}
			}
			if(finance.test(url)){
				if(fCnt>5){
					continue;
				}else{
					fCnt++;
				}
			}
			if(!sports.test(url) && !oly.test(url) && !ent.test(url) && !finance.test(url) && !news.test(url) && !mil.test(url)){
				continue;
       }
	        if(society.test(url) || bbs.test(url))
	       {
				continue;
			}

     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MySlideList['url']+"' target='_blank'"+">"+MySlideList['title']+"</a></td><td>"+MySlideList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MySlideList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");

 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/c/slide_1_2841_602591.html" target="_blank">第十五届中国航展第五日：俄罗斯“勇士”飞行表演队炫舞蓝天</a></td><td>视觉中国</td><td>11-17 08:17</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/c/slide_1_2841_602593.html" target="_blank">北京：银杏树下遍地金黄 市民撒起“银杏雨”</a></td><td>视觉中国</td><td>11-17 08:21</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/w/slide_1_2841_602595.html" target="_blank">超级月亮升起 映照加沙地带难民营残垣断壁</a></td><td>视觉中国</td><td>11-17 08:32</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://slide.news.sina.com.cn/w/slide_1_2841_602596.html" target="_blank">奥地利秋日雾霭缭绕</a></td><td>视觉中国</td><td>11-17 08:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/star/slide_4_704_377160.html" target="_blank">组图：《我们的翻译官》官宣开机 宋茜陈星旭联袂出演</a></td><td>其他</td><td>10-27 11:27</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/go/slide_2_820_287555.html" target="_blank">高清-第六届中日韩聂卫平杯大师赛首日 古力李昌镐等出战</a></td><td>新浪体育</td><td>11-16 22:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/g_seriea/slide_2_730_262373.html" target="_blank">伊布30岁后至今攻入297粒俱乐部进球</a></td><td>SIPA图片社</td><td>02-08 10:14</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/g/slide_2_730_282293.html" target="_blank">[欧冠]那不勒斯3-0格拉斯哥流浪者</a></td><td>视觉中国</td><td>10-27 09:36</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/tv/slide_4_704_389163.html" target="_blank">组图：TVB将翻拍韩剧《黑话律师》 原剧由李钟硕林允儿主演</a></td><td>其他</td><td>09-25 19:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/go/slide_2_820_286732.html" target="_blank">图集-亚运围棋男团决赛 中国-韩国</a></td><td>新浪体育</td><td>10-03 16:44</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
</div>
<!-- loopblk end -->


<a name="2"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>国内新闻</h2>
		<ul class="Tabs">
			<li id="Tab21" class="on" onmouseover="chgTab(2,1)">点击量排行</li>
			<li id="Tab22" onmouseover="chgTab(2,2)">评论数排行</li>
			<li id="Tab23" onmouseover="chgTab(2,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://news.sina.com.cn/china/" target="_blank">更多国内新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con21">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=news_china_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=news_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in news_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=news_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];        
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
    j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media']+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwinct8190709.shtml" target="_blank">会见拜登，中方这5句话很意味深长</a></td><td>京报网</td><td>11-17 08:41</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincq4570351.shtml" target="_blank">广州车展，面子是雷军余承东，里子又是谁？</a></td><td>界面新闻</td><td>11-17 07:56</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincn7792843.shtml" target="_blank">间谍利用视频监控漏洞伺机窃取我国家秘密，国安机关提示</a></td><td>北京日报客户端</td><td>11-17 08:14</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwismr8066511.shtml" target="_blank">军迷直呼过瘾！200秒视频详解歼-20航展飞行动作</a></td><td>央视</td><td>11-17 09:50</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwkazk1071233.shtml" target="_blank">张家界家住三楼的一家七口在火灾中遇难，邻居：起火点为二楼手机配件仓库</a></td><td>极目新闻</td><td>11-17 15:48</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincs1406734.shtml" target="_blank">十字路口的客运站｜路在何方，县城客运司机的苦恼与困惑</a></td><td>澎湃新闻</td><td>11-17 07:30</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/o/2024-11-17/doc-incwismk7677493.shtml" target="_blank">商务部部长王文涛会见加拿大国贸部长伍凤仪</a></td><td>新京报</td><td>11-17 09:15</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-17/doc-incwifvq7911630.shtml" target="_blank">习近平会见美国总统拜登</a></td><td>新华社</td><td>11-17 06:14</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwismq1294483.shtml" target="_blank">外交部发言人全面介绍中美元首利马会晤情况</a></td><td>央视</td><td>11-17 10:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-16/doc-incwhkry8351874.shtml" target="_blank">珠海市人民检察院依法以涉嫌以危险方法危害公共安全罪对樊某批准逮捕</a></td><td>央视</td><td>11-16 21:31</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con22" style="display:none;">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=gnxwpl&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=news_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in news_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=news_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-16/doc-incwhqyc8670174.shtml" target="_blank">金鸡奖获奖名单公布！雷佳音、李庚希分别获第37届中国电影金鸡奖最佳男、女主角</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwhqyc8670174&amp;style=0" target="_blank">254,178</a></td><td>11-16 22:01</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwhqyc8670174&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-17/doc-incwinct8188786.shtml" target="_blank">拜登：美国不支持“台湾独立”</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwinct8188786&amp;style=0" target="_blank">67,185</a></td><td>11-17 08:23</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwinct8188786&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-16/doc-incwhkry8351874.shtml" target="_blank">珠海市人民检察院依法以涉嫌以危险方法危害公共安全罪对樊某批准逮捕</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwhkry8351874&amp;style=0" target="_blank">29,872</a></td><td>11-16 21:31</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwhkry8351874&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-14/doc-incvzwnk1053120.shtml" target="_blank">中铁七局通报记者被打：配合警方调查</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncvzwnk1053120&amp;style=0" target="_blank">18,292</a></td><td>11-14 20:39</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncvzwnk1053120&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-14/doc-incvyuys7387463.shtml" target="_blank">李佩霞受贿案一审宣判</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncvyuys7387463&amp;style=0" target="_blank">7,721</a></td><td>11-14 09:33</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncvyuys7387463&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/gov/xlxw/2024-11-15/doc-incwcqvq0210853.shtml" target="_blank">学习进行时｜习近平主席与秘鲁的故事</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwcqvq0210853&amp;style=0" target="_blank">4,998</a></td><td>11-15 14:44</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwcqvq0210853&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/gov/xlxw/2024-11-15/doc-incwcefu0388950.shtml" target="_blank">独家视频丨习近平同秘鲁总统举行会谈</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwcefu0388950&amp;style=0" target="_blank">2,818</a></td><td>11-15 09:31</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwcefu0388950&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-14/doc-incvyuyu4720168.shtml" target="_blank">视频画报｜习近平主席与亚太大家庭</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncvyuyu4720168&amp;style=0" target="_blank">2,799</a></td><td>11-14 08:12</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncvyuyu4720168&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/gov/xlxw/2024-11-14/doc-incvzsen1148312.shtml" target="_blank">习近平在秘鲁媒体发表署名文章</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncvzsen1148312&amp;style=0" target="_blank">2,495</a></td><td>11-14 17:54</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncvzsen1148312&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/xl/2024-11-15/doc-incwapiy3941546.shtml" target="_blank">习近平抵达利马出席亚太经合组织第三十一次领导人非正式会议并对秘鲁进行国事访问</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&amp;newsid=comos-ncwapiy3941546&amp;style=0" target="_blank">2,186</a></td><td>11-15 02:58</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-ncwapiy3941546&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con23" style="display:none;">
		<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfgnxw&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfgnxw_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://news.sina.com.cn/c/2014-12-01/024531225769.shtml" target="_blank">新疆警察学院原党委书记李彦明被双开</a></td><td>7</td><td>12-01 02:45</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=1-1-31225769' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://news.sina.com.cn/c/2014-09-24/145630907684.shtml" target="_blank">中国社科院院长:国内阶级斗争是不可能熄灭的</a></td><td>5</td><td>09-24 14:56</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=1-1-30907684' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://news.sina.com.cn/c/2017-04-18/doc-ifyeifqx6197233.shtml" target="_blank">专访云南腾冲市旅游发展局副局长姜家邦： 从观光到重复休闲 腾冲旅游10年投资1000亿元</a></td><td>4</td><td>04-18 05:43</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=comos-fyeifqx6197233' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://news.sina.com.cn/c/2012-09-21/092225223127.shtml" target="_blank">西安日系车车主遇示威人群被重击头部砸穿颅骨</a></td><td>4</td><td>09-21 09:22</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gn&newsid=1-1-25223127' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://news.sina.com.cn/c/zg/jpm/2015-06-16/17411148.html" target="_blank">让我们想象下“北京如何搬出北京”？</a></td><td>4</td><td>06-16 17:41</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=&newsid=' target='_blank'>发表评论</a></td> --></tr>

</tbody></table>

	</div>
</div>
<!-- loopblk end -->
<a name="3"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>国际新闻</h2>
		<ul class="Tabs">
			<li id="Tab31" class="on" onmouseover="chgTab(3,1)">点击量排行</li>
			<li id="Tab32" onmouseover="chgTab(3,2)">评论数排行</li>
			<li id="Tab33" onmouseover="chgTab(3,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://news.sina.com.cn/world/" target="_blank">更多国际新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con31">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=news_world_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=news_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in news_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=news_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];        
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
    j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media']+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwincn7789337.shtml" target="_blank">特朗普宣布她为白宫新闻秘书！年仅27岁，7月才当上新手妈妈</a></td><td>每日经济新闻</td><td>11-17 07:29</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwiwtp7959903.shtml" target="_blank">新加坡“第一家族”拆房风云</a></td><td>新浪新闻综合</td><td>11-17 12:28</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwiwtk4379015.shtml" target="_blank">七国集团称将坚定不移支持乌克兰</a></td><td>新浪新闻综合</td><td>11-17 14:15</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwincq4566073.shtml" target="_blank">俄乌，突发！</a></td><td>新浪新闻综合</td><td>11-17 07:24</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwhzpw1659569.shtml" target="_blank">以总理内塔尼亚胡位于凯撒利亚的住宅遭炸弹袭击</a></td><td>央视</td><td>11-17 04:27</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwiwth7592535.shtml" target="_blank">以色列突发！</a></td><td>新浪新闻综合</td><td>11-17 12:07</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwifvu1533403.shtml" target="_blank">以安全机构：2枚照明弹落入以总理住宅院内 未造成损失</a></td><td>央视</td><td>11-17 07:26</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwiwtn1186743.shtml" target="_blank">印度成功进行远程高超音速导弹飞行试验</a></td><td>央视</td><td>11-17 13:29</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwincq4565027.shtml" target="_blank">金与正谴责韩国再向朝鲜南部散发反朝宣传单</a></td><td>央视</td><td>11-17 07:21</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwhzpu4800948.shtml" target="_blank">波兰外长：俄罗斯外交官将很快离开波兹南总领馆</a></td><td>央视</td><td>11-17 04:26</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con32" style="display:none;">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=gjxwpl&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=news_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in news_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=news_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-15/doc-incwcefn9697692.shtml" target="_blank">特朗普称俄乌战争必须停止</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwcefn9697692&amp;style=0" target="_blank">2,109</a></td><td>11-15 10:33</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwcefn9697692&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-15/doc-incwczme6191106.shtml" target="_blank">特朗普继续组建新班子 外媒：是为了回报对自己最忠诚的人</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwczme6191106&amp;style=0" target="_blank">555</a></td><td>11-15 19:29</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwczme6191106&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-15/doc-incwcefn9696976.shtml" target="_blank">特朗普盟友提出大胆计划：出售美联储黄金储备 购买100万枚比特币</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwcefn9696976&amp;style=0" target="_blank">525</a></td><td>11-15 10:31</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwcefn9696976&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-15/doc-incwcqvq0202609.shtml" target="_blank">“俄军在哈尔科夫战线取得重大突破”，乌媒也证实了</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwcqvq0202609&amp;style=0" target="_blank">503</a></td><td>11-15 14:12</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwcqvq0202609&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-16/doc-incwfyae8531724.shtml" target="_blank">爆料！马斯克或遭调查</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwfyae8531724&amp;style=0" target="_blank">500</a></td><td>11-16 15:15</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwfyae8531724&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-15/doc-incwczmk0044183.shtml" target="_blank">挪威渔民出海捕鱼，意外“捕获”美军核潜艇，美方回应</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwczmk0044183&amp;style=0" target="_blank">449</a></td><td>11-15 20:28</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwczmk0044183&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwiwtp7959903.shtml" target="_blank">新加坡“第一家族”拆房风云</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwiwtp7959903&amp;style=0" target="_blank">435</a></td><td>11-17 12:28</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwiwtp7959903&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-16/doc-incwfyah5329747.shtml" target="_blank">获特朗普提名后，他们被爆出“性丑闻”</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwfyah5329747&amp;style=0" target="_blank">274</a></td><td>11-16 16:45</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwfyah5329747&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwincn7789337.shtml" target="_blank">特朗普宣布她为白宫新闻秘书！年仅27岁，7月才当上新手妈妈</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwincn7789337&amp;style=0" target="_blank">121</a></td><td>11-17 07:29</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwincn7789337&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-17/doc-incwinct8184681.shtml" target="_blank">特朗普选择油企高管担任能源部长 并计划由能源委员会来推动生产</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&amp;newsid=comos-ncwinct8184681&amp;style=0" target="_blank">40</a></td><td>11-17 07:35</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=gj&newsid=comos-ncwinct8184681&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con33" style="display:none;">
		<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfgwxw&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfgwxw_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://news.sina.com.cn/w/2004-12-20/11314575163s.shtml" target="_blank">美国科学家最新发现女人更适合太空旅行</a></td><td>5</td><td>12-20 11:31</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=&newsid=1-1-5274497' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://news.sina.com.cn/w/zg/jpm/2014-07-18/120897.html" target="_blank">马航MH17坠毁：哭泣的面孔</a></td><td>5</td><td>07-18 12:08</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=&newsid=' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->


<a name="4"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->

	<div class="lbti">
		<h2>社会新闻</h2>
		<ul class="Tabs">
			<li id="Tab41" class="on" onmouseover="chgTab(4,1)">点击量排行</li>
			<li id="Tab42" onmouseover="chgTab(4,2)">评论数排行</li>
			<li id="Tab43" onmouseover="chgTab(4,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://news.sina.com.cn/society/" target="_blank">更多社会新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con41">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=news_society_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=news_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in news_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=news_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];        
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
    j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media']+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwincn7792395.shtml" target="_blank">“反诈老陈”辞职两年后：目前每月数十场直播，坦言做回警察没什么可能</a></td><td>新浪新闻综合</td><td>11-17 07:49</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwismr8074407.shtml" target="_blank">近期广州地区超40只宠物狗因疑似中毒死亡，官方通报</a></td><td>北京日报客户端</td><td>11-17 10:29</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-16/doc-incwhkse2020552.shtml" target="_blank">38吨稻谷丢失后警方不作为、态度差？派出所回应：已解决</a></td><td>新浪新闻综合</td><td>11-16 21:28</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwiwtk4358417.shtml" target="_blank">“女教师被丈夫举报出轨学生”当事人首发声：网传信息非事实，正维权</a></td><td>新浪新闻综合</td><td>11-17 11:28</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwifvq7912890.shtml" target="_blank">足坛一夜动态：德国7-0！荷兰晋级欧国联8强 巴萨女足4-0皇马</a></td><td>新浪新闻综合</td><td>11-17 06:27</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwincs1404679.shtml" target="_blank">ATP年终总决赛半决赛：辛纳2-0鲁德，连续两年晋级决赛</a></td><td>新浪新闻综合</td><td>11-17 06:48</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwkazh4272721.shtml" target="_blank">山东济宁万象汇一女孩高空坠落，不幸身亡！短短四秒 危险发生？</a></td><td>新浪新闻综合</td><td>11-17 14:46</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-16/doc-incwhksf8796820.shtml" target="_blank">无锡一高校被曝发生持刀伤人事件，派出所回应：已出警</a></td><td>新浪新闻综合</td><td>11-16 21:21</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwifvs4686770.shtml" target="_blank">惨案！德国7-0波黑锁欧国联A3头名 维尔茨2射1传哈弗茨传射</a></td><td>新浪新闻综合</td><td>11-17 05:47</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwifvs4690428.shtml" target="_blank">三球罚球绝杀&amp;26+9+6 字母22+15+12 普林斯23分 黄蜂险胜雄鹿</a></td><td>新浪新闻综合</td><td>11-17 06:31</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con42" style="display:none;">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=shxwpl&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=news_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in news_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=news_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://news.sina.com.cn/c/2024-11-17/doc-incwincn7789568.shtml" target="_blank">安徽舒城县政府大院“随便进”引热议，县长：大家都是群众，没有不方便</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwincn7789568&amp;style=0" target="_blank">11,376</a></td><td>11-17 07:31</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwincn7789568&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://news.sina.com.cn/w/2024-11-16/doc-incwfyak2246745.shtml" target="_blank">泽连斯基：努力确保明年以外交手段结束俄乌冲突</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwfyak2246745&amp;style=0" target="_blank">3,689</a></td><td>11-16 16:40</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwfyak2246745&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwismr8074407.shtml" target="_blank">近期广州地区超40只宠物狗因疑似中毒死亡，官方通报</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwismr8074407&amp;style=0" target="_blank">2,115</a></td><td>11-17 10:24</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwismr8074407&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-11-17/detail-incwkaze7490585.d.html" target="_blank">山东济宁一女孩商场坠落？目击者：连同电梯扶手旁玻璃坠落，当地回应</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwkaze7490585&amp;style=0" target="_blank">1,967</a></td><td>11-17 14:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwkaze7490585&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwiwtk4358417.shtml" target="_blank">“女教师被丈夫举报出轨学生”当事人首发声：网传信息非事实，正维权</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwiwtk4358417&amp;style=0" target="_blank">1,652</a></td><td>11-17 11:28</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwiwtk4358417&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/news/2024-11-17/detail-incwinct8187709.d.html" target="_blank">女孩在食堂打闹撞到端饭同学被烫，竟调监控发帖要男生道歉，校方回应</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwinct8187709&amp;style=0" target="_blank">1,473</a></td><td>11-17 08:11</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwinct8187709&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-16/doc-incwhekh2122090.shtml" target="_blank">四川一罪犯4年怀孕产子3次！被质疑“逃避坐牢”</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwhekh2122090&amp;style=0" target="_blank">934</a></td><td>11-16 17:19</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwhekh2122090&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://news.sina.com.cn/s/2024-11-17/doc-incwincn7792395.shtml" target="_blank">“反诈老陈”辞职两年后：目前每月数十场直播，坦言做回警察没什么可能</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwincn7792395&amp;style=0" target="_blank">886</a></td><td>11-17 07:49</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwincn7792395&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://news.sina.com.cn/zx/2024-11-17/doc-incwinct8191804.shtml" target="_blank">习近平出席亚太经合组织第三十一次领导人非正式会议并发表重要讲话</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwinct8191804&amp;style=0" target="_blank">853</a></td><td>11-17 08:49</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwinct8191804&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://news.sina.com.cn/zx/2024-11-17/doc-incwkaze7508390.shtml" target="_blank">习近平在巴西媒体发表署名文章</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&amp;newsid=comos-ncwkaze7508390&amp;style=0" target="_blank">680</a></td><td>11-17 15:48</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-ncwkaze7508390&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con43" style="display:none;">
		<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfshxw&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfshxw_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://news.sina.com.cn/s/wh/2016-04-25/doc-ifxrpvea1175772.shtml" target="_blank">河南一名高二学生参与群殴重伤 未脱离生命危险</a></td><td>6</td><td>04-25 13:30</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-fxrpvea1175772' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://news.sina.com.cn/s/2018-11-14/doc-ihmutuec0231866.shtml" target="_blank">成都16日起对限养区禁养犬进行收容 22种犬被禁</a></td><td>4</td><td>11-14 22:44</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=sh&newsid=comos-hmutuec0231866' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://news.sina.com.cn/s/2001-09-26/366526.html" target="_blank">出境游莫丢脸 读者批评部分上海人不文明举止</a></td><td>4</td><td>09-26 16:32</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=None&newsid=1-1-366526' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->

<a name="5"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>体育新闻</h2>
		<ul class="Tabs">
			<li id="Tab51" class="on" onmouseover="chgTab(5,1)">点击量排行</li>
			<li id="Tab52" onmouseover="chgTab(5,2)">评论数排行</li>
			<li id="Tab53" onmouseover="chgTab(5,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://sports.sina.com.cn/" target="_blank">更多体育新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con51">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.sports.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=sports_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=channel_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-17/doc-incwincq4574691.shtml" target="_blank">[新浪彩票]足彩第24180期任九：葡萄牙不败可期</a></td><td>新浪彩票</td><td>11-17 08:20</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-17/doc-incwincq4574269.shtml" target="_blank">[新浪彩票]足彩第24180期大势：西班牙谨慎防平</a></td><td>新浪彩票</td><td>11-17 08:17</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://sports.sina.com.cn/go/2024-11-17/doc-incwkazh4284727.shtml" target="_blank">三星杯八强战丁浩胜申真谞 中国棋手包揽四强</a></td><td>新浪体育综合</td><td>11-17 15:44</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-17/doc-incwincn7797958.shtml" target="_blank">[新浪彩票]足彩24180期盈亏指数：意大利主场不稳</a></td><td>新浪彩票</td><td>11-17 08:23</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-17/doc-incwismq1299474.shtml" target="_blank">[新浪彩票]足彩24180期冷热指数：奥地利坐和望赢</a></td><td>新浪彩票</td><td>11-17 10:39</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-17/doc-incwincn7797785.shtml" target="_blank">[新浪彩票]足彩24180期投注策略：希腊全身而退</a></td><td>新浪彩票</td><td>11-17 08:22</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-17/doc-incwincn7798339.shtml" target="_blank">竞彩大势：北马其顿高看一线 希腊客场难胜</a></td><td>新浪彩票</td><td>11-17 08:25</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://sports.sina.com.cn/basketball/nba/2024-11-17/doc-incwiwtk4361143.shtml" target="_blank">老詹关键三分克内克特27分 湖人险胜鹈鹕5连胜</a></td><td>新浪体育讯</td><td>11-17 11:37</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-16/doc-incwhqxw8233302.shtml" target="_blank">大乐透头奖1注1800万+5注1000万 奖池余额9.39亿</a></td><td>新浪彩票</td><td>11-16 22:44</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://sports.sina.com.cn/go/2024-11-17/doc-incwincn7805189.shtml" target="_blank">三星杯党毅飞复仇李轩豪 金禹丞世界大赛首秀惊艳</a></td><td>新浪体育综合</td><td>11-17 08:56</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con52" style="display:none;">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.sports.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=tyxwpl&amp;top_time=20241117&amp;top_show_num=30&amp;top_order=DESC&amp;js_var=channel_pl_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_pl_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_pl_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
	var match = new RegExp("match.sports.sina.com.cn");
	var url=MyNewsList['url'];
	if(match.test(url))
	 {
		continue;
	}
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://sports.sina.com.cn/others/volleyball/2024-11-16/doc-incwheki8910621.shtml" target="_blank">排超女排A级江苏3-2挫福建领跑 津沪两强五局险胜</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwheki8910621&amp;style=0" target="_blank">66,096</a></td><td>11-16 18:49</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwheki8910621&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://sports.sina.com.cn/global/others/2024-11-14/doc-incvzmwq1253298.shtml" target="_blank">梅西明年第一站来中国 迈阿密国际筹划25年季前赛</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncvzmwq1253298&amp;style=0" target="_blank">8,014</a></td><td>11-14 16:28</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncvzmwq1253298&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://sports.sina.com.cn/china/national/2024-11-15/doc-incwaauh0957678.shtml" target="_blank">世预赛-张玉宁90分钟反上演绝杀 国足1-0巴林</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwaauh0957678&amp;style=0" target="_blank">4,098</a></td><td>11-15 00:05</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwaauh0957678&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://sports.sina.com.cn/others/badmin/2024-11-16/doc-incwfyah5322857.shtml" target="_blank">日本大师赛第5日国羽2胜2负 李诗沣胜安赛龙进决赛</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwfyah5322857&amp;style=0" target="_blank">2,723</a></td><td>11-16 16:13</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwfyah5322857&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://sports.sina.com.cn/g/pl/2024-11-16/doc-incwfawq8924505.shtml" target="_blank">欧国联-C罗2射1传B费莱奥破门 葡萄牙5-1波兰</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwfawq8924505&amp;style=0" target="_blank">2,668</a></td><td>11-16 06:52</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwfawq8924505&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://sports.sina.com.cn/basketball/nba/2024-11-17/doc-incwiwtn1178257.shtml" target="_blank">塔图姆压哨绝杀绿军险胜猛龙 雄鹿遭误判输球</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwiwtn1178257&amp;style=0" target="_blank">1,428</a></td><td>11-17 11:49</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwiwtn1178257&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://sports.sina.com.cn/go/2024-11-15/doc-incwczme6205521.shtml" target="_blank">三星杯柯洁半目惜败申真谞 八强中国7人战韩国1人</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwczme6205521&amp;style=0" target="_blank">987</a></td><td>11-15 20:18</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwczme6205521&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://sports.sina.com.cn/others/snooker/2024-11-16/doc-incwfies2567485.shtml" target="_blank">冠中冠肖国栋6-3卫冕冠军 创造历史首度晋级决赛</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwfies2567485&amp;style=0" target="_blank">552</a></td><td>11-16 07:58</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwfies2567485&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://k.sina.com.cn/article_2289815244_887bcecc00102ca2i.html" target="_blank">轰27+5三分再创新高！克内克特两度暴扣天赋溢出 三战三分19中12</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwiwtk4357146&amp;style=0" target="_blank">523</a></td><td>11-17 11:23</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwiwtk4357146&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://sports.sina.com.cn/l/2024-11-15/doc-incwaxxw0496084.shtml" target="_blank">[新浪彩票]足彩第24179期任九：西班牙客战不败</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&amp;newsid=comos-ncwaxxw0496084&amp;style=0" target="_blank">474</a></td><td>11-15 08:25</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-ncwaxxw0496084&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con53" style="display:none;">
		<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.sports.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfty&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfty_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://video.sina.com.cn/p/sports/k/v/doc/2018-08-20/140868989666.html" target="_blank" class="videoNewsLeft">视频集锦-3Ball USA决赛日 中国新浪队比赛集锦</a></td><td>10</td><td>08-20 14:17</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=6-408-279480' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://video.sina.com.cn/p/sports/pl/v/2016-04-19/220765216587.html" target="_blank" class="videoNewsLeft">阿扎尔大聊NBA 一起关注《NBA最王牌》</a></td><td>5</td><td>04-19 22:07</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=6-408-232634' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://slide.sports.sina.com.cn/f1/slide_2_84822_130414.html" target="_blank">2017F1摩纳哥站第1次练习赛</a></td><td>5</td><td>01-01 08:00</td><!-- <td><a href='http://t.cn/RSPNaLo' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://video.sina.com.cn/p/sports/k/v/doc/2018-07-30/111368944827.html" target="_blank" class="videoNewsLeft">草根篮球剧集《无中生有》 中国街球明星战美国</a></td><td>4</td><td>07-30 11:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=6-408-278210' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://video.sina.com.cn/p/sports/k/v/doc/2018-09-21/141069022460.html" target="_blank" class="videoNewsLeft">一代人炽热记忆 中国男篮08奥运VS美国全场</a></td><td>4</td><td>09-21 14:16</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=6-408-281931' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://sports.sina.com.cn/basketball/cba/2018-11-20/doc-ihnyuqhi5164061.shtml" target="_blank">郭艾伦19分哈德森33+6 辽宁99-96险胜深圳</a></td><td>4</td><td>11-20 21:58</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-hnyuqhi5164061' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://sports.sina.com.cn/go/2018-11-20/doc-ihmutuec1980513.shtml" target="_blank">李钦诚：AI可分析出胜负手 柯洁：完全向AI学习</a></td><td>4</td><td>11-20 16:38</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-hmutuec1980513' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://sports.sina.com.cn/g/pl/2018-11-19/doc-ihnyuqhi1599417.shtml" target="_blank">马拉松“对国旗不敬事件” 晒出了多少脑残</a></td><td>4</td><td>11-19 11:51</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-hnyuqhi1599417' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://sports.sina.com.cn/run/2017-05-18/doc-ifyfkqiv6478958.shtml" target="_blank">中国马拉松“黑色”产业链深度揭秘</a></td><td>4</td><td>05-18 09:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-fyfkqiv6478958' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://sports.sina.com.cn/golf/pgatour/2018-11-19/doc-ihmutuec1473456.shtml" target="_blank">RSM精英赛霍维尔三世加洞夺冠 时隔11再赢美巡赛</a></td><td>4</td><td>11-19 07:57</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=ty&newsid=comos-hmutuec1473456' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->


<a name="6"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>财经新闻</h2>
		<ul class="Tabs">
			<li id="Tab71" class="on" onmouseover="chgTab(7,1)">点击量排行</li>
			<li id="Tab72" onmouseover="chgTab(7,2)">评论数排行</li>
			<li id="Tab73" onmouseover="chgTab(7,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://finance.sina.com.cn" target="_blank">更多财经新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con71">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.finance.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=finance_0_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=channel_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-17/doc-incwincs1412068.shtml" target="_blank">“必须结束战争”，泽连斯基给出时间表！俄军：击落乌军102架无人机！</a></td><td>每日经济新闻</td><td>11-17 08:26</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://finance.sina.com.cn/stock/2024-11-17/doc-incwincs1414148.shtml" target="_blank">最新披露！爆买中国资产！</a></td><td>券商中国</td><td>11-17 08:46</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwismq1290793.shtml" target="_blank">段永平、李录，190亿美元最新持仓！</a></td><td>券商中国</td><td>11-17 09:44</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwismn4471725.shtml" target="_blank">泽连斯基称“美国不能迫使乌克兰在谈判桌坐下并倾听”，马斯克发声</a></td><td>环球网</td><td>11-17 10:24</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwincq4561859.shtml" target="_blank">外交部发言人就中国获得2026年亚太经合组织主办权答记者问</a></td><td>新华社</td><td>11-17 06:54</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwinct8189697.shtml" target="_blank">最新！机构看好的20只潜力股出炉</a></td><td>证券时报e公司</td><td>11-17 08:31</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-17/doc-incwismk7691068.shtml" target="_blank">无视大盘下跌，149只股票本周走出独立行情！明天能否带头破局？</a></td><td>每日经济新闻</td><td>11-17 10:12</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-17/doc-incwhvfy1771528.shtml" target="_blank">江苏一高校发生持刀伤人事件，致8死17伤！刚刚，警方通报</a></td><td>昆明信息港</td><td>11-17 00:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://finance.sina.com.cn/world/2024-11-17/doc-incwincs1407956.shtml" target="_blank">习近平会见美国总统拜登</a></td><td>央视新闻客户端</td><td>11-17 07:40</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-17/doc-incwiwtk4371782.shtml" target="_blank">昔日“超市一哥”宣布，将关闭北京两家门店，公司最新回应了</a></td><td>每日经济新闻</td><td>11-17 12:13</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con72" style="display:none;">
				<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.finance.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=cjxwpl&amp;top_time=20241117&amp;top_show_num=30&amp;top_order=DESC&amp;js_var=channel_pl_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_pl_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_pl_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
	var match = new RegExp("match.sports.sina.com.cn");
	var url=MyNewsList['url'];
	if(match.test(url))
	 {
		continue;
	}
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/finance/2024-11-17/detail-incwiwtn1184515.d.html" target="_blank">中美领导人会晤 拜登表态：美方不支持“台独”</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwiwtn1184515&amp;style=0" target="_blank">2,573</a></td><td>11-17 12:46</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwiwtn1184515&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-16/doc-incwhqya1899913.shtml" target="_blank">最新！以色列打死米纳维、伊萨</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwhqya1899913&amp;style=0" target="_blank">2,189</a></td><td>11-16 23:27</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwhqya1899913&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwiwtk4379267.shtml" target="_blank">知情人：2名中企高管在菲律宾遭绑架撕票案主犯在美国落网</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwiwtk4379267&amp;style=0" target="_blank">2,073</a></td><td>11-17 12:42</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwiwtk4379267&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwinct8188644.shtml" target="_blank">会见拜登，中方这5句话很意味深长</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwinct8188644&amp;style=0" target="_blank">1,378</a></td><td>11-17 08:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwinct8188644&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-16/doc-incwheke5213548.shtml" target="_blank">泽连斯基表态！俄乌冲突将在明年结束？</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwheke5213548&amp;style=0" target="_blank">1,310</a></td><td>11-16 17:45</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwheke5213548&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwismn4471725.shtml" target="_blank">泽连斯基称“美国不能迫使乌克兰在谈判桌坐下并倾听”，马斯克发声</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwismn4471725&amp;style=0" target="_blank">663</a></td><td>11-17 10:24</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwismn4471725&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://finance.sina.com.cn/roll/2024-11-16/doc-incwfien8834144.shtml" target="_blank">“市值管理14条”正式版透露新信号：三类“松绑”与两类“加码”并现</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwfien8834144&amp;style=0" target="_blank">543</a></td><td>11-16 09:48</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwfien8834144&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-17/doc-incwiwtk4380704.shtml" target="_blank">中国获得2026年APEC主办权 将第三次担任东道主</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwiwtk4380704&amp;style=0" target="_blank">386</a></td><td>11-17 12:48</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwiwtk4380704&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://finance.sina.com.cn/jjxw/2024-11-16/doc-incwhvfu8111547.shtml" target="_blank">突然爆雷，几百人“被坑”？抢到黄金，平台不发货不退款！有人买了47万元，还有多人贷款下单，现在还不上了！上海警方已立案</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwhvfu8111547&amp;style=0" target="_blank">262</a></td><td>11-16 23:57</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwhvfu8111547&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://video.sina.com.cn/p/finance/2024-11-16/detail-incwhksa5105722.d.html" target="_blank">河北一公交车与面包车相撞1死1伤</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&amp;newsid=comos-ncwhksa5105722&amp;style=0" target="_blank">220</a></td><td>11-16 19:31</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-ncwhksa5105722&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con73" style="display:none;">
			<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.finance.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfcj&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfcj_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://finance.sina.com.cn/world/20150607/064822367735.shtml" target="_blank">天神娱乐234万美元拍得巴菲特午餐</a></td><td>5</td><td>06-07 06:48</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=31-1-22367735' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://finance.sina.com.cn/roll/2018-10-15/doc-ihmhafir8230445.shtml" target="_blank">马斯克点赞导演新海诚:我不是钢铁侠 叫我"埃隆酱"</a></td><td>5</td><td>10-15 19:33</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hmhafir8230445' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://finance.sina.com.cn/stock/usstock/c/2018-10-15/doc-ihmhafir8591930.shtml" target="_blank">美股早盘：道指标普转涨 纳指跌幅收窄</a></td><td>5</td><td>10-15 23:37</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hmhafir8591930' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://finance.sina.com.cn/stock/s/2018-09-26/doc-ihkmwytp2253391.shtml" target="_blank">黄河旋风被子公司坑惨 业绩承诺完成4%高管持股冻结</a></td><td>4</td><td>09-26 13:26</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hkmwytp2253391' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://finance.sina.com.cn/stock/hkstock/marketalerts/2018-09-26/doc-ihkmwytp2302372.shtml" target="_blank">中兴通讯联合中国联通网研院完成5G承载关键功能验证</a></td><td>4</td><td>09-26 13:51</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hkmwytp2302372' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://finance.sina.com.cn/meeting/2018-09-17/doc-ihkahyhy0559582.shtml" target="_blank">宗庆后：建议政府过紧日子少征税 多让老百姓消费</a></td><td>4</td><td>09-17 10:56</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hkahyhy0559582' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://finance.sina.com.cn/china/gncj/2018-07-23/doc-ihftenhy7877533.shtml" target="_blank">问题疫苗完整视频曝光 每个案例都是家破人亡的悲剧</a></td><td>4</td><td>07-23 10:56</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hftenhy7877533' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://finance.sina.com.cn/stock/s/2018-09-26/doc-ihkmwytp2572398.shtml" target="_blank">世纪华通的盛大游戏:股东享受造富盛宴 投资者买单</a></td><td>4</td><td>09-26 15:47</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-hkmwytp2572398' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://finance.sina.com.cn/chanjing/cyxw/2018-09-22/doc-ifxeuwwr7252798.shtml" target="_blank">楼市取消预售制:中国抛弃香港模式 今后再无地产大佬</a></td><td>4</td><td>09-22 21:41</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=cj&newsid=comos-fxeuwwr7252798' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->

<a name="8"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>娱乐新闻</h2>
		<ul class="Tabs">
			<li id="Tab81" class="on" onmouseover="chgTab(8,1)">点击量排行</li>
			<li id="Tab82" onmouseover="chgTab(8,2)">评论数排行</li>
			<li id="Tab83" onmouseover="chgTab(8,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://ent.sina.com.cn" target="_blank">更多娱乐新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con81">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.ent.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=ent_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=channel_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://ent.sina.com.cn/v/u/2024-10-30/doc-incuiaqz7991008.shtml" target="_blank">《绿灯侠》新剧定主演 凯尔·钱德勒加盟</a></td><td>新浪娱乐</td><td>10-30 12:53</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://ent.sina.com.cn/zz/2024-11-14/doc-incvzmwn4451628.shtml" target="_blank">《胜券在握》北京首映好评如潮 职场爽片群星力赞</a></td><td>新浪电影</td><td>11-14 17:01</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/f/2024-10-30/doc-incuiaqy1213212.shtml" target="_blank">玛格特·罗比主演新版《呼啸山庄》 老熟人再合作</a></td><td>新浪娱乐</td><td>10-30 12:50</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/f/2024-10-30/doc-incuiarc6973358.shtml" target="_blank">恐怖片《罪孽者》爆预告 双胞胎兄弟离奇遭遇</a></td><td>新浪娱乐</td><td>10-30 12:47</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/f/2024-10-30/doc-incuiaqy1210604.shtml" target="_blank">马提亚斯·修奈尔加盟《超女》 计划年底开机</a></td><td>新浪娱乐</td><td>10-30 12:41</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/c/2024-11-14/doc-incvzsek4364341.shtml" target="_blank">微博观影团《好东西》北京首映抢票</a></td><td>新浪娱乐</td><td>11-14 17:29</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/f/2023-04-27/doc-imyrukvf4483358.shtml" target="_blank">《蜘蛛侠：纵横宇宙》发布新海报 穿越多元宇宙</a></td><td>新浪娱乐</td><td>04-27 06:40</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/f/2023-04-27/doc-imyruepa2121356.shtml" target="_blank">新一部《异形》电影开拍 将是原创独立故事</a></td><td>新浪娱乐</td><td>04-27 05:36</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://ent.sina.com.cn/v/u/2023-04-27/doc-imyrvnhn1417911.shtml" target="_blank">《美国恐怖故事》第12季宣布 罗伯茨卡戴珊主演</a></td><td>新浪娱乐</td><td>04-27 18:07</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://ent.sina.com.cn/v/u/2024-10-30/doc-incuiaqy1201562.shtml" target="_blank">查理·汉纳姆主演《怪物》第三季 饰连环杀手</a></td><td>新浪娱乐</td><td>10-30 12:12</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con82" style="display:none;">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.ent.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=ylxwpl&amp;top_time=20241117&amp;top_show_num=30&amp;top_order=DESC&amp;js_var=channel_pl_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_pl_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_pl_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
	var match = new RegExp("match.sports.sina.com.cn");
	var url=MyNewsList['url'];
	if(match.test(url))
	 {
		continue;
	}
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/tv/slide_4_704_389657.html" target="_blank">组图：2024微博视界大会荣誉揭晓 刘亦菲获年度演员</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=slidenews-album-704-389657&amp;style=0" target="_blank">2,113</a></td><td>11-05 23:07</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=slidenews-album-704-389657&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://ent.sina.com.cn/y/ygangtai/2023-08-13/doc-imzhapfx5866380.shtml" target="_blank">张学友演唱会因头晕延迟开场 报平安称不用担心</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=comos-mzhapfx5866380&amp;style=0" target="_blank">8</a></td><td>08-13 23:59</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-mzhapfx5866380&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/y/slide_4_704_387915.html" target="_blank">组图：许光汉现身田馥甄演唱会 二人牵手合唱画面养眼</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=slidenews-album-704-387915&amp;style=0" target="_blank">4</a></td><td>08-12 21:02</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=slidenews-album-704-387915&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://ent.sina.com.cn/v/u/2024-10-30/doc-incuhrzm3884944.shtml" target="_blank">《异形：地球》剧集曝预告 定档明年开播</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=comos-ncuhrzm3884944&amp;style=0" target="_blank">4</a></td><td>10-30 07:10</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-ncuhrzm3884944&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/star/slide_4_704_387839.html" target="_blank">组图：李现向社会福利基金会捐款100万 购买物资已运抵黑龙江灾区</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=slidenews-album-704-387839&amp;style=0" target="_blank">3</a></td><td>08-09 12:41</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=slidenews-album-704-387839&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://ent.sina.com.cn/m/f/2024-10-30/doc-incuhrzi7100854.shtml" target="_blank">《毒液3》曝新预告 共生体之神惊艳亮相</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=comos-ncuhrzi7100854&amp;style=0" target="_blank">3</a></td><td>10-30 06:35</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-ncuhrzi7100854&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://ent.sina.com.cn/s/h/2023-07-05/doc-imyzreks9947346.shtml" target="_blank">台媒曝炎亚纶NONO等加害人最重可判30年徒刑</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=comos-myzreks9947346&amp;style=0" target="_blank">2</a></td><td>07-05 11:54</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-myzreks9947346&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://ent.sina.com.cn/k/2023-08-09/doc-imzfqvxw0849706.shtml" target="_blank">SMTOWN回应边伯贤合约到期 不知道他开公司</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=comos-mzfqvxw0849706&amp;style=0" target="_blank">2</a></td><td>08-09 16:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-mzfqvxw0849706&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/star/slide_4_704_387854.html" target="_blank">组图：对王一博言论不满？大鹏回应抿嘴被解读呼吁“少玩儿我”</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=slidenews-album-704-387854&amp;style=0" target="_blank">2</a></td><td>08-09 19:32</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=slidenews-album-704-387854&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/film/slide_4_704_387933.html" target="_blank">组图：《封神》剧组曾向蓝天救援队捐赠物资 携手共渡难关</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&amp;newsid=slidenews-album-704-387933&amp;style=0" target="_blank">2</a></td><td>08-13 14:46</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=slidenews-album-704-387933&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con83" style="display:none;">
		<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.ent.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfyl&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfyl_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://ent.sina.com.cn/y/ygangtai/2018-11-19/doc-ihnyuqhi3096424.shtml" target="_blank">邓紫棋回应入选BBC百大女性：太受宠若惊了</a></td><td>5</td><td>11-19 18:36</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-hnyuqhi3096424' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://ent.sina.com.cn/s/h/2018-11-20/doc-ihnyuqhi4857615.shtml" target="_blank">胡杏儿公开回应怀二胎传闻：有喜讯会说</a></td><td>5</td><td>11-20 18:15</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-hnyuqhi4857615' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://ent.sina.com.cn/m/c/2017-01-17/doc-ifxzqnim4760584.shtml" target="_blank">王丽坤《西游》露真容  蜘蛛精成撩汉担当</a></td><td>4</td><td>01-17 11:03</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-fxzqnim4760584' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://video.sina.com.cn/p/ent/doc/2016-12-28/103065456277.html" target="_blank" class="videoNewsLeft">外媒评2016世界最美面孔 林允力压刘亦菲baby</a></td><td>4</td><td>12-28 10:30</td><!-- <td><a href='http://t.cn/RIRD6Pk' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://video.sina.com.cn/p/ent/w/doc/2017-02-05/162765640069.html" target="_blank" class="videoNewsLeft">霉霉Taylor Swift预热演唱会首唱《五十度黑》原声新单</a></td><td>4</td><td>02-05 16:27</td><!-- <td><a href='http://t.cn/RJh5ALg' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://ent.sina.com.cn/s/m/2016-11-07/doc-ifxxneua4355403.shtml" target="_blank">专访刘昊然：我才19岁 只想好好“集邮”</a></td><td>4</td><td>11-07 18:44</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-fxxneua4355403' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://ent.sina.com.cn/s/m/2016-03-06/doc-ifxqafha0413900.shtml" target="_blank">谭晶提案：公共场所建母婴室</a></td><td>4</td><td>03-06 20:09</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-fxqafha0413900' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://ent.sina.com.cn/m/c/2017-02-06/doc-ifyafcyx7172753.shtml" target="_blank">《南京！南京！》入选21世纪华语电影十大</a></td><td>4</td><td>02-06 16:17</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-fyafcyx7172753' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://slide.ent.sina.com.cn/star/slide_4_704_160540.html" target="_blank">余文乐健硕肌肉首曝光 性感半裸秀腹肌线条(图)</a></td><td>4</td><td>01-01 08:00</td><!-- <td><a href='http://t.cn/RM4DJYB' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://ent.sina.com.cn/v/h/2017-02-06/doc-ifyafcyw0371775.shtml" target="_blank">台演员高鸣今上吊自杀 曾自称5种癌症缠身</a></td><td>4</td><td>02-06 11:56</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=yl&newsid=comos-fyafcyw0371775' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->


<a name="7"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>科技新闻</h2>
		<ul class="Tabs">
			<li id="Tab61" class="on" onmouseover="chgTab(6,1)">点击量排行</li>
			<li id="Tab62" onmouseover="chgTab(6,2)">评论数排行</li>
			<li id="Tab63" onmouseover="chgTab(6,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://tech.sina.com.cn" target="_blank">更多科技新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con61">
			<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.tech.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=tech_news_suda&amp;top_time=20241117&amp;top_show_num=20&amp;top_order=DESC&amp;js_var=channel_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
	  
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwfyae8542129.shtml" target="_blank">业界唯一！华为数据湖打通孤岛 让数据永远在线</a></td><td>快科技</td><td>11-17 01:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwftuk5431391.shtml" target="_blank">华为新专利公开！攻克硅基负极电池体积膨胀难题</a></td><td>快科技</td><td>11-17 01:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwfpnn5514072.shtml" target="_blank">11月26四款齐发 还有折叠新机X6！网友曝光Mate 70售价：华为不打算涨价</a></td><td>快科技</td><td>11-17 01:06</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwfpnn5490104.shtml" target="_blank">华为李小龙：华为手机无密码完全无法获取数据！拆“内存”都不行</a></td><td>快科技</td><td>11-17 01:01</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/it/2022-12-09/doc-imxwarir1654855.shtml" target="_blank">理想汽车人事变动！李想直接管销售服务业务，马东辉接替沈亚楠任总裁</a></td><td>市场资讯</td><td>12-09 20:04</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/internet/2022-12-09/doc-imqmmthc7527372.shtml" target="_blank">Lululemon Q3营收同比增长28% Q4指引不及预期</a></td><td>智通财经APP</td><td>12-09 07:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/internet/2022-12-09/doc-imqqsmrp9063312.shtml" target="_blank">好市多 Q1营收不及预期 净销售额同比增8%</a></td><td>智通财经APP</td><td>12-09 07:38</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/internet/2022-12-09/doc-imxwavrm2960926.shtml" target="_blank">SEC建议：美国公司应向投资者披露对加密资产市场的风险敞口</a></td><td>界面新闻</td><td>12-09 20:16</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/it/2022-12-09/doc-imxwarir1645192.shtml" target="_blank">三星电子第三季度晶圆代工全球市占率15.5%</a></td><td>财联社</td><td>12-09 19:38</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/it/2022-12-09/doc-imxwavrm2962271.shtml" target="_blank">一图看懂蔚来、小鹏、理想汽车2022年第三季度财报</a></td><td>新浪科技</td><td>12-09 20:26</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con62" style="display:none;">
				<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.tech.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=kjxwpl&amp;top_time=20241117&amp;top_show_num=30&amp;top_order=DESC&amp;js_var=channel_pl_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_pl_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_pl_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
	var match = new RegExp("match.sports.sina.com.cn");
	var url=MyNewsList['url'];
	if(match.test(url))
	 {
		continue;
	}
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/roll/2024-11-13/doc-incvxhxk8057525.shtml" target="_blank">贾跃亭：已与顶级主机厂达成合作 确保FX车型明年下线</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncvxhxk8057525&amp;style=0" target="_blank">103</a></td><td>11-13 18:08</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncvxhxk8057525&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://k.sina.com.cn/article_1649679354_m62541bfa03301jsp6.html" target="_blank">喇叭能拆？抢先实拍比亚迪夏（内饰/三排/行李厢）</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwckpn6376261&amp;style=0" target="_blank">77</a></td><td>11-15 11:37</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwckpn6376261&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/roll/2024-11-16/doc-incwfyak2234196.shtml" target="_blank">“学校有钱建草堂没钱修宿舍”回应还有疑问待解 | 新京报快评</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwfyak2234196&amp;style=0" target="_blank">69</a></td><td>11-16 15:00</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwfyak2234196&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwkiie4158386.shtml" target="_blank">毫无原因 苹果突然退款！用户收到AppleCare+服务费</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwkiie4158386&amp;style=0" target="_blank">69</a></td><td>11-17 16:04</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwkiie4158386&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/roll/2024-11-16/doc-incwheke5207539.shtml" target="_blank">中国能出一个“马斯克”吗 马斯克本人回应</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwheke5207539&amp;style=0" target="_blank">66</a></td><td>11-16 17:19</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwheke5207539&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-16/doc-incwhqxy5015500.shtml" target="_blank">肯德基多门店下架预制产品 工作人员：可能是因为销量不太好</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwhqxy5015500&amp;style=0" target="_blank">59</a></td><td>11-16 23:19</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwhqxy5015500&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/digi/2024-11-17/doc-incwismn4467059.shtml" target="_blank">小鹏汇天“陆地航母”2024 广州车展完成全球公开载人首飞</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwismn4467059&amp;style=0" target="_blank">56</a></td><td>11-17 10:07</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwismn4467059&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwfpnn5490104.shtml" target="_blank">华为李小龙：华为手机无密码完全无法获取数据！拆“内存”都不行</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwfpnn5490104&amp;style=0" target="_blank">38</a></td><td>11-16 10:19</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwfpnn5490104&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/roll/2024-11-12/doc-incvvcyi2087609.shtml" target="_blank">明年法定假期将增加2天，同程旅行春节机票搜索暴涨3倍</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncvvcyi2087609&amp;style=0" target="_blank">38</a></td><td>11-12 18:18</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncvvcyi2087609&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://finance.sina.com.cn/tech/discovery/2024-11-17/doc-incwfpnn5514072.shtml" target="_blank">11月26四款齐发 还有折叠新机X6！网友曝光Mate 70售价：华为不打算涨价</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&amp;newsid=comos-ncwfpnn5514072&amp;style=0" target="_blank">35</a></td><td>11-16 11:39</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-ncwfpnn5514072&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con63" style="display:none;">
			<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.tech.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfkj&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfkj_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2018-06-04/doc-ihcmurvh2536861.shtml" target="_blank">小黄车快黄了？有员工称形势严峻 应该是实在没钱了</a></td><td>17</td><td>06-04 08:50</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-hcmurvh2536861' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2018-06-05/doc-ihcmurvh9534696.shtml" target="_blank">美团推小象生鲜 围猎新零售市场</a></td><td>15</td><td>06-05 06:09</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-hcmurvh9534696' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://tech.sina.com.cn/e/2018-06-05/doc-ihcmurvh9754300.shtml" target="_blank">董明珠投资银隆或遭忽悠 能否二次连任决定银隆生死</a></td><td>9</td><td>06-05 07:24</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-hcmurvh9754300' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://tech.sina.com.cn/soft/news/2000-06-30/29482.shtml" target="_blank">新浪全新即时信息软件-“新浪点点通”</a></td><td>6</td><td>06-30 16:53</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=2-1-29482' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://tech.sina.com.cn/2018-06-03/doc-ihcmurvf6507022.shtml" target="_blank">无需做手术也能治好近视！今年内将开展临床试验</a></td><td>6</td><td>06-03 06:50</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-hcmurvf6507022' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2018-05-29/doc-ihcffhsu7211342.shtml" target="_blank">优酷宣布拿下2018世界杯直播权 央视此前曾称不分销</a></td><td>5</td><td>05-29 08:39</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-hcffhsu7211342' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2015-12-16/doc-ifxmszek7154062.shtml" target="_blank">网易下一步建立影视子公司？丁磊未予回应</a></td><td>5</td><td>12-16 08:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-fxmszek7154062' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2015-11-13/doc-ifxksqku2970537.shtml" target="_blank">申通董事长：要让洋快递感到压力</a></td><td>5</td><td>11-13 23:19</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-fxksqku2970537' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2015-12-16/doc-ifxmpnqm3350165.shtml" target="_blank">滴滴出行CEO程维：柳青是一位很有理想的人</a></td><td>5</td><td>12-16 16:29</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-fxmpnqm3350165' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://tech.sina.com.cn/i/2018-06-04/doc-ihcmurvh8228353.shtml" target="_blank">苹果及亚马逊盘中刷新历史新高，亚马逊市值破8000亿</a></td><td>5</td><td>06-04 22:52</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=kj&newsid=comos-hcmurvh8228353' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->


<a name="9"></a>
<!-- loopblk begin -->
<div class="loopblk">
	<!-- 标题栏 begin -->
	<div class="lbti">
		<h2>军事新闻</h2>
		<ul class="Tabs">
			<li id="Tab91" class="on" onmouseover="chgTab(9,1)">点击量排行</li>
			<li id="Tab92" onmouseover="chgTab(9,2)">评论数排行</li>
			<li id="Tab93" onmouseover="chgTab(9,3)">分享数排行</li>
		</ul>
		<span class="caplink"><a href="http://mil.news.sina.com.cn" target="_blank">更多军事新闻&gt;&gt;</a></span>
	</div>
	<!-- 标题栏 end -->
	<div class="Cons" id="Con91">

		<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">媒体</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=news_mil_suda&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=channel_"> 
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {

   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_.data[i];

	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
    MyNewsList['comment_url'] = MyNewsList['comment_url'].replace("http://comment4.news.sina.com.cn/comment/comment4.html", "http://comment5.news.sina.com.cn/comment/skin/default.html");
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td>"+MyNewsList['media'].replace(/_稿费|微天下/g,'')+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");

 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwczme6193008.shtml" target="_blank">中国国防部：菲方勾连域外国家破坏南海和平稳定</a></td><td>中国新闻网</td><td>11-15 19:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwczmk0033605.shtml" target="_blank">中国国防部：美国是太空安全的最大威胁</a></td><td>中国新闻网</td><td>11-15 19:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcvch6260896.shtml" target="_blank">中国航展的“国际范”</a></td><td>中国新闻网</td><td>11-15 16:55</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwczme6192987.shtml" target="_blank">国防部：民进党当局“倚外谋独”只会将台湾推向兵凶战危</a></td><td>中国新闻网</td><td>11-15 19:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcvce9494497.shtml" target="_blank">国防部：中新（加坡）举行“合作-2024”陆军联合训练</a></td><td>国防部网</td><td>11-15 17:15</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2022-01-11/doc-ikyamrmz4554183.shtml" target="_blank">美在欧洲部署先进武器？俄警告：可能作出军事回应</a></td><td>参考消息</td><td>01-11 17:45</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcvce9478936.shtml" target="_blank">中国海警局南海分局组织英模和一线代表参观中国航展</a></td><td>中国新闻网</td><td>11-15 16:45</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2022-02-25/doc-imcwipih5299784.shtml" target="_blank">欧盟称将对俄实施新的最严厉制裁 涉经济和政治精英</a></td><td>中国新闻网</td><td>02-25 13:10</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2022-03-15/doc-imcwiwss6115004.shtml" target="_blank">俄驻美大使：望美媒停止煽动恐俄情绪 公正报道</a></td><td>海外网</td><td>03-15 11:20</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcqvq0226928.shtml" target="_blank">国防部介绍中非和平安全论坛军事医学专题会议情况</a></td><td>中国新闻网</td><td>11-15 15:35</td><!-- <td><a href='' target='_blank'>发表评论</a></td> --></tr> 

</tbody></table>

	</div>
	<div class="Cons" id="Con92" style="display:none;">
				<table cellspacing="0">
		<tbody><tr>
		<th width="55">序号</th>
		<th>新闻标题</th>
		<th width="162">评论数</th>
		<th width="109">时间</th>
		<!-- <th width="102">发表评论</th> -->
		</tr>
<!-- 列表 begin -->
   <script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=jsxwpl&amp;top_time=20241117&amp;top_show_num=30&amp;top_order=DESC&amp;js_var=channel_pl_">	
   //src的参数为排行系统生成的js链接(这里是每天，出国，取十条，顺序的取值，返回值是studyaboard_1_data是的取值链接)
 </script>

  <script language="JavaScript">
  var MyNewsList;
  var i;
  var j=0;
  var sCnt=0;
  var eCnt=0;
  var fCnt=0
  var MyNews = new Object();
  for (i in channel_pl_.data) //studyaboard_1_data.data这个参数为链接返回值那个对象的名称
 {
   
   if(j>9)
	{
	   break;
   }
   MyNewsList=channel_pl_.data[i];
		
	var month = MyNewsList['create_date'].split("-")[1];
	var day = MyNewsList['create_date'].split("-")[2];
    var hour= MyNewsList['create_time'].split(":")[0];
    var min= MyNewsList['create_time'].split(":")[1];
	var match = new RegExp("match.sports.sina.com.cn");
	var url=MyNewsList['url'];
	if(match.test(url))
	 {
		continue;
	}
     j++;
    document.write("<tr><td>"+j+"</td><td class='ConsTi'><a href='"+MyNewsList['url']+"' target='_blank' "+">"+MyNewsList['title']+"</a></td><td><a href='"+MyNewsList['comment_url']+"' target='_blank'>"+MyNewsList['top_num']+"</a></td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+MyNewsList['comment_url']+"' target='_blank'>发表评论</a></td> --></tr>");
    
 //可以在这里对列表样式对应设置一般都是<li>...</li>
   }
</script><tr><td>1</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcvcn0126247.shtml" target="_blank">俄媒：俄军在哈尔科夫战线取得重大突破</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-ncwcvcn0126247&amp;style=0" target="_blank">72</a></td><td>11-15 17:05</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-ncwcvcn0126247&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcqvq0224499.shtml" target="_blank">俄媒报道俄侦察兵传奇战斗经历</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-ncwcqvq0224499&amp;style=0" target="_blank">62</a></td><td>11-15 15:27</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-ncwcqvq0224499&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwczme6193008.shtml" target="_blank">中国国防部：菲方勾连域外国家破坏南海和平稳定</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-ncwczme6193008&amp;style=0" target="_blank">57</a></td><td>11-15 19:35</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-ncwczme6193008&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcvcn0126231.shtml" target="_blank">珠海航展见证中国航发技术进步</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-ncwcvcn0126231&amp;style=0" target="_blank">19</a></td><td>11-15 17:05</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-ncwcvcn0126231&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwczmk0033605.shtml" target="_blank">中国国防部：美国是太空安全的最大威胁</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-ncwczmk0033605&amp;style=0" target="_blank">16</a></td><td>11-15 19:35</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-ncwczmk0033605&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2023-01-11/doc-imxzvcvm8995946.shtml" target="_blank">热销第一，英国王室坐不住了！</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-mxzvcvm8995946&amp;style=0" target="_blank">2</a></td><td>01-11 16:44</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-mxzvcvm8995946&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2022-09-14/doc-imqmmtha7289893.shtml" target="_blank">普京任命，中方欢迎</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-mqmmtha7289893&amp;style=0" target="_blank">1</a></td><td>09-14 16:24</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-mqmmtha7289893&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2022-09-14/doc-imqqsmrn9023462.shtml" target="_blank">“这可能是美国有史以来第一次”</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-mqqsmrn9023462&amp;style=0" target="_blank">1</a></td><td>09-14 11:02</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-mqqsmrn9023462&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/2022-09-14/doc-imqqsmrn9036210.shtml" target="_blank">美媒：英联邦自治运动打开闸门</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-mqqsmrn9036210&amp;style=0" target="_blank">1</a></td><td>09-14 12:40</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-mqqsmrn9036210&style=0' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="https://mil.news.sina.com.cn/zonghe/2024-11-15/doc-incwcqvn3447436.shtml" target="_blank">深山里的坚守：火箭军某连温暖氛围的力量</a></td><td><a href="http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&amp;newsid=comos-ncwcqvn3447436&amp;style=0" target="_blank">1</a></td><td>11-15 15:27</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=comos-ncwcqvn3447436&style=0' target='_blank'>发表评论</a></td> --></tr> 
</tbody></table>
<!-- 列表 end -->

	</div>
	<div class="Cons" id="Con93" style="display:none;">
			<table cellspacing="0">
  <tbody><tr>
    <th width="55">序号</th>
    <th>新闻标题</th>
    <th width="162">分享数</th>
    <th width="109">时间</th>
    <!-- <th width="102">发表评论</th> -->
  </tr>
<script>
function showContent(data_arr) {

  data = data_arr['data'];
  var nu = 0;
  for(var i in data){
    ++nu;
    data[i].create_date

	  var month = data[i].create_date.split("-")[1];
	  var day   = data[i].create_date.split("-")[2];
    var hour  = data[i].create_time.split(":")[0];
    var min   = data[i].create_time.split(":")[1]; 

    var videoNewsLeft = '';
    if (data[i].url.match(/http:\/\/video.sina.com.cn/)) {
      videoNewsLeft = ' class="videoNewsLeft"';
    }

	  document.write("<tr><td>"+nu+"</td><td class='ConsTi'><a href='"+data[i].url+"' target='_blank' "+videoNewsLeft+">"+data[i].title.replace(/视频:|视频：|视频-/g, '')+"</a></td><td>"+data[i].top_num+"</td><td>"+month+"-"+day+" "+hour+":"+min+"</td><!-- <td><a href='"+data[i].comment_url+"' target='_blank'>发表评论</a></td> --></tr>");
  }
}
</script>
<script type="text/javascript" src="//top.news.sina.com.cn/ws/GetTopDataList.php?top_type=day&amp;top_cat=wbrmzfjsxw&amp;top_time=20241117&amp;top_show_num=10&amp;top_order=DESC&amp;js_var=wbrmzfjsxw_1_data&amp;call_back=showContent"></script><tr><td>1</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2014-09-17/1124801656.html" target="_blank">甲午大东沟海战中日两国舰队的阵型问题</a></td><td>5</td><td>09-17 11:24</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-801656' target='_blank'>发表评论</a></td> --></tr><tr><td>2</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-02-28/0721716991.html" target="_blank">国家海洋局否认海监船用机枪瞄准日本渔船</a></td><td>5</td><td>02-28 07:21</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-716991' target='_blank'>发表评论</a></td> --></tr><tr><td>3</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2015-09-03/1154838388.html" target="_blank">阅兵前5艘中国海军舰艇出现在阿拉斯加</a></td><td>4</td><td>09-03 11:54</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-838388' target='_blank'>发表评论</a></td> --></tr><tr><td>4</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-02-28/0847716999.html" target="_blank">美智库称美日对中国失去耐心 解放军未准备交锋</a></td><td>4</td><td>02-28 08:47</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-716999' target='_blank'>发表评论</a></td> --></tr><tr><td>5</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-02-21/1019716269.html" target="_blank">台将从两岸最前线撤军 被批门户洞开造成恐慌</a></td><td>4</td><td>02-21 10:19</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-716269' target='_blank'>发表评论</a></td> --></tr><tr><td>6</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-02-26/1005716766.html" target="_blank">韩媒称美日韩派间谍在中朝边境争朝核爆区泥土</a></td><td>4</td><td>02-26 10:05</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-716766' target='_blank'>发表评论</a></td> --></tr><tr><td>7</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-11-26/0836751334.html" target="_blank">媒体称中国已做好最坏准备 若开战将对日清算</a></td><td>4</td><td>11-26 08:36</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-751334' target='_blank'>发表评论</a></td> --></tr><tr><td>8</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-11-19/0737750187.html" target="_blank">俄称对华武器供应已减少数倍 无法恢复昔日辉煌</a></td><td>4</td><td>11-19 07:37</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-750187' target='_blank'>发表评论</a></td> --></tr><tr><td>9</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-10-31/1529747241.html" target="_blank">中国军方就日舰强闯我演习区滞留3天提出交涉</a></td><td>4</td><td>10-31 15:29</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-747241' target='_blank'>发表评论</a></td> --></tr><tr><td>10</td><td class="ConsTi"><a href="http://mil.news.sina.com.cn/2013-11-27/1028751551.html" target="_blank">日美外长就合作应对中国防空识别区达成一致</a></td><td>4</td><td>11-27 10:28</td><!-- <td><a href='http://comment5.news.sina.com.cn/comment/skin/default.html?channel=jc&newsid=27-1-751551' target='_blank'>发表评论</a></td> --></tr>
</tbody></table>

	</div>
</div>
<!-- loopblk end -->
	
<!-- 说明 begin -->
<div id="shm">本页面为实时新闻排行榜，点击量排行显示从当前时间起24小时内各频道新闻浏览量最高的排行情况，每小时更新一次；<br>
评论数排行显示从当前时间起24小时内各频道新闻评论数量最高的排行情况，每小时更新一次。</div>
<!-- 说明 end -->

<!--开始：底部-->
<hr style="width:950px;">
<!--开始：底部-->
<!--开始：底部-->
<table width="950" border="0" cellspacing="0" cellpadding="0">
<tbody><tr><td align="center">
<br>
<a href="/guest.html" target="_blank">新闻中心意见反馈留言板</a>　电话：010-82612286　　<a href="http://tech.sina.com.cn/sinahelp/2003-10-28/113/314.html" target="_blank">新浪网产品客户服务联系电话</a>　　欢迎批评指正
<br>
<br>
<a href="http://corp.sina.com.cn/chn/" class="sinatail">新浪简介</a> | 
<a href="http://corp.sina.com.cn/eng/" class="sinatail">About Sina</a> | 
<a href="http://ads.sina.com.cn/" class="sinatail">广告服务</a> | 
<a href="http://www.sina.com.cn/contactus.html">联系我们</a> | <a href="http://corp.sina.com.cn/chn/sina_job.html" class="sinatail">招聘信息</a> | 
<a href="http://www.sina.com.cn/intro/lawfirm.shtml" class="sinatail">网站律师</a> | 
<a href="http://english.sina.com" class="sinatail">SINA English</a> | 
<a href="https://login.sina.com.cn/cgi/register/reg_sso.php" class="sinatail">会员注册</a>  | 
<a href="http://help.sina.com.cn/" class="sinatail">产品答疑</a>
<br><br>Copyright © 1996-2024 SINA Corporation, All Rights Reserved
<br>
<br>新浪公司 <a href="http://www.sina.com.cn/intro/copyright.shtml" class="sinatail">版权所有</a>
<br>北京市通信公司提供网络带宽
</td></tr>
</tbody></table>
<!--结束：底部-->

<!--结束：底部-->
<!--seo内容输出开始-->
<div style="position:absolute;left:-9999px;height:1px;width:1px;overflow:hidden;">
	
	
	
</div>
<!--seo内容输出结束-->
<script type="text/javascript" src="//www.sinaimg.cn/unipro/pub/suda_m_v629.js"></script>
<script type="text/javascript">suds_init(2212,100.0000,965,2);</script>

</div>
<script src="https://pluto.sina.cn/gk/match?id=1"></script><!-- body code begin -->

<!-- SSO_UPDATECOOKIE_START -->
<script type="text/javascript">var sinaSSOManager=sinaSSOManager||{};sinaSSOManager.q=function(b){if(typeof b!="object"){return""}var a=new Array();for(key in b){a.push(key+"="+encodeURIComponent(b[key]))}return a.join("&")};sinaSSOManager.es=function(f,d,e){var c=document.getElementsByTagName("head")[0];var a=document.getElementById(f);if(a){c.removeChild(a)}var b=document.createElement("script");if(e){b.charset=e}else{b.charset="gb2312"}b.id=f;b.type="text/javascript";d+=(/\?/.test(d)?"&":"?")+"_="+(new Date()).getTime();b.src=d;c.appendChild(b)};sinaSSOManager.doCrossDomainCallBack=function(a){sinaSSOManager.crossDomainCounter++;document.getElementsByTagName("head")[0].removeChild(document.getElementById(a.scriptId))};sinaSSOManager.crossDomainCallBack=function(a){if(!a||a.retcode!=0){return false}var d=a.arrURL;var b,f;var e={callback:"sinaSSOManager.doCrossDomainCallBack"};sinaSSOManager.crossDomainCounter=0;if(d.length==0){return true}for(var c=0;c<d.length;c++){b=d[c];f="ssoscript"+c;e.scriptId=f;b=b+(/\?/.test(b)?"&":"?")+sinaSSOManager.q(e);sinaSSOManager.es(f,b)}};sinaSSOManager.updateCookieCallBack=function(c){var d="ssoCrossDomainScriptId";var a="http://login.sina.com.cn/sso/crossdomain.php";if(c.retcode==0){var e={scriptId:d,callback:"sinaSSOManager.crossDomainCallBack",action:"login",domain:"sina.com.cn"};var b=a+"?"+sinaSSOManager.q(e);sinaSSOManager.es(d,b)}else{}};sinaSSOManager.updateCookie=function(){var g=1800;var p=7200;var b="ssoLoginScript";var h=3600*24;var i="sina.com.cn";var m=1800;var l="http://login.sina.com.cn/sso/updatetgt.php";var n=null;var f=function(e){var r=null;var q=null;switch(e){case"sina.com.cn":q=sinaSSOManager.getSinaCookie();if(q){r=q.et}break;case"sina.cn":q=sinaSSOManager.getSinaCookie();if(q){r=q.et}break;case"51uc.com":q=sinaSSOManager.getSinaCookie();if(q){r=q.et}break}return r};var j=function(){try{return f(i)}catch(e){return null}};try{if(g>5){if(n!=null){clearTimeout(n)}n=setTimeout("sinaSSOManager.updateCookie()",g*1000)}var d=j();var c=(new Date()).getTime()/1000;var o={};if(d==null){o={retcode:6102}}else{if(d<c){o={retcode:6203}}else{if(d-h+m>c){o={retcode:6110}}else{if(d-c>p){o={retcode:6111}}}}}if(o.retcode!==undefined){return false}var a=l+"?callback=sinaSSOManager.updateCookieCallBack";sinaSSOManager.es(b,a)}catch(k){}return true};sinaSSOManager.updateCookie();</script>
<!-- SSO_UPDATECOOKIE_END -->

<!-- body code end -->


</body></html>