# !/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 0:17
# <AUTHOR> shaocanfan
# @File    : news_summary.py
# @explain : 获取新闻话题


# ----------------------------------------- 每日简报 -------------------------------------------------
# import requests
# import json
# url = 'https://whyta.cn/api/tx/bulletin?key=ea15387c6657'
#
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
#     'Content-Type': 'application/json'
# }
#
# response = requests.get(url, headers=headers)
# context = response.text
# json_data = json.loads(context)
#
# title = [group['title'] for group in json_data['result']['list']]
#
# print(title)
# ----------------------------------------- 每日简报 -------------------------------------------------

# ----------------------------------------- 抖音 ----------------------------------------------------

# import requests
# import json
# url = 'https://whyta.cn/api/tx/douyinhot?key=ea15387c6657'
#
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
#     'Content-Type': 'application/json'
# }
#
# response = requests.get(url, headers=headers)
# context = response.text
# json_data = json.loads(context)
#
# the_context = []
#
# for the_list in json_data['result']['list']:
#     the_context.append(the_list['word'])
#
# print(rf'{the_context}')

# ----------------------------------------- 抖音 ----------------------------------------------------


# ----------------------------------------- 微博 ----------------------------------------------------

# import requests
# import json
# url = 'https://whyta.cn/api/tx/weibohot?key=ea15387c6657'
#
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
#     'Content-Type': 'application/json'
# }
#
# response = requests.get(url, headers=headers)
# context = response.text
# json_data = json.loads(context)
#
# the_context = []
#
# for the_list in json_data['result']['list']:
#     the_context.append(the_list['hotword'])
#
# print(the_context)

# ----------------------------------------- 微博 ----------------------------------------------------


# ----------------------------------------- 今日头条 -------------------------------------------------

# import requests
# import json
# jinritoutiao_url = 'https://whyta.cn/api/tx/toutiaohot?key=ea15387c6657'
#
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
#     'Content-Type': 'application/json'
# }
#
# response = requests.get(url, headers=headers)
# context = response.text
# json_data = json.loads(context)
#
# title = [group['word'] for group in json_data['result']['list']]
#
# print(title)

# ----------------------------------------- 今日头条 -------------------------------------------------





# ----------------------------------------- 整合 ----------------------------------------------------
import requests
import json

meirijianbao_url = 'https://whyta.cn/api/tx/bulletin?key=ea15387c6657'  # 每日简报
douyin_url = 'https://whyta.cn/api/tx/douyinhot?key=ea15387c6657'  # 抖音
weibo_url = 'https://whyta.cn/api/tx/weibohot?key=ea15387c6657'  # 微博
jinritoutiao_url = 'https://whyta.cn/api/tx/toutiaohot?key=ea15387c6657' # 今日头条

all_url = [meirijianbao_url, douyin_url, weibo_url, jinritoutiao_url]

# 创建一个字典，将URLs映射到相应的数字
url_to_number = {
    'meirijianbao_url': 1,
    'douyin_url': 2,
    'weibo_url': 3,
    'jinritoutiao_url': 4
}


def search_url(all_url):
    meirijianbao_list = []
    douyin_list = []
    weibo_list = []
    jinritoutiao_list = []
    all_list = []

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        'Content-Type': 'application/json'
    }

    for url in all_url:
        response = requests.get(url, headers=headers)
        context = response.text
        json_data = json.loads(context)

        if url_to_number['meirijianbao_url'] == 1:
            if 'title' in json_data['result']['list'][0]:
                meirijianbao_list = [group['title'] for group in json_data['result']['list']]

        # if url_to_number['douyin_url'] == 2:
        #     if 'word' in json_data['result']['list'][0]:
        #         douyin_list = [group['word'] for group in json_data['result']['list']]

        if url_to_number['weibo_url'] == 3:
            if 'hotword' in json_data['result']['list'][0]:
                weibo_list = [group['hotword'] for group in json_data['result']['list']]

        if url_to_number['jinritoutiao_url'] == 4:
            if 'word' in json_data['result']['list'][0]:
                jinritoutiao_list = [group['word'] for group in json_data['result']['list']]

    all_list = meirijianbao_list + douyin_list + weibo_list + jinritoutiao_list
    all_data = {  # 把数据归类
        'meirijianbao': meirijianbao_list,
        'douyin': douyin_list,
        'weibo': weibo_list,
        'jinritoutiao': jinritoutiao_list
    }
    return all_list


if __name__ == '__main__':
    print(search_url(all_url))
# ----------------------------------------- 整合 ----------------------------------------------------




