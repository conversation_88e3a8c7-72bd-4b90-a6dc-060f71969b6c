# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2024/8/22 13:18
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : news_summary.py
# @explain : 爬取新闻

import requests
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
from webdriver_manager.microsoft import EdgeChromiumDriverManager

context_num = 0

class WebScraper:
    # def __init__(self, driver_path='msedgedriver.exe',headless=True):
    #     edge_option = Options()
    #     if headless:
    #         edge_option.add_argument("--headless")
    #         edge_option.add_argument("disable-gpu")  # 如果在无头模式下运行，需要禁用GPU
    #     self.driver = webdriver.Edge(service=Service(driver_path),options=edge_option)
    def __init__(self, headless=True):
        # 设置浏览器选项
        options = webdriver.EdgeOptions()
        options.use_chromium = True  # 确保使用基于 Chromium 的 Edge
        options.add_argument("disable-gpu")  # 如果在无头模式下运行，需要禁用GPU
        if headless:
            options.add_argument('--headless')
        self.driver = webdriver.Edge(service=Service(EdgeChromiumDriverManager().install()), options=options)


    def load_page(self, url, wait_time=20):
        # 加载页面
        self.driver.get(url)
        self.driver.implicitly_wait(wait_time)

    def get_page_source(self):
        # 获取页面html内容
        with open('crawl_news.html', 'w', encoding='utf-8') as f:
            f.write(self.driver.page_source)
        return self.driver.page_source

    def parse_html(self, page_source):
        # 解析HTML
        soup = BeautifulSoup(page_source,'lxml')
        return soup

    def extract_click(self, soup, click_id, num):
        self.data = soup.find('div', id=click_id).find('table', cellspacing="0").find('tbody').find_all('tr')[num].find('a').get(
            'href')
        return self.data

    def close_brower(self):
        # 关闭浏览器
        self.driver.quit()

def get_context(urls,min_num,max_num):
    with open('context_txt.txt', 'w', encoding='utf-8') as file:
        file.write('')
    global context_num
    headers = {
         'accept-encoding': "gzip, deflate, br, zstd",
         'accept-language': "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
         'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                       "Safari/537.36 Edg/*********",
         'accept': "*/*",
         'referer': "https://news.sina.com.cn",
         'authority': "news.sina.com.cn",
         'cache-control': "max-age=0",
         'Pragma': 'no-cache'
    }
    for url in urls:
        # print(rf'url:{split_url}')
        response = requests.get(url=url, headers=headers)
        response.encoding = 'utf-8'
        context = response.text
        soup = BeautifulSoup(context, 'lxml')
        section = soup.find('div', class_='article')

        if section is not None:
            paragraphs = section.find_all('p', class_='art_p') + section.find_all('p', attrs={
                'cms-style': 'font-L'}) + section.find_all('p')
            text = [p.get_text(strip=True) for p in paragraphs]
            string_data = ''.join(text)
            # print(type(string_data))

            if min_num <= len(string_data) <= max_num:
                # print(string_data)
                context_num = context_num + 1
                print(f'已经往context_txt.txt文件添加了{context_num}份新闻')
                with open('context_txt.txt', 'a', encoding='utf-8') as file:
                    file.write(string_data+'\n')

                # print(string_data)
            else:
                print(f'this paragraphs is more than {max_num}k or less than {min_num}k')
                pass
        else:
            print("Specified section not found.")

def get_context_listdata():
    with open('context_txt.txt', 'r', encoding='utf-8') as file:
        list_a = [line.strip() for line in file]  # 使用 strip() 去除换行符
    print('已经从context_txt.txt文件中获取并输出列表形式的内容')
    return list_a

if __name__ == '__main__':
    datalist = []
    scraper = WebScraper()
    scraper.load_page('https://news.sina.com.cn/hotnews/')
    page_source = scraper.get_page_source()
    # with open('crawl_news.html', 'r', encoding='utf-8') as f:
    #     page_source = f.read()

    soup = scraper.parse_html(page_source)

    for i in range(1,11):
        data = scraper.extract_click(soup,"Con11",i)
        datalist.append(data)
    # scraper.close_brower()
    print(datalist)
    get_context(datalist, 1000, 3000)
    print(get_context_listdata()[1])
    scraper.close_brower()






# -------------------------------------------- 备用 ---------------------------------------------------------------------
# import requests
# from bs4 import BeautifulSoup
#
# url = 'https://api.crawlbase.com/?token=mdPa4OZOqT-PHIr19Xnmkg&url=https://news.sina.com.cn/hotnews/'
#
# headers = {
#     'accept-encoding': "gzip, deflate, br",
#     'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
#     'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
#                   "Safari/537.36 Edg/*********",
#     'accept': "*/*",
#     'referer': "https://xw.qq.com/m/recommend/",
#     'authority': "pacaio.match.qq.com",
#     'cache-control': "no-cache",
#     'Pragma': 'no-cache'
# }
#
# response = requests.get(url=url, headers=headers)
# soup = BeautifulSoup(response.text,'lxml')
# data = soup.find('div', id='Con11').find('table', cellspacing="0").find('tbody').find_all('tr')[1].find('a').get('href')
# print(data)
# -------------------------------------------- 备用 ---------------------------------------------------------------------





# ------------------------------------------------- 原始版本 -------------------------------------------------------------
# import time
# from bs4 import BeautifulSoup
# import requests
# import json
#
# context_num = 0
#
# url = "https://finance.sina.cn/tech/2024-08-20/detail-inckiaqy0131968.d.html"
# headers = {
#     'accept-encoding': "gzip, deflate, br",
#     'accept-language': "zh-CN,zh;q=0.9,en;q=0.8",
#     'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
#                   "Safari/537.36 Edg/*********",
#     'accept': "*/*",
#     'referer': "https://xw.qq.com/m/recommend/",
#     'authority': "pacaio.match.qq.com",
#     'cache-control': "no-cache",
#     'Pragma': 'no-cache'
# }
#
#
# def get_all_pagedata(up_num):
#     response = requests.get(
#         f'https://feeds.sina.cn/api/v4/tianyi?action=1&up={up_num}&down=1&length=100&cre=tianyi&mod=wnews&statics=1&merge=5&language=zh-CN',
#         headers=headers) # mod=wtech是科技新闻
#     with open("new_html.json", 'w', encoding='utf-8') as file:
#         file.write(response.text)
#     print('get_all_pagedata is ok!')
#
#
# def get_urls():
#     with open("new_html.json", 'r', encoding='utf-8') as file:
#         url_data = json.load(file)
#
#     datas = url_data['result']['data']
#     urls = [url['base']['base']['url'] for url in datas]
#     print(urls)
#     # print('- - ' * 10)
#     return urls
#
#
# def get_context(urls):
#
#     global context_num
#     for url in urls:
#         # print(rf'原来的url:{url}')
#         split_url = url.split('?')[0]
#         # print(rf'分割后的url:{split_url}')
#         response = requests.get(url=split_url, headers=headers)
#         context = response.text
#         soup = BeautifulSoup(context, 'lxml')
#         section = soup.find('section', class_='art_pic_card art_content')
#
#         if section is not None:
#             paragraphs = section.find_all('p', class_='art_p') + section.find_all('p', attrs={
#                 'cms-style': 'font-L'}) + section.find_all('p')
#             text = [p.get_text(strip=True) for p in paragraphs]
#             string_data = ''.join(text)
#             # print(type(string_data))
#
#             if 1000 <= len(string_data) <= 3000:
#                 # print(string_data)
#                 context_num = context_num + 1
#                 print(f'已经往context_txt.txt文件添加了{context_num}份新闻')
#                 with open('context_txt.txt', 'w', encoding='utf-8') as file:
#                     file.write(string_data+'\n')
#
#                 # print(string_data)
#             else:
#                 print('this paragraphs is more than 3k or less than 1k')
#                 pass
#         else:
#             print("Specified section not found.")
#
# def get_context_listdata():
#     with open('context_txt.txt', 'r', encoding='utf-8') as file:
#         list_a = [line.strip() for line in file]  # 使用 strip() 去除换行符
#     print('已经从context_txt.txt文件中获取并输出列表形式的内容')
#     return list_a
#
#
# if __name__ == '__main__':
#     for i in range(1):
#         get_all_pagedata(i)
#         time.sleep(15)
#         url_list = get_urls()
#         get_context(url_list)
# ------------------------------------------------- 原始版本 -------------------------------------------------------------


