# !/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 0:17
# <AUTHOR> shaocanfan
# @File    : news_summary.py
# @explain : 汇总
import time

# import crawl_news, call_LLM, get_news_topics
# import os
# import smtplib
# import datetime
# from email.mime.text import MIMEText
# from email.mime.multipart import MIMEMultipart
#
#
# sender_email = '<EMAIL>'
# sender_key = '5109SmWYMHt4iwBf'
# receiver_email = ','.join([ '<EMAIL>','<EMAIL>'])
# context_body = 'Hello, 以下为您汇报ai生成的新闻~'
# filename_path = 'output_news.txt'
#
#
# def summary_context_list(topic, num):
#     datalist = []
#     scraper = crawl_news.WebScraper()
#     scraper.load_page('https://news.sina.com.cn/hotnews/')
#     scraper.get_page_source()
#     with open('crawl_news.html', 'r', encoding='utf-8') as f:
#         page_source = f.read()
#     soup = scraper.parse_html(page_source)
#     for i in range(1, 1 + num):
#         data = scraper.extract_click(soup, topic, i)
#         datalist.append(data)
#     scraper.close_brower()
#     print(datalist)
#     return datalist
#
#
# def send_email(sender_email, receiver_email, password, subject, filename):
#     msg = MIMEMultipart()
#     msg['From'] = sender_email
#     msg['To'] = receiver_email
#     msg['Subject'] = subject
#
#     with open('output_news.txt', 'r', encoding='utf-8') as f:
#         body = f.read()
#
#     # 邮件正文
#     msg.attach(MIMEText(body, 'plain'))
#
#     # 文件路径
#     filepath = os.path.join(os.getcwd(), filename)
#
#     # 检查文件是否存在
#     if not os.path.isfile(filepath):
#         print(f"Error: The file output_news.txt does not exist.")
#         return
#
#     # 附件
#     # with open(filepath, "rb") as attachment:
#     #     part = MIMEBase('application', 'octet-stream')
#     #     part.set_payload(attachment.read())
#     #     encoders.encode_base64(part)
#     #     part.add_header('Content-Disposition', f"attachment; filename= {os.path.basename(filename)}")
#     #     msg.attach(part)
#
#     # 每次发送都重新建立连接
#     try:
#         server = smtplib.SMTP_SSL('smtp.mxhichina.com', 465)
#         server.login(sender_email, password)
#         server.sendmail(sender_email, receiver_email, msg.as_string())
#         server.quit()
#         print("邮件已成功发送！")
#     except smtplib.SMTPException as e:
#         print(f"邮件发送失败：{e}")
#
#
# if __name__ == '__main__':
#     sends_num = 0
#     begin_send_data = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
#     con_lists = ['Con11', 'Con21', 'Con31', 'Con41', 'Con51']
#     con_topics = ['新闻总排行']
#     with open('output_news.txt', 'w', encoding='utf-8') as file:  # 打开output_news.txt将ai生成的内容写进去
#         file.write("开始写入：\n\n")
#
#     for con_list, con_topic in zip(con_lists, con_topics):
#         context_url_list = summary_context_list(con_list, 10)  # 类别，选十条新闻
#         crawl_news.get_context(context_url_list, 500, 3000)  # 获取内容的字数是1000-3000，并将其保存在context_txt.txt
#         context_list = crawl_news.get_context_listdata()  # 读取context_txt.txt文件，将每个新闻以列表的形式存放到 context_list 中
#         with open('output_news.txt', 'a', encoding='utf-8') as file:  # 打开output_news.txt将ai生成的内容写进去
#             file.write(f"**********{con_topic}**********：\n\n")
#         for i in range(1):  # 写两篇
#             call_LLM.use_llm(context_list[i])
#             send_data = datetime.datetime.now()
#             formatted_date = send_data.strftime("%Y-%m-%d %H:%M:%S")
#             subject_topic = rf'{formatted_date}时发送的新闻'
#             send_email(sender_email, receiver_email, sender_key, subject_topic, filename_path)
#             sends_num = sends_num+1
#             print(rf'第{sends_num}封邮件已完成！')
#         print("finish！")
#
#     finish_send_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
#     print(rf'the tiem from {begin_send_data} 到 {finish_send_time}')


# ----------------------------------------初始版本------------------------------------------------------------------------

import crawl_news, call_LLM, get_news_topics
import os
import smtplib
import datetime
from email import encoders
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

send_data = datetime.datetime.now()
formatted_date = send_data.strftime("%Y-%m-%d %H:%M:%S")

sender_email = '<EMAIL>'
sender_key = '5109SmWYMHt4iwBf'
receiver_email = '<EMAIL>'
subject_topic = rf'{formatted_date}时发送的新闻'
context_body = 'Hello, 以下为您汇报ai生成的新闻~'
filename_path = 'output_news.txt'


def summary_context_list(topic, num):
    datalist = []
    scraper = crawl_news.WebScraper()
    scraper.load_page('https://news.sina.com.cn/hotnews/')
    scraper.get_page_source()
    with open('crawl_news.html', 'r', encoding='utf-8') as f:
        page_source = f.read()
    soup = scraper.parse_html(page_source)
    for i in range(1, 1 + num):
        data = scraper.extract_click(soup, topic, i)
        datalist.append(data)
    scraper.close_brower()
    print(datalist)
    return datalist


def send_email(sender_email, receiver_email, password, subject, body, filename):
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = receiver_email
    msg['Subject'] = subject

    # 邮件正文
    msg.attach(MIMEText(body, 'plain'))

    # 文件路径
    filepath = os.path.join(os.getcwd(), filename)

    # 检查文件是否存在
    if not os.path.isfile(filepath):
        print(f"Error: The file output_news.txt does not exist.")
        return

    # 附件
    with open(filepath, "rb") as attachment:
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(attachment.read())
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', f"attachment; filename= {os.path.basename(filename)}")
        msg.attach(part)

    # 每次发送都重新建立连接
    try:
        server = smtplib.SMTP_SSL('smtp.mxhichina.com', 465)
        server.login(sender_email, password)
        server.sendmail(sender_email, receiver_email, msg.as_string())
        server.quit()
        print("邮件已成功发送！")
    except smtplib.SMTPException as e:
        print(f"邮件发送失败：{e}")


if __name__ == '__main__':
    # Con1x:总排行 Con2x:国内 Con3x:国际 Con4x:社会 Con5x:体育 Con6x:科技 Con7x:财经 Con8:娱乐 Con9x:军事
    # x=1点击量排行 x=2评论数排行

    begin_send_data = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    con_lists = ['Con41']
    con_topics = ['xx']
    with open('output_news.txt', 'w', encoding='utf-8') as file:  # 打开output_news.txt将ai生成的内容写进去
        file.write("开始写入：\n\n")

    for con_list, con_topic in zip(con_lists, con_topics):
        context_url_list = summary_context_list(con_list, 10)  # 类别，选十条新闻
        crawl_news.get_context(context_url_list, 500, 10000)  # 获取内容的字数是1000-3000，并将其保存在context_txt.txt
        context_list = crawl_news.get_context_listdata()  # 读取context_txt.txt文件，将每个新闻以列表的形式存放到 context_list 中
        with open('output_news.txt', 'a', encoding='utf-8') as file:  # 打开output_news.txt将ai生成的内容写进去
            file.write(f"**********{con_topic}**********：\n\n")
        for i in range(min(len(context_list), 2)):  # 写两篇
            call_LLM.use_llm(context_list[i])
            print('完成！', flush=True)
        print("finish！")

    send_email(sender_email, receiver_email, sender_key, subject_topic, context_body, filename_path)
    finish_send_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(rf'the tiem from {begin_send_data} 同{finish_send_time}')
# ----------------------------------------初始版本------------------------------------------------------------------------