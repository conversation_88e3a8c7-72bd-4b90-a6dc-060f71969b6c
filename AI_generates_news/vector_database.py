# !/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 0:17
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : news_summary.py
# @explain : 向量数据库

import pinecone
from pinecone import Pinecone
from sentence_transformers import SentenceTransformer

# 初始化 Pinecone
pc = Pinecone(api_key="659379e2-500c-4b2a-95f1-8fe07dfb3514")

# 检查是否存在索引，如果不存在，创建索引
index_name = "final-gen-news"
# 连接到索引
index = pc.Index(index_name)
# 加载模型
model = SentenceTransformer("D:\paraphrase-multilingual-MiniLM-L12-v2") # 维度为384

# 示例文本数据和其向量
json_data = {
    "type": "0171",
    "context": [
        {"id": 1, "text": "美国总统拜登宣布新的经济刺激计划。"},
        {"id": 2, "text": "中国股市今天大幅上涨。"},
        {"id": 3, "text": "小孩会游泳。"},
        {"id": 4, "text": "日本首相访问美国，讨论贸易问题。"},
        {"id": 5, "text": "女孩被老师打破头骨，老师当庭翻供"},
        {"id": 6, "text": "张根硕自曝1年前患甲状腺癌"}
    ]
}

# 文本向量化
texts = [item['text'] for item in json_data['context']]


vectors = model.encode(texts)

# 将数据与其 ID 上传到 Pinecone
for item, vector in zip(json_data['context'], vectors):
    index.upsert(vectors=[(str(item['id']), vector.tolist())] , namespace= "test1")

# 打印确认消息
print("Vectors are successfully uploaded to Pinecone.")

# 添加搜索功能
def search_by_keyword(keyword, top_k=4):
    # 将关键字转换为向量
    query_vector = model.encode(keyword)
    # print(query_vector)
    # 使用 Pinecone 进行查询，注意改用关键字参数
    query_results = index.query(namespace="test1", vector=query_vector.tolist(), top_k=top_k)
    return query_results


def retrieve_text_by_id(target_id):
    # 这里我们假设你已经连接到了索引
    # 从 Pinecone 索引中获取向量数据
    try:
        response = index.fetch(ids=[str(target_id)], namespace="test1")
        if response:
            # 从你的原始数据中查找对应的文本
            text = next((item['text'] for item in json_data['context'] if str(item['id']) == target_id), None)
            return text
        else:
            return "No data found for this ID."
    except Exception as e:
        return str(e)


def find_data():
    keywords = ["游泳", "金融", "生病","打架","工作", "股票"]
    for keyword in keywords:
        print(f"\nSearching for: '{keyword}'")
        search_results = search_by_keyword(keyword)
        print(rf'search_results:{search_results}')
        if search_results['matches']:
            print("Results:")
            for match in search_results['matches']:
                matched_id = match['id']
                matched_score = match['score']
                matched_text = next((item['text'] for item in json_data['context'] if str(item['id']) == matched_id), None)
                print(f"ID: {matched_id}, Score: {matched_score}, Text: {matched_text}")
        else:
            print("No matches found.")

if __name__ == '__main__':
    find_data()
    # text_content = retrieve_text_by_id('3')
    # print(f"Text for ID 3: {text_content}")

# ------------------------------------------------------------------------------------------------------------




