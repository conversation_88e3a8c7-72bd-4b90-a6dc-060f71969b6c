# !/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 0:17
# <AUTHOR> shaocanfan
# @File    : news_summary.py
# @explain : 调用llm

# ----------------------------------------- deepseek模型 --------------------------------------------
# import requests
# import json
#
# ai_context_num = 0

#
# def use_llm(neirong):
#     global ai_context_num
#
#     url = 'https://api.deepseek.com/chat/completions'
#     headers = {
#         'Authorization': 'Bearer sk-cafd52755ce447c7a276d28a89a76c95',
#         'Content-Type': 'application/json',
#     }
#     # neirong = ['你是谁','鲁迅为什么打周树人']
#     # neirong = [
#
#     data = {
#         'model': 'deepseek-chat',
#         'messages': [
#             # {
#             #     'role': 'system',
#             #     'content': '你是一位技艺精湛的新闻撰稿人，擅长编写引人入胜但不失真实性的新闻文章。你的作品既要忠实于提供的参考材料，又要在不歪'
#             #                '曲事实的前提下增添一些有助于理解的背景信息和细节，使文章更为完整和动人。现在，请你根据我提供的一篇新闻文章，首先总'
#             #                '结其要点，然后利用你的知识和文笔，创作一篇约100字的新闻文章。文章应该保持客观、准确且语句流畅,且不能偏离原文的本意，'
#             #                '分为一到两个段落。\n\n要求输出的格式如下：\n\n1、新闻要点总结：\n\n2、新闻文章撰写：\n标题：\n正文：（分为一段或者'
#             #                '两段）\n\n3、结语：'
#             # },
#             {
#                 'role': 'system',
#                 # 'content':'作为一位资深新闻撰稿人，您的使命是对现有新闻稿件进行精心提炼与创造性编辑，目标是制作出既引人入胜又严格忠于事实的新闻内容。首先，您需从文章中挖掘核心信息，提炼出精髓，随后利用您卓越的写作技艺和广博的知识底蕴，添加必要的背景信息，同时确保所有增添内容的真实性与原文相符。您的任务包括创作一个精炼的约50字新闻摘要，以及一篇全面、客观、流畅的新闻文章，该文章应保持中立视角，措辞严谨，句式通顺，避免人工智能语言特征的过度显现，紧密围绕原文中心思想展开论述，文章布局规划为一到两个逻辑清晰、衔接自然的段落。 ### 具体任务指引与输出格式： - **新闻摘要：** - 精准提炼出一个既吸引读者注意力又能准确反映全文核心的标题。 - 用不超过50字的篇幅，概括新闻的主要事件，确保摘要内容精炼且信息量饱满。 - **新闻正文：** - 首段应迅速抓住读者兴趣，用精练有力的文字概述新闻事件，确立文章的基本调性，并囊括所有关键信息要素。 - 在后续段落中（如需），逐步展开，添加背景细节，深化读者理解，同时保持叙述的客观性和文字的流畅性，确保文章整体结构紧凑，逻辑连贯。 请依据上述指导原则，结合提供的新闻文章素材，施展您的才华，开启创作之旅。'
#                 'content':'作为一位经验丰富的新闻撰稿专家，您的任务是将现有的新闻稿件进行提炼与艺术加工，使之既富有吸引力又保持内容的真实可靠性。这一过程涵盖两个核心步骤：首先提炼新闻的核心要点，随后运用您高超的叙事技巧与深厚的学识底蕴，增补精确的背景信息，确保新增内容与原文事实严格吻合。最终成果应包含一个精炼至50字以内的新闻摘要，以及一段精心编排的新闻正文，正文控制在200字左右，整体风格秉持客观公正，语言精练而生动，规避人工智能语言的生硬感，且每篇作品都应具备独特的视角和风格，紧贴原文主旨展开论述。 ### 具体创作指南及输出格式规范： 1. **新闻标题：** - 创拟一个标题，旨在瞬间吸引读者的同时，精准传达新闻的实质内容。 2. **新闻摘要：** - 编写一段不超过50字的摘要，全面覆盖新闻要点，确保每一关键信息得到体现。 3. **新闻正文段落：** - 在约200字的篇幅内，以凝练的文字勾勒出新闻事件的轮廓，确立文章的基本格调，确保核心信息鲜明突出。 - 融合关键背景信息，深化文章内涵与维度，同时维护文章结构的紧凑与阅读的顺畅性。 - 确保文章结构布局合理，逻辑连贯，文字表达符合新闻客观性，又蕴含文学感染力，避免格式化的叙述，让每篇文章都成为一次独特的阅读体验。 请遵循上述创作指南，基于提供的新闻素材，发挥您的创意与才华，精心打造这篇既真实又引人入胜的新闻佳作。 '
#             },
#             # {
#             #     'role': 'system', # 总结一篇文章的prompt
#             #     'content':'作为一位技艺高超的新闻撰稿专家，你的使命是基于给定的新闻素材，创作出既扣人心弦又忠于事实的新闻文章。这不仅要求你精准提炼文章要点，还要在尊重真相的基础上，巧妙融入补充背景与细节，以增强叙述的丰富性和吸引力，同时确保文章的每个字句都流露出真挚与自然，避免过度机械化的表达。 请遵循以下步骤完成此任务： ### 第一步：提炼要点 仔细阅读提供的新闻文章，概括其核心信息与关键细节，确保摘要内容精炼且全面。 ### 第二步：创作新闻文章 根据提炼的要点，运用你的写作技巧与深刻理解，撰写一篇大约800字的新闻报道。请注意以下几点： - **客观性与准确性**：文章必须严格依据原始素材，不得扭曲事实，所有陈述需有据可依。 - **流畅性与自然度**：文字表述应如行云流水，力求语言生动、贴近人类自然书写风格，避免人工智能生成文本的常见生硬感。 - **结构安排**：正文部分建议分为三至四段，每段各有侧重，逻辑清晰，逐步展开故事。 - **个人观点**：在保持客观的同时，适当在文章中融入你基于事实分析的合理观点，体现深度与独立思考。 ### 输出格式规范： 1. **新闻文章撰写**: - **标题**：（提炼文章核心，吸引读者注意） - **正文**： - 第一段：简要介绍新闻背景与核心事件，激发读者兴趣。 - 第二段：详细阐述事件经过，穿插必要背景信息与细节，增强叙述的饱满度。 - 第三段：分析事件影响，或涉及的相关方反应，展现多重视角。 - （可选第四段）：深入探讨事件背后的原因、趋势或潜在意义，提升文章深度。 - **结语**：总结全文，可适当提出引人深思的问题或简短评论，为文章画上圆满句号，同时鼓励读者反思或参与讨论。 请依据上述指南，结合提供的新闻素材，开始你的创作之旅。'
#             # },
#
#
#             {
#                 'role': 'user',
#                 'content': str(rf'{neirong}'),
#                 # 'content': str(rf"Hello!你是谁")
#             },
#
#         ],
#         'temperature': 0.2,
#         'max_tokens': 4096,
#     }
#
#     response = requests.post(url, headers=headers, json=data)
#     data = response.json()
#     txt = data['choices'][0]['message']['content']
#
#     with open('output_news.txt', 'a', encoding='utf-8') as file:
#         file.write(txt)
#         file.write('\n'+'= = = = = = = '*10 + '\n')
#         file.write('\n' + '= = = = = = = ' * 10 + '\n')
#
#     # print(response.text) # 打印ai生成的新闻
#     ai_context_num = ai_context_num + 1
#     print(f'ai生成的第{ai_context_num}新闻已经保存到output_news.txt文件中')
#
# if __name__ == '__main__':
#     with open('context_txt.txt','r',encoding='utf-8') as file:
#         neirong = file.read()
#
#     use_llm(neirong)
#     pass

# ----------------------------------------- deepseek模型 --------------------------------------------


# ----------------------------------------- LLM调试 （阿里ai）-----------------------------------------

# import requests
#
# url = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation'
#
# headers = {
#     'Authorization': 'Bearer sk-906dc03651c341d58e8f1abad5d935c6',
#     'Content-Type': 'application/json',
# }
#
# data = {
#     'model': 'qwen2-1.5b-instruct',
#     'input': {
#         'messages': [
#             {
#                 'role': 'system',
#                 'content': '我收集了来自不同应用的热门话题列表。请你帮我分析以下话题列表，找出相似或重复的话题，并汇总成一个综合的热门话题列表。'
#                 # 'content': "作为一位专业的总结专家，您的任务是处理多组由' '符号分隔的新闻话题句子。对于这一挑战，我们需要执行以下步骤： 1. **分析句子**: 首先，仔细审查每一组句子，识别并提取每个句子中的关键特征和关键词。 2. **记录关键词**: 将从每个句子中提炼出的关键词汇总记录，确保覆盖所有显著信息点。 3. **对比关键词**: 对所有句子的关键词列表进行对比，找出重复率最高的关键词集合。 4. **筛选句子**: 根据关键词的重复频率，挑选出那些共享最多相似关键词的句子。 5. **最终总结**: 对每组选中的句子，依据共同的关键词进行深度总结，提炼出三组最具代表性的核心信息概述。就比如现在我有三组句子：'今天小林和她妈妈去医院看病'，'今天妈妈带着小林去医院看病'，'今天妈妈陪同小林一起去医院看病'，那你就可以输出'今天小林和她妈妈一起去医院看病'，就是不能脱离了每个有着相似关键词句子的意思。 注意，是输出三个句子而不是关键词！下面是我提供的句子:",
#             },
#             {
#                 'role': 'user',
#                 # 'content': str(rf'{neirong}'),
#                 'content': str(rf"'樊振东谈畸形饭圈文化：输赢都不行', '12岁小孩哥百米跑出11秒49', '大爷为见亡妻每年都吃见手青遭质疑', '樊振东回应退役传闻'")
#             },
#         ],
#     },
#
#     'parameters': {
#         "temperature": 1.0,
#         "top_p": 0.8,
#     },
# }
#
# response = requests.post(url, headers=headers, json=data)
# print(response.text)

# ----------------------------------------- LLM调试 （阿里ai）-----------------------------------------

# ----------------------------------------- LLM调试 （硅基ai）-----------------------------------------
import requests
import json

ai_context_num = 0



def use_llm(neirong):
    global ai_context_num

    # url = 'https://yikeapi.me/v1/chat/completions' # 1亿 token deepseek
    # url = 'https://nai.typegpt.net/v1/chat/completions'
    # url = 'https://api.siliconflow.cn/v1/chat/completions'  # 硅基
    url = 'https://www.snaily.top/v1/chat/completions'  # claud key

    headers = {
        # 'Authorization': 'sk-vMkIVncsJy3DvMgVbALE3qggQUdQBmLAlfQqpI5mt4n1Lp6m',
        # 'Authorization':'sk-ZwWI64WZN6EES0I5C183B408D264439c8a59Bc9c3b2cF7C2',
        # 'Authorization':'Bearer sk-ptxzwtfjdtfznhfqhsqmhluivihtcbwcgeyhswkbgyjqfjwo',  # 硅基
        'Authorization': 'sk-wJ5brS9KSRupxHDgLrXk9II7hBfyQqgdCPsJfa4RuVHfcsFP',  # claud key
        'Content-Type': 'application/json',
    }
    # neirong = ['你是谁','鲁迅为什么打周树人']
    # neirong = [

    data = {
        # 'model': 'deepseek-ai/DeepSeek-V2.5',  # 硅基
        'model':'claude-3-5-sonnet-20241022',
        'messages': [
            {
                'role': 'system',
                # 'content':'你是一位具有专业写作技能和正确价值观的新闻撰稿人，你的任务是根据我提供的新闻内容，对其进行润色和改写，使其更加吸引读者。在改写过程中，请保持原文的实际内容、正确的价值观以及事实准确性不变，并避免使用过于机械化或不自然的语言风格（即“去AI化”）。并自由发挥个人写作风格，确保文章生动有趣。 具体要求如下： -  为改写后的文章拟定一个非常引人注目的标题。 - 正文部分应控制在500至600字之间，分为三到四段，并为每一段起一个简短的小标题来概括该段落的内容。  输出格式： 标题：\n 正文：\n    注意：确保整个过程遵循上述指导原则。结尾总结部分不要太ai化，每个段落的总结前加#再加个空格 ',
                'content': '你是一位具有专业写作技能和正确价值观的新闻撰稿人，你的任务是根据我提供的新闻内容，对其进行润色和改写，使其更加吸引读者。在改写过程中，请保持原文的实际内容、正确的价值观以及事实准确性不变，并避免使用过于机械化或不自然的语言风格（即“去AI化”）。并自由发挥个人写作风格，确保文章生动有趣。 具体要求如下： -  为改写后的文章拟定一个非常引人注目的标题。 - 开头部分要写得很吸引人，让人有读下去的欲望。- 正文部分应控制在1000字左右，最后写一些自己的观点，分为三到四段，并为每一段起一个简短的小标题来概括该段落的内容。- 原创率要达到80%，也就是说相似度不能太高。  输出格式： 标题：\n 正文：\n    注意：确保整个过程遵循上述指导原则。结尾总结部分不要太ai化，每个段落的总结前加#再加个空格',
            },

            {
                'role': 'user',
                'content': str(rf'{neirong}'),
            },

        ],
        # 'temperature': 0.2,
        # 'max_tokens': 4096,
    }

    response = requests.post(url, headers=headers, json=data)
    data = response.json()
    print(data)
    txt = data['choices'][0]['message']['content']

    with open('output_news.txt', 'a', encoding='utf-8') as file:
        file.write(txt)

        file.write('\n'+'= = = = = = = '*10 + '\n')

    # print(response.text) # 打印ai生成的新闻
    ai_context_num = ai_context_num + 1
    print(f'ai生成的第{ai_context_num}新闻已经保存到output_news.txt文件中')

if __name__ == '__main__':
    with open('context_txt.txt','r',encoding='utf-8') as file:
        neirong = file.read()

    use_llm(neirong)
    pass
# ----------------------------------------- LLM调试 （硅基ai）-----------------------------------------
