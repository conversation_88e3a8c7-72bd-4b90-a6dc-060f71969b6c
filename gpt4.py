import requests
import json

url = 'https://api.openai.com/v1/chat/completions'
# url = 'https://openai34434.cognitiveservices.azure.com/openai/deployments/o3-mini/chat/completions?api-version=2025-01-01-preview'
# url = 'https://api.x.ai/v1/chat/completions'

headers = {
    'Authorization': 'Bearer ********************************************************************************************************************************************************************',
    'Content-Type': 'application/json'
}

data = {
    'model': 'gpt-4.1-2025-04-14',
    'messages': [
        {'role': 'user', 'content': 'What is your big model language?Is gpt-4.1?'},
        # {'role': 'user', 'content': 'how are you?'}
    ]
}

response = requests.post(url, headers=headers, json=data)
json_string = response.text
data = json.loads(json_string)
# content = data['choices'][0]['message']['content']

print(data)

