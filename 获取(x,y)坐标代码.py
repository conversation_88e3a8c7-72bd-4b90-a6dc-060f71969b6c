# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/14 17:33
# <AUTHOR> s<PERSON>canfan
# @File    : 获取(x,y)坐标代码.py
# @explain : 


# from pynput import mouse, keyboard
#
# running = True
#
# def on_click(x, y, button, pressed):
#     if pressed:
#         print(f"鼠标点击坐标: x={x}, y={y}")
#
# def on_press(key):
#     global running
#     try:
#         if key.char == 'q':
#             running = False
#             # 停止监听
#             return False
#     except AttributeError:
#         pass
#
# # 键盘监听器
# keyboard_listener = keyboard.Listener(on_press=on_press)
# keyboard_listener.start()
#
# # 鼠标监听器
# with mouse.Listener(on_click=on_click) as mouse_listener:
#     while running:
#         mouse_listener.join(0.1)  # 定时检查 running 变量
#     mouse_listener.stop()
#
# print("程序已退出。")


import loguru
import asyncio  # <-- Add this import

async def solve_turnstile(logger: 'loguru.Logger', url: str, user_agent: str, user_data_path: str = None):
    from DrissionPage import ChromiumPage, ChromiumOptions
    options = (
        ChromiumOptions()
        .auto_port()
        .headless()
        .incognito(True)
        .set_user_agent(user_agent)
        .set_argument('--guest')
        .set_argument('--no-sandbox')
        .set_argument('--disable-gpu')
    )
    if user_data_path:
        options.set_user_data_path(user_data_path)
    page = ChromiumPage(options)
    page.screencast.set_save_path('turnstile')
    page.screencast.set_mode.video_mode()
    page.screencast.start()
    page.get(url)
    logger.debug('waiting for turnstile')
    await asyncio.sleep(2)
    divs = page.eles('tag:div', timeout=10)
    iframe = None
    for div in divs:
        if div.shadow_root:
            iframe = div.shadow_root.ele(
                "xpath://iframe[starts-with(@src, 'https://challenges.cloudflare.com/')]",
                timeout=0
            )
            if iframe:
                break
            break
    body_element = iframe.ele('tag:body', timeout=10).shadow_root
    logger.debug('waiting for text:Verify you are human')
    verify_element = body_element.ele("text:Verify you are human", timeout=10)
    await asyncio.sleep(1)
    logger.debug('click verify')
    verify_element.offset(10, 10).click(by_js=False)
    logger.debug('waiting for deleted')
    verify_element.wait.deleted(timeout=10)
    await asyncio.sleep(1)
    page.screencast.stop()
    page.close()

Url = "https://demo.fuclaude.com"
Ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
User_data_path = ""

# --- This is the corrected line ---
asyncio.run(solve_turnstile(loguru.logger, Url, Ua, User_data_path))