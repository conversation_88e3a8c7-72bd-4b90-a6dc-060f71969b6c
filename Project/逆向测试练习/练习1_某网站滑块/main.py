# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/29 00:56
# <AUTHOR> shaocan<PERSON>
# @File    : main.py
# @explain :

import base64
import io
import json
import time

import ddddocr
import execjs
import requests
from PIL import Image


def get_params(data):
    return execjs.compile(open('test1.js', encoding='utf-8').read()).call("get_params", data)


def get_picture(params):
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        'DNT': '1',
        'Origin': 'https://www.ynjzjgcx.com',
        'Pragma': 'no-cache',
        'Referer': 'https://www.ynjzjgcx.com/datapub/enterprise',
        'Sec-<PERSON>tch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'appId': '84ded2cd478642b2',
        'isToken': 'false',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    json_data = {
        'params': params
    }

    response = requests.post('https://www.ynjzjgcx.com/prod-api/mohurd-pub/vcode/genVcode', headers=headers,
                             json=json_data)

    # 解析响应
    if response.status_code == 200:
        result = response.json()

        # 检查响应是否成功
        if result.get('code') == 200 and result.get('success'):
            # 解析data字段
            data_str = result.get('data', '')
            if data_str:
                # data字段是JSON字符串，需要再次解析
                data_obj = json.loads(data_str)

                small_image = data_obj.get('smallImage')
                big_image = data_obj.get('bigImage')
                slide_id = data_obj.get('slideId')

                if small_image and big_image:
                    print(f"✅ 成功获取验证码图片")
                    print(f"SlideId: {slide_id}")
                    print(f"SmallImage的长度: {len(small_image)} 字符")
                    print(f"BigImage的长度: {len(big_image)} 字符")

                    return small_image, big_image, slide_id
                else:
                    print("❌ 响应中缺少图片数据")
                    return None, None, None
            else:
                print("❌ 响应中缺少data字段")
                return None, None, None
        else:
            print(f"❌ 请求失败: {result}")
            return None, None, None
    else:
        print(f"❌ HTTP请求失败: {response.status_code}")
        return None, None, None


def Base64Decode(small_image_base64, big_image_base64):
    small_image_data = base64.b64decode(small_image_base64)
    big_image_data = base64.b64decode(big_image_base64)
    with open(f'small_image.jpeg', 'wb') as f:
        f.write(small_image_data)
    print(f"✅ 小图片已保存: small_image.jpeg")
    with open(f'big_image.jpeg', 'wb') as f:
        f.write(big_image_data)
    print(f"✅ 大图片已保存: sbig_image.jpeg")
    return small_image_data, big_image_data


def test_distance():
    slice_image = open('./small_image.jpeg', 'rb').read()
    bg_image = open('./big_image.jpeg', 'rb').read()

    ocr = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)

    result = ocr.slide_match(slice_image, bg_image, simple_target=True)
    distance_x = result['target'][0]
    distance_y = result['target'][1]

    print(f"识别距离: {distance_x}")

    return distance_x, distance_y


def verfity_picture(small_picture_bytes, big_picture_bytes, distance_x, distance_y):
    bg_image = Image.open(io.BytesIO(big_picture_bytes))
    slider_image = Image.open(io.BytesIO(small_picture_bytes))
    bg_image.paste(slider_image, (distance_x, distance_y), slider_image)
    bg_image.save('xx.png')


def finish(slide_id: str, distance_x: int):
    data_vefity = {
        "pageNum": 1,
        "pageSize": 10,
        "certificateType": "",
        "name": "",
        "slideId": slide_id,
        "key": "query",
        "width": distance_x,
    }
    print(data_vefity)
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        'DNT': '1',
        'Origin': 'https://www.ynjzjgcx.com',
        'Pragma': 'no-cache',
        'Referer': 'https://www.ynjzjgcx.com/datapub/enterprise',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'appId': '84ded2cd478642b2',
        'isToken': 'false',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    params1 = execjs.compile(open('test1.js', encoding='utf-8').read()).call("get_params", data_vefity)

    json_data = {
        'params': params1,
    }
    print('json_data', json_data)
    response = requests.post(
        url='https://www.ynjzjgcx.com/prod-api/mohurd-pub/dataServ/findBaseEntDpPage',
        headers=headers,
        json=json_data,
    )

    print(response.text)


def main():
    params = get_params({"key": "query"})
    small_image, big_image, slide_id = get_picture(params)
    # print(small_image, big_image, slide_id)
    small_image, big_image = Base64Decode(small_image, big_image)
    print(f"✅✅✅图片保存任务完成，slide_id：{slide_id}")
    distance_x, distance_y = test_distance()

    verfity_picture(small_image, big_image, distance_x, distance_y)
    print('✅✅✅distance_x:', distance_x)
    finish(slide_id, distance_x)


if __name__ == '__main__':
    main()
