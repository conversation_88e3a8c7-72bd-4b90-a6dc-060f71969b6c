# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/29 10:30
# <AUTHOR> shaocanfan
# @File    : test2.py
# @explain :


# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/29 00:56
# <AUTHOR> shaocanfan
# @File    : main.py
# @explain :

import base64
import io
import json

import ddddocr
import execjs
import requests
from PIL import Image


def get_params(data):
    return execjs.compile(open('test1.js', encoding='utf-8').read()).call("get_params", data)

def get_picture(params):
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        'DNT': '1',
        'Origin': 'https://ynjzjgcx.com',
        'Pragma': 'no-cache',
        'Referer': 'https://ynjzjgcx.com/datapub/enterprise',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'appId': '84ded2cd478642b2',
        'isToken': 'false',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    json_data = {
    'params': params
    }

    response = requests.post('https://ynjzjgcx.com/prod-api/mohurd-pub/vcode/genVcode', headers=headers, json=json_data)

    # 解析响应
    if response.status_code == 200:
        result=response.json()

        # 检查响应是否成功
        if result.get('code') == 200 and result.get('success'):
            # 解析data字段
            data_str = result.get('data', '')
            if data_str:
                # data字段是JSON字符串，需要再次解析
                data_obj = json.loads(data_str)

                small_image = data_obj.get('smallImage')
                big_image = data_obj.get('bigImage')
                slide_id = data_obj.get('slideId')

                if small_image and big_image:
                    print(f"✅ 成功获取验证码图片")
                    print(f"SlideId: {slide_id}")
                    print(f"SmallImage的长度: {len(small_image)} 字符")
                    print(f"BigImage的长度: {len(big_image)} 字符")

                    return small_image, big_image, slide_id
                else:
                    print("❌ 响应中缺少图片数据")
                    return None, None, None
            else:
                print("❌ 响应中缺少data字段")
                return None, None, None
        else:
            print(f"❌ 请求失败: {result}")
            return None, None, None
    else:
        print(f"❌ HTTP请求失败: {response.status_code}")
        return None, None, None


def Base64Decode(small_image_base64, big_image_base64):
    small_image_data = base64.b64decode(small_image_base64)
    big_image_data = base64.b64decode(big_image_base64)
    with open(f'small_image.jpeg', 'wb') as f:
        f.write(small_image_data)
    print(f"✅ 小图片已保存: small_image.jpeg")
    with open(f'big_image.jpeg', 'wb') as f:
        f.write(big_image_data)
    print(f"✅ 大图片已保存: sbig_image.jpeg")
    return small_image_data, big_image_data


def test_distance():
    """
    改进的距离识别函数，使用多种方法提高准确性
    """
    slice_image = open('./small_image.jpeg','rb').read()
    bg_image = open('./big_image.jpeg','rb').read()

    ocr = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)

    distances = []

    # 方法1: simple_target=True
    try:
        result1 = ocr.slide_match(slice_image, bg_image, simple_target=True)
        if 'target' in result1 and len(result1['target']) >= 2:
            distance_x1 = result1['target'][0]
            distance_y1 = result1['target'][1]
            distances.append((distance_x1, distance_y1))
            print(f"方法1识别距离: x={distance_x1}, y={distance_y1}")
    except Exception as e:
        print(f"方法1识别失败: {e}")

    # 方法2: simple_target=False
    try:
        result2 = ocr.slide_match(slice_image, bg_image, simple_target=False)
        if 'target' in result2 and len(result2['target']) >= 2:
            distance_x2 = result2['target'][0]
            distance_y2 = result2['target'][1]
            distances.append((distance_x2, distance_y2))
            print(f"方法2识别距离: x={distance_x2}, y={distance_y2}")
    except Exception as e:
        print(f"方法2识别失败: {e}")

    if not distances:
        print("❌ 所有识别方法都失败了")
        return None, None

    # 如果有多个结果，选择最合理的
    if len(distances) == 1:
        final_x, final_y = distances[0]
    else:
        # 多个结果时，选择X坐标差异不大的，或者取平均值
        x_coords = [d[0] for d in distances]
        y_coords = [d[1] for d in distances]

        # 如果X坐标差异较小（<10像素），取平均值
        if max(x_coords) - min(x_coords) <= 10:
            final_x = sum(x_coords) // len(x_coords)
            final_y = sum(y_coords) // len(y_coords)
            print(f"多方法平均距离: x={final_x}, y={final_y}")
        else:
            # 差异较大时，选择第一个结果
            final_x, final_y = distances[0]
            print(f"选择方法1结果: x={final_x}, y={final_y}")

    print(f"🎯 最终识别距离: x={final_x}, y={final_y}")
    return final_x, final_y


def verfity_picture(small_picture_bytes, big_picture_bytes, distance_x, distance_y):
    """
    生成单张验证图片
    """
    if distance_x is None or distance_y is None:
        print("❌ 距离识别失败，无法生成验证图片")
        return

    try:
        bg_image = Image.open(io.BytesIO(big_picture_bytes))
        slider_image = Image.open(io.BytesIO(small_picture_bytes))

        print(f"📏 图片尺寸: 大图{bg_image.size}, 小图{slider_image.size}")
        print(f"🎯 识别位置: ({distance_x}, {distance_y})")

        # 生成验证图片
        result_image = bg_image.copy()
        if slider_image.mode == 'RGBA':
            result_image.paste(slider_image, (distance_x, distance_y), slider_image)
        else:
            result_image.paste(slider_image, (distance_x, distance_y))

        # 保存验证图片
        filename = 'verify_result.png'
        result_image.save(filename)
        print(f"✅ 验证图片已保存: {filename}")
        print(f"💡 请查看图片检查滑块是否正确填补了缺口")

    except Exception as e:
        print(f"❌ 生成验证图片时出错: {e}")






def main():
    params = get_params({"key":"query"})
    small_image, big_image, slide_id = get_picture(params)
    # print(small_image, big_image, slide_id)
    small_image, big_image = Base64Decode(small_image, big_image)
    print(f"✅✅✅图片保存任务完成，slide_id：{slide_id}")
    distance_x,distance_y = test_distance()

    verfity_picture(small_image, big_image,distance_x,distance_y)



if __name__ == '__main__':
    main()
