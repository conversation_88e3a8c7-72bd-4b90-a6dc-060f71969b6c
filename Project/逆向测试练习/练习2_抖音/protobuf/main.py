# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/8/4 13:08
# <AUTHOR> shaocan<PERSON>
# @File    : main.py
# @explain :

from google.protobuf import json_format

from p1_pb2 import Person, Message

p1 = Person()
p1.name = "yuan"
p1.id = 18

info = p1.SerializeToString()
print(info)

p2 = Message()
p2.message = "POST"
p2.payload = "棒极了"
info = p2.SerializeToString()
print(info)

obj = Message()
obj.ParseFromString(info)
print(obj.message)
print(obj.payload)


