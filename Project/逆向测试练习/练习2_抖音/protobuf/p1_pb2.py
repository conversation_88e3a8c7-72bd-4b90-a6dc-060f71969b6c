# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: p1.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x08p1.proto\x12\x04yuan\"\"\n\x06<PERSON>erson\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x05\"+\n\x07Message\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0f\n\x07payload\x18\x02 \x01(\tb\x06proto3')



_PERSON = DESCRIPTOR.message_types_by_name['Person']
_MESSAGE = DESCRIPTOR.message_types_by_name['Message']
Person = _reflection.GeneratedProtocolMessageType('Person', (_message.Message,), {
  'DESCRIPTOR' : _PERSON,
  '__module__' : 'p1_pb2'
  # @@protoc_insertion_point(class_scope:yuan.Person)
  })
_sym_db.RegisterMessage(Person)

Message = _reflection.GeneratedProtocolMessageType('Message', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGE,
  '__module__' : 'p1_pb2'
  # @@protoc_insertion_point(class_scope:yuan.Message)
  })
_sym_db.RegisterMessage(Message)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PERSON._serialized_start=18
  _PERSON._serialized_end=52
  _MESSAGE._serialized_start=54
  _MESSAGE._serialized_end=97
# @@protoc_insertion_point(module_scope)
