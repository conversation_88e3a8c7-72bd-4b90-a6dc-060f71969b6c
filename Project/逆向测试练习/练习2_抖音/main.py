# !/usr/bin/env python
# -*- coding: utf-8 -*-
import gzip
import json

# @Time    : 2025/7/15 17:47
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : douyin.py
# @explain :

import requests
import re
import execjs
from websocket import WebSocketApp
import ssl
import time
from douyin_pb2 import PushFrame,Response,MemberMessage,ChatMessage,SendMessageBody,PreMessage


def on_open(ws):
    print("on_open",ws)

def on_message(ws,message):
    #回调函数，接收抖音的弹幕信息
    # print("on_message",ws,message)
    frame = PushFrame()
    frame.ParseFromString(message)
    # print(frame)
    origin_bytes = gzip.decompress(frame.payload)
    all_res = Response()
    all_res.ParseFromString(origin_bytes)

    membermessage = MemberMessage()
    chatmessage = ChatMessage()
    sendmessage = SendMessageBody()
    pre = PreMessage()
    #print("===============开始===============")
    # print('message:', all_res)

    # print(all_res.messagesList)
    # for msg in all_res.messagesList:
    #     if msg.method == "WebcastMemberMessage":
    #         membermessage.ParseFromString(msg.payload)
    #         print(membermessage)
    for msg in all_res.messagesList:
        if msg.method == "WebcastChatMessage":
            chatmessage.ParseFromString(msg.payload)
            print(f'🔥用户名:', chatmessage.user.nickName)
            print(f"🎯提取到聊天信息:",chatmessage.content)
            print(f"🏆荣誉等级:", chatmessage.user.BadgeImageList[0].content.alternativeText)
            print(f'抖音号:',chatmessage.user.shortId)
            print('------------')



            pre.sendMessageBody.CopyFrom(sendmessage)
            print('send successful!!!')
            time.sleep(10)

            print(chatmessage)
    #print("===============结束===============")



def on_error(ws, error):  # 多加个参数 error
    print("on_error", error)

def on_close(ws, close_status_code, close_msg):
    print("on_close", close_status_code, close_msg)


def get_room_id(url):

    cookies = {
        '__ac_nonce':'0677cd822009854415249',
    }

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'cache-control': 'no-cache',
        'dnt': '1',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'referer': 'https://live.douyin.com/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        # 'cookie': 'enter_pc_once=1; UIFID_TEMP=1b474bc7e0db9591e645dd8feb8c65aae4845018effd0c2743039a380ee6474080a726860eaa28c47b43563689bfa573e92be3aaad8151707b5f173c1a8cd534cb4c4d333438a68e9eaed3fa8683bfe47618402fd6a6a089895487ae016db8d7c42701fc8f96bf0909abf5b0d491dffa; hevc_supported=true; __security_mc_1_s_sdk_crypt_sdk=2d885316-4c32-a450; bd_ticket_guard_client_web_domain=2; passport_csrf_token=220e86913a03789c46910dc8de788be3; passport_csrf_token_default=220e86913a03789c46910dc8de788be3; download_guide=%221%2F20250726%2F0%22; d_ticket=528b126afe9851f60afefcb7191f8556cb6cf; passport_assist_user=CjxxMmgmpxV2unSwF0_dopsseYw3d5t7nLhrtlpl8a2F9v2yHGGf1Ayxn9ejC_R4WdokQNT8eWJNACmpT44aSgo8AAAAAAAAAAAAAE9Hi5SgdgABbSEuY42P5UoyiTgknOXw5jYXJS74JGi6gDtugQDveFtlJAhch2Ipm_A3EMPl9w0Yia_WVCABIgEDLqcY3A%3D%3D; n_mh=93kVgjTS1gkoRwmMTVtwFSJ5OjL0nfuQTm9N0esS_OM; passport_auth_status=39991e36af7d03ed8426b0041e3465f6%2C; passport_auth_status_ss=39991e36af7d03ed8426b0041e3465f6%2C; uid_tt=1c775f525967f2877e9664e37522c745; uid_tt_ss=1c775f525967f2877e9664e37522c745; sid_tt=1cae53682606700445283ca3462bad8a; sessionid=1cae53682606700445283ca3462bad8a; sessionid_ss=1cae53682606700445283ca3462bad8a; session_tlb_tag=sttt%7C9%7CHK5TaCYGcARFKDyjRiutiv_________OGVGFSySrRkunav-dzAm3iAvF6zws5yN9GsgeEzqm6jk%3D; is_staff_user=false; login_time=1753540200937; __security_mc_1_s_sdk_cert_key=8cf3204e-4f8c-acbb; __security_server_data_status=1; publish_badge_show_info=%220%2C0%2C0%2C1753540201491%22; is_dash_user=1; live_use_vvc=%22false%22; h265ErrorNum=-1; xgplayer_device_id=2546013872; xgplayer_user_id=147462799930; fpk1=U2FsdGVkX1/BG0fwzMzjHaz4fp8cA1NSopqs48IeMuVNgoEjtA0xRCnL9CmZaxLWqrrs4e+gNGdJ/KwatOkpgQ==; fpk2=7ddeda88d0c599cc494da0dece6554d5; my_rd=2; passport_mfa_token=CjX%2BzO9PaTZW81%2Fd4yUriIDwhlaiEQUo5e5q13dY2scMv%2B7ETFRCwNCib%2Bb4u8Hv%2FiTx5JKt%2BBpKCjwAAAAAAAAAAAAAT0hNuvbD50XhV%2F1PKISYYiwJ1zqj7j%2FhdBijyPk%2F8FkhHsqRnWtuN8dVVkBvDkZX9zoQru33DRj2sdFsIAIiAQNpq0PS; sid_guard=1cae53682606700445283ca3462bad8a%7C1753616326%7C5107875%7CWed%2C+24-Sep-2025+14%3A30%3A01+GMT; sid_ucp_v1=1.0.0-KDYyMzFjOTkzYjJlM2EwMTE3NWE4ZTA5Y2E4ZTZmYTU2NDhmOGVlYWUKHwjE6q6H4gIQxp-YxAYY7zEgDDCF_Y7VBTgCQPEHSAQaAmxxIiAxY2FlNTM2ODI2MDY3MDA0NDUyODNjYTM0NjJiYWQ4YQ; ssid_ucp_v1=1.0.0-KDYyMzFjOTkzYjJlM2EwMTE3NWE4ZTA5Y2E4ZTZmYTU2NDhmOGVlYWUKHwjE6q6H4gIQxp-YxAYY7zEgDDCF_Y7VBTgCQPEHSAQaAmxxIiAxY2FlNTM2ODI2MDY3MDA0NDUyODNjYTM0NjJiYWQ4YQ; _bd_ticket_crypt_doamin=2; _bd_ticket_crypt_cookie=8f506973ca6bfa4e74fb8e0233c895ae; __security_mc_1_s_sdk_sign_data_key_web_protect=32a9e003-492a-bb3c; SelfTabRedDotControl=%5B%7B%22id%22%3A%226806188535394093070%22%2C%22u%22%3A43%2C%22c%22%3A43%7D%5D; strategyABtestKey=%************.44%22; WallpaperGuide=%7B%22showTime%22%3A1753545343780%2C%22closeTime%22%3A0%2C%22showCount%22%3A1%2C%22cursor1%22%3A35%2C%22cursor2%22%3A10%2C%22hoverTime%22%3A1753545517806%7D; stream_player_status_params=%22%7B%5C%22is_auto_play%5C%22%3A0%2C%5C%22is_full_screen%5C%22%3A0%2C%5C%22is_full_webscreen%5C%22%3A0%2C%5C%22is_mute%5C%22%3A0%2C%5C%22is_speed%5C%22%3A1%2C%5C%22is_visible%5C%22%3A1%7D%22; webcast_leading_last_show_time=1753679014203; webcast_leading_total_show_times=1; has_avx2=null; device_web_cpu_core=8; device_web_memory_size=8; csrf_session_id=5ca78c4b5724c853484f953fbe77e9f4; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1536%2C%5C%22screen_height%5C%22%3A864%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A100%7D%22; home_can_add_dy_2_desktop=%221%22; __live_version__=%221.1.3.6365%22; __ac_nonce=06887279b0034e6d71037; __ac_signature=_02B4Z6wo00f01oxw2qQAAIDCu1Qe-vUtGV6MUN4AAMun4d; sdk_source_info=7e276470716a68645a606960273f276364697660272927676c715a6d6069756077273f276364697660272927666d776a68605a607d71606b766c6a6b5a7666776c7571273f275e58272927666a6b766a69605a696c6061273f27636469766027292762696a6764695a7364776c6467696076273f275e5827292771273f273d36303c333c323d33363032342778; bit_env=q6Fo75gBjJzDgbIwseKQlQ6gcOY8JiVfhzW4beiIhxAUSAEIu9yF5htW-L8IQc3k38w9L1BsrUJ847Av9oVniGYGIfV1V9FWMjWfUssCaGkuV_nbH9eYfijOcgvhlS2tF-hhMbUysaDZNz0uaEFSLqV8flSbpuBEgH0Fq9J_HZe3hFUErr3bPQJeG6KH9tv9wAOeteqGTnVgRo2B2O1mNvaicR9DCWs-F9_VESdVQ-SZ3fHVhTgxn55fcz6X3Gf2wmSzQObC1tEiaiIrKel53G18KKbpqsht_DYA1DWZNU60PmcJbG-JmVXIl6Yu_4sJsgRGUsBaO2ejRGuIL1ohvGH6x94r3yNY1W0Gq9qYUPUVEbNU45Xcz0nSqMYaJV-wSRUtXiaam_F8ZG-8qu-hgwA_073YkUngBoNokT5qMYQypR2T2Q_cfaggalW70d5DAbqI6BzhPvPk-KeMJYMl4N0nTPQDQ7l5izVwzmNx0mygS0pZbkYhZsfthRFW1Of9; gulu_source_res=eyJwX2luIjoiYTlmMjU3NzAxMWQ2OTIyYjc5NWQ5Zjk3NjY1OWVkOTNkMGQ2NjBjMWZhMmNkYzdjMGI4NmI5YTU2YjlhYmU1OCJ9; passport_auth_mix_state=8e7u18fa5bndfiuotl0j0wfs6veko946; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAAitVfT6fzbA1mj74taheNdp75loqzBttSpwhBRfcfDxA%2F1753718400000%2F0%2F0%2F1753688571270%22; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAAitVfT6fzbA1mj74taheNdp75loqzBttSpwhBRfcfDxA%2F1753718400000%2F0%2F1753687971270%2F0%22; xg_device_score=7.547739235768296; live_can_add_dy_2_desktop=%221%22; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCSWVEbWdvQlUvbDJLNDVwSG51NWNZMHF5NElKYVlMMGRvZzQ4M0srNVBqSnV4NGo1ZjBUMS92SGo4WlZhTVZuU0UrdlZZUHQzVlA4aVJpL0ttNzBNd0k9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; volume_info=%7B%22isUserMute%22%3Atrue%2C%22isMute%22%3Atrue%2C%22volume%22%3A0.556%7D; biz_trace_id=b5b5d063; odin_tt=4334a620c5abce70ec560e20898a0f3257ff7bce613f039a075fe460140521a65f6add083ae650abbdb96c352aa03394d6d3f4254cb3efaff03dfb250595f64ddebc8924bf16a30c131251fdca07eb76; ttwid=1%7CwI8gt4biHH8bLmgHL4gX9j5nM2O4pD-pPCe_2jq-nrw%7C1753688015%7Cf0aed52b8972c2387a6a6b447f3756cedddeab68ec036c6832cf32f1f6659a14; IsDouyinActive=false; passport_fe_beating_status=false',
    }

    response = requests.get(url, headers=headers,cookies=cookies)

    room_id = re.findall(r'\\"roomId\\":\\"(\d+)\\"',response.text)[0]

    ttwid = response.cookies.get_dict()["ttwid"]

    return room_id,ttwid


# 东方甄选：80017709309



def main():
    # (1)获取room_id
    get_time0 = int(time.time() * 1000)
    room_id,ttwid = get_room_id("https://live.douyin.com/80017709309")
    get_time = int(time.time() * 1000)
    print("room_id==",room_id,'\n')
    print("ttwid==",ttwid,'\n')
    print("get_time==",get_time,'\n')
    print("get_time0==", get_time0, '\n')

    # (2)获取sign
    signture = execjs.compile(open("D:\pycharm\python_project_exe\Project\逆向测试练习\练习2_抖音\loader1.js",encoding='utf-8').read()).call("get_sign",room_id)
    # signture = "6KkVNxMjjNO9uUnL"
    if(signture is None):
        print("❌ 无法获取签名")
        return


    print("sign==",signture)

    # (3)通过websocket接收信息
    # Ws_url = f'wss://webcast100-ws-web-lf.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.14-beta.0&update_version_code=1.0.14-beta.0&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1536&screen_height=864&browser_language=zh-CN&browser_platform=Win32&browser_name=Mozilla&browser_version=5.0%20(Windows%20NT%2010.0;%20Win64;%20x64)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=d-7527698813587292163_u-1_fh-7527698223705608235_t-{get_time0}_r-7527698826472255258&internal_ext=internal_src:dim|wss_push_room_id:{room_id}|wss_push_did:7310123775072175650|first_req_ms:{get_time0}|fetch_time:{get_time0}|seq:1|wss_info:0-{get_time0}-0-0|wrds_v:7527698822177235004&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=7310123775072175650&im_path=/webcast/im/fetch/&identity=audience&need_persist_msg_count=15&insert_task_id=&live_reason=&room_id={room_id}&heartbeatDuration=0&signature={signture}'
    Ws_url = f'wss://webcast100-ws-web-lq.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.14-beta.0&update_version_code=1.0.14-beta.0&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1536&screen_height=864&browser_language=zh-CN&browser_platform=Win32&browser_name=Mozilla&browser_version=5.0%20(Windows%20NT%2010.0;%20Win64;%20x64)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=fh-7534356571658051081_t-1754229433112_r-7534358044415630841_d-7534358001465950211_u-1&internal_ext=internal_src:dim|wss_push_room_id:{room_id}|wss_push_did:7531397351983728169|first_req_ms:1754229433015|fetch_time:1754229433112|seq:1|wss_info:0-1754229433112-0-0|wrds_v:7534358040120664259&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=7531397351983728169&im_path=/webcast/im/fetch/&identity=audience&need_persist_msg_count=15&insert_task_id=&live_reason=&room_id={room_id}&heartbeatDuration=0&signature={signture}'
    Ws_url = f'wss://webcast100-ws-web-lq.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.14-beta.0&update_version_code=1.0.14-beta.0&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1536&screen_height=864&browser_language=zh-CN&browser_platform=Win32&browser_name=Mozilla&browser_version=5.0%20(Windows%20NT%2010.0;%20Win64;%20x64)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=fh-7534555074321160970_t-1754275409321_r-7534555509832106740_d-7534555445407514625_u-1&internal_ext=internal_src:dim|wss_push_room_id:{room_id}|wss_push_did:7531397351983728169|first_req_ms:1754275409229|fetch_time:1754275409321|seq:1|wss_info:0-1754275409321-0-0|wrds_v:7534555488357190310&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=7531397351983728169&im_path=/webcast/im/fetch/&identity=audience&need_persist_msg_count=15&insert_task_id=&live_reason=&room_id={room_id}&heartbeatDuration=0&signature={signture}'
    ws = WebSocketApp(
        url=Ws_url,
        header={
            'Upgrade': 'websocket',
            'Origin': 'https://live.douyin.com',
            'Cache-Control': 'no-cache',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Pragma': 'no-cache',
            'Connection': 'Upgrade',
            'Sec-WebSocket-Key': 'Pn2S0kCWObEiNP4MZ/FW/w==',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Sec-WebSocket-Version': '13',
            'Sec-WebSocket-Extensions': 'permessage-deflate; client_max_window_bits',
        },
        cookie=f'ttwid={ttwid}',
        on_open=on_open,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close,

    )

    ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE}, ping_interval=10, ping_timeout=5)



if __name__ == '__main__':
    main()

















