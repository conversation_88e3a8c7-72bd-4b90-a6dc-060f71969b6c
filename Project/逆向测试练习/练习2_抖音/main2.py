import time

# 假设抖音 epoch 是 2010-01-01 00:00:00 UTC
TIKTOK_EPOCH = 0  # 毫秒时间戳

def generate_msg_id(machine_id=1, sequence=0):
    """
    生成一个符合抖音风格的 snowflake ID（msgid）
    :param machine_id: 机器ID（0~1023）
    :param sequence: 序列号（0~4095）
    """
    current_time_ms = int(time.time() * 1000)
    timestamp_part = current_time_ms - TIKTOK_EPOCH  # 差值部分，占41位

    if timestamp_part >= (1 << 41):
        raise ValueError("时间戳超出范围")

    # 拼装64位
    msg_id = (timestamp_part << 22) | (machine_id << 12) | sequence
    return msg_id

# 示例
print(generate_msg_id())