# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: douyin.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x64ouyin.proto\x12\x06\x64ouyin\"\xe4\x02\n\x08Response\x12%\n\x0cmessagesList\x18\x01 \x03(\x0b\x32\x0f.douyin.Message\x12\x0e\n\x06\x63ursor\x18\x02 \x01(\t\x12\x15\n\rfetchInterval\x18\x03 \x01(\x04\x12\x0b\n\x03now\x18\x04 \x01(\x04\x12\x13\n\x0binternalExt\x18\x05 \x01(\t\x12\x11\n\tfetchType\x18\x06 \x01(\r\x12\x36\n\x0brouteParams\x18\x07 \x03(\x0b\x32!.douyin.Response.RouteParamsEntry\x12\x19\n\x11heartbeatDuration\x18\x08 \x01(\x04\x12\x0f\n\x07needAck\x18\t \x01(\x08\x12\x12\n\npushServer\x18\n \x01(\t\x12\x12\n\nliveCursor\x18\x0b \x01(\t\x12\x15\n\rhistoryNoMore\x18\x0c \x01(\x08\x1a\x32\n\x10RouteParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x9a\x01\n\x07Message\x12\x0e\n\x06method\x18\x01 \x01(\t\x12\x0f\n\x07payload\x18\x02 \x01(\x0c\x12\r\n\x05msgId\x18\x03 \x01(\x03\x12\x0f\n\x07msgType\x18\x04 \x01(\x05\x12\x0e\n\x06offset\x18\x05 \x01(\x03\x12\x15\n\rneedWrdsStore\x18\x06 \x01(\x08\x12\x13\n\x0bwrdsVersion\x18\x07 \x01(\x03\x12\x12\n\nwrdsSubKey\x18\x08 \x01(\t\"\xf7\x01\n\x10\x45mojiChatMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\x0f\n\x07\x65mojiId\x18\x03 \x01(\x03\x12\"\n\x0c\x65mojiContent\x18\x04 \x01(\x0b\x32\x0c.douyin.Text\x12\x16\n\x0e\x64\x65\x66\x61ultContent\x18\x05 \x01(\t\x12&\n\x0f\x62\x61\x63kgroundImage\x18\x06 \x01(\x0b\x32\r.douyin.Image\x12\x14\n\x0c\x66romIntercom\x18\x07 \x01(\x08\x12\x1c\n\x14intercomHideUserCard\x18\x08 \x01(\x08\"\xca\x04\n\x0b\x43hatMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x17\n\x0fvisibleToSender\x18\x04 \x01(\x08\x12&\n\x0f\x62\x61\x63kgroundImage\x18\x05 \x01(\x0b\x32\r.douyin.Image\x12\x1b\n\x13\x66ullScreenTextColor\x18\x06 \x01(\t\x12(\n\x11\x62\x61\x63kgroundImageV2\x18\x07 \x01(\x0b\x32\r.douyin.Image\x12\x32\n\x10publicAreaCommon\x18\t \x01(\x0b\x32\x18.douyin.PublicAreaCommon\x12 \n\tgiftImage\x18\n \x01(\x0b\x32\r.douyin.Image\x12\x12\n\nagreeMsgId\x18\x0b \x01(\x04\x12\x15\n\rpriorityLevel\x18\x0c \x01(\r\x12\x38\n\x13landscapeAreaCommon\x18\r \x01(\x0b\x32\x1b.douyin.LandscapeAreaCommon\x12\x11\n\teventTime\x18\x0f \x01(\x04\x12\x12\n\nsendReview\x18\x10 \x01(\x08\x12\x14\n\x0c\x66romIntercom\x18\x11 \x01(\x08\x12\x1c\n\x14intercomHideUserCard\x18\x12 \x01(\x08\x12\x0e\n\x06\x63hatBy\x18\x14 \x01(\t\x12\x1e\n\x16individualChatPriority\x18\x15 \x01(\r\x12 \n\nrtfContent\x18\x16 \x01(\x0b\x32\x0c.douyin.Text\"\xa1\x01\n\x13LandscapeAreaCommon\x12\x10\n\x08showHead\x18\x01 \x01(\x08\x12\x14\n\x0cshowNickname\x18\x02 \x01(\x08\x12\x15\n\rshowFontColor\x18\x03 \x01(\x08\x12\x16\n\x0e\x63olorValueList\x18\x04 \x03(\t\x12\x33\n\x13\x63ommentTypeTagsList\x18\x05 \x03(\x0e\x32\x16.douyin.CommentTypeTag\"\x87\x03\n\x12RoomUserSeqMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x38\n\tranksList\x18\x02 \x03(\x0b\x32%.douyin.RoomUserSeqMessageContributor\x12\r\n\x05total\x18\x03 \x01(\x03\x12\x0e\n\x06popStr\x18\x04 \x01(\t\x12\x38\n\tseatsList\x18\x05 \x03(\x0b\x32%.douyin.RoomUserSeqMessageContributor\x12\x12\n\npopularity\x18\x06 \x01(\x03\x12\x11\n\ttotalUser\x18\x07 \x01(\x03\x12\x14\n\x0ctotalUserStr\x18\x08 \x01(\t\x12\x10\n\x08totalStr\x18\t \x01(\t\x12\x1b\n\x13onlineUserForAnchor\x18\n \x01(\t\x12\x18\n\x10totalPvForAnchor\x18\x0b \x01(\t\x12\x17\n\x0fupRightStatsStr\x18\x0c \x01(\t\x12\x1f\n\x17upRightStatsStrComplete\x18\r \x01(\t\"^\n\x11\x43ommonTextMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\r\n\x05scene\x18\x03 \x01(\t\"\x89\x01\n\x16UpdateFanTicketMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x1e\n\x16roomFanTicketCountText\x18\x02 \x01(\t\x12\x1a\n\x12roomFanTicketCount\x18\x03 \x01(\x04\x12\x13\n\x0b\x66orceUpdate\x18\x04 \x01(\x08\"\xa9\x01\n\x1dRoomUserSeqMessageContributor\x12\r\n\x05score\x18\x01 \x01(\x04\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\x0c\n\x04rank\x18\x03 \x01(\x04\x12\r\n\x05\x64\x65lta\x18\x04 \x01(\x04\x12\x10\n\x08isHidden\x18\x05 \x01(\x08\x12\x18\n\x10scoreDescription\x18\x06 \x01(\t\x12\x14\n\x0c\x65xactlyScore\x18\x07 \x01(\t\"\xb1\x06\n\x0bGiftMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x0e\n\x06giftId\x18\x02 \x01(\x04\x12\x16\n\x0e\x66\x61nTicketCount\x18\x03 \x01(\x04\x12\x12\n\ngroupCount\x18\x04 \x01(\x04\x12\x13\n\x0brepeatCount\x18\x05 \x01(\x04\x12\x12\n\ncomboCount\x18\x06 \x01(\x04\x12\x1a\n\x04user\x18\x07 \x01(\x0b\x32\x0c.douyin.User\x12\x1c\n\x06toUser\x18\x08 \x01(\x0b\x32\x0c.douyin.User\x12\x11\n\trepeatEnd\x18\t \x01(\r\x12&\n\ntextEffect\x18\n \x01(\x0b\x32\x12.douyin.TextEffect\x12\x0f\n\x07groupId\x18\x0b \x01(\x04\x12\x17\n\x0fincomeTaskgifts\x18\x0c \x01(\x04\x12\x1a\n\x12roomFanTicketCount\x18\r \x01(\x04\x12(\n\x08priority\x18\x0e \x01(\x0b\x32\x16.douyin.GiftIMPriority\x12 \n\x04gift\x18\x0f \x01(\x0b\x32\x12.douyin.GiftStruct\x12\r\n\x05logId\x18\x10 \x01(\t\x12\x10\n\x08sendType\x18\x11 \x01(\x04\x12\x32\n\x10publicAreaCommon\x18\x12 \x01(\x0b\x32\x18.douyin.PublicAreaCommon\x12%\n\x0ftrayDisplayText\x18\x13 \x01(\x0b\x32\x0c.douyin.Text\x12\x1c\n\x14\x62\x61nnedDisplayEffects\x18\x14 \x01(\x04\x12\x16\n\x0e\x64isplayForSelf\x18\x19 \x01(\x08\x12\x18\n\x10interactGiftInfo\x18\x1a \x01(\t\x12\x13\n\x0b\x64iyItemInfo\x18\x1b \x01(\t\x12\x17\n\x0fminAssetSetList\x18\x1c \x03(\x04\x12\x12\n\ntotalCount\x18\x1d \x01(\x04\x12\x18\n\x10\x63lientGiftSource\x18\x1e \x01(\r\x12\x15\n\rtoUserIdsList\x18  \x03(\x04\x12\x10\n\x08sendTime\x18! \x01(\x04\x12\x1b\n\x13\x66orceDisplayEffects\x18\" \x01(\x04\x12\x0f\n\x07traceId\x18# \x01(\t\x12\x17\n\x0f\x65\x66\x66\x65\x63tDisplayTs\x18$ \x01(\x04\"\xa3\x03\n\nGiftStruct\x12\x1c\n\x05image\x18\x01 \x01(\x0b\x32\r.douyin.Image\x12\x10\n\x08\x64\x65scribe\x18\x02 \x01(\t\x12\x0e\n\x06notify\x18\x03 \x01(\x08\x12\x10\n\x08\x64uration\x18\x04 \x01(\x04\x12\n\n\x02id\x18\x05 \x01(\x04\x12\x12\n\nforLinkmic\x18\x07 \x01(\x08\x12\x0e\n\x06\x64oodle\x18\x08 \x01(\x08\x12\x13\n\x0b\x66orFansclub\x18\t \x01(\x08\x12\r\n\x05\x63ombo\x18\n \x01(\x08\x12\x0c\n\x04type\x18\x0b \x01(\r\x12\x14\n\x0c\x64iamondCount\x18\x0c \x01(\r\x12\x1a\n\x12isDisplayedOnPanel\x18\r \x01(\x08\x12\x17\n\x0fprimaryEffectId\x18\x0e \x01(\x04\x12$\n\rgiftLabelIcon\x18\x0f \x01(\x0b\x32\r.douyin.Image\x12\x0c\n\x04name\x18\x10 \x01(\t\x12\x0e\n\x06region\x18\x11 \x01(\t\x12\x0e\n\x06manual\x18\x12 \x01(\t\x12\x11\n\tforCustom\x18\x13 \x01(\x08\x12\x1b\n\x04icon\x18\x15 \x01(\x0b\x32\r.douyin.Image\x12\x12\n\nactionType\x18\x16 \x01(\r\"U\n\x0eGiftIMPriority\x12\x16\n\x0equeueSizesList\x18\x01 \x03(\x04\x12\x19\n\x11selfQueuePriority\x18\x02 \x01(\x04\x12\x10\n\x08priority\x18\x03 \x01(\x04\"e\n\nTextEffect\x12*\n\x08portrait\x18\x01 \x01(\x0b\x32\x18.douyin.TextEffectDetail\x12+\n\tlandscape\x18\x02 \x01(\x0b\x32\x18.douyin.TextEffectDetail\"\xb6\x02\n\x10TextEffectDetail\x12\x1a\n\x04text\x18\x01 \x01(\x0b\x32\x0c.douyin.Text\x12\x14\n\x0ctextFontSize\x18\x02 \x01(\r\x12!\n\nbackground\x18\x03 \x01(\x0b\x32\r.douyin.Image\x12\r\n\x05start\x18\x04 \x01(\r\x12\x10\n\x08\x64uration\x18\x05 \x01(\r\x12\t\n\x01x\x18\x06 \x01(\r\x12\t\n\x01y\x18\x07 \x01(\r\x12\r\n\x05width\x18\x08 \x01(\r\x12\x0e\n\x06height\x18\t \x01(\r\x12\x10\n\x08shadowDx\x18\n \x01(\r\x12\x10\n\x08shadowDy\x18\x0b \x01(\r\x12\x14\n\x0cshadowRadius\x18\x0c \x01(\r\x12\x13\n\x0bshadowColor\x18\r \x01(\t\x12\x13\n\x0bstrokeColor\x18\x0e \x01(\t\x12\x13\n\x0bstrokeWidth\x18\x0f \x01(\r\"\xef\x04\n\rMemberMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\x13\n\x0bmemberCount\x18\x03 \x01(\x04\x12\x1e\n\x08operator\x18\x04 \x01(\x0b\x32\x0c.douyin.User\x12\x14\n\x0cisSetToAdmin\x18\x05 \x01(\x08\x12\x11\n\tisTopUser\x18\x06 \x01(\x08\x12\x11\n\trankScore\x18\x07 \x01(\x04\x12\x11\n\ttopUserNo\x18\x08 \x01(\x04\x12\x11\n\tenterType\x18\t \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\n \x01(\x04\x12\x19\n\x11\x61\x63tionDescription\x18\x0b \x01(\t\x12\x0e\n\x06userId\x18\x0c \x01(\x04\x12*\n\x0c\x65\x66\x66\x65\x63tConfig\x18\r \x01(\x0b\x32\x14.douyin.EffectConfig\x12\x0e\n\x06popStr\x18\x0e \x01(\t\x12/\n\x11\x65nterEffectConfig\x18\x0f \x01(\x0b\x32\x14.douyin.EffectConfig\x12&\n\x0f\x62\x61\x63kgroundImage\x18\x10 \x01(\x0b\x32\r.douyin.Image\x12(\n\x11\x62\x61\x63kgroundImageV2\x18\x11 \x01(\x0b\x32\r.douyin.Image\x12\'\n\x11\x61nchorDisplayText\x18\x12 \x01(\x0b\x32\x0c.douyin.Text\x12\x32\n\x10publicAreaCommon\x18\x13 \x01(\x0b\x32\x18.douyin.PublicAreaCommon\x12\x18\n\x10userEnterTipType\x18\x14 \x01(\x04\x12\x1a\n\x12\x61nchorEnterTipType\x18\x15 \x01(\x04\"n\n\x10PublicAreaCommon\x12 \n\tuserLabel\x18\x01 \x01(\x0b\x32\r.douyin.Image\x12\x19\n\x11userConsumeInRoom\x18\x02 \x01(\x04\x12\x1d\n\x15userSendGiftCntInRoom\x18\x03 \x01(\x04\"\x96\x05\n\x0c\x45\x66\x66\x65\x63tConfig\x12\x0c\n\x04type\x18\x01 \x01(\x04\x12\x1b\n\x04icon\x18\x02 \x01(\x0b\x32\r.douyin.Image\x12\x11\n\tavatarPos\x18\x03 \x01(\x04\x12\x1a\n\x04text\x18\x04 \x01(\x0b\x32\x0c.douyin.Text\x12\x1f\n\x08textIcon\x18\x05 \x01(\x0b\x32\r.douyin.Image\x12\x10\n\x08stayTime\x18\x06 \x01(\r\x12\x13\n\x0b\x61nimAssetId\x18\x07 \x01(\x04\x12\x1c\n\x05\x62\x61\x64ge\x18\x08 \x01(\x0b\x32\r.douyin.Image\x12\x1c\n\x14\x66lexSettingArrayList\x18\t \x03(\x04\x12&\n\x0ftextIconOverlay\x18\n \x01(\x0b\x32\r.douyin.Image\x12$\n\ranimatedBadge\x18\x0b \x01(\x0b\x32\r.douyin.Image\x12\x15\n\rhasSweepLight\x18\x0c \x01(\x08\x12 \n\x18textFlexSettingArrayList\x18\r \x03(\x04\x12\x19\n\x11\x63\x65nterAnimAssetId\x18\x0e \x01(\x04\x12#\n\x0c\x64ynamicImage\x18\x0f \x01(\x0b\x32\r.douyin.Image\x12\x34\n\x08\x65xtraMap\x18\x10 \x03(\x0b\x32\".douyin.EffectConfig.ExtraMapEntry\x12\x16\n\x0emp4AnimAssetId\x18\x11 \x01(\x04\x12\x10\n\x08priority\x18\x12 \x01(\x04\x12\x13\n\x0bmaxWaitTime\x18\x13 \x01(\x04\x12\x0f\n\x07\x64ressId\x18\x14 \x01(\t\x12\x11\n\talignment\x18\x15 \x01(\x04\x12\x17\n\x0f\x61lignmentOffset\x18\x16 \x01(\x04\x1a/\n\rExtraMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"|\n\x04Text\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x15\n\rdefaultPatter\x18\x02 \x01(\t\x12)\n\rdefaultFormat\x18\x03 \x01(\x0b\x32\x12.douyin.TextFormat\x12%\n\npiecesList\x18\x04 \x03(\x0b\x32\x11.douyin.TextPiece\"\xb4\x02\n\tTextPiece\x12\x0c\n\x04type\x18\x01 \x01(\x08\x12\"\n\x06\x66ormat\x18\x02 \x01(\x0b\x32\x12.douyin.TextFormat\x12\x13\n\x0bstringValue\x18\x03 \x01(\t\x12(\n\tuserValue\x18\x04 \x01(\x0b\x32\x15.douyin.TextPieceUser\x12(\n\tgiftValue\x18\x05 \x01(\x0b\x32\x15.douyin.TextPieceGift\x12*\n\nheartValue\x18\x06 \x01(\x0b\x32\x16.douyin.TextPieceHeart\x12\x34\n\x0fpatternRefValue\x18\x07 \x01(\x0b\x32\x1b.douyin.TextPiecePatternRef\x12*\n\nimageValue\x18\x08 \x01(\x0b\x32\x16.douyin.TextPieceImage\"C\n\x0eTextPieceImage\x12\x1c\n\x05image\x18\x01 \x01(\x0b\x32\r.douyin.Image\x12\x13\n\x0bscalingRate\x18\x02 \x01(\x02\":\n\x13TextPiecePatternRef\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x16\n\x0e\x64\x65\x66\x61ultPattern\x18\x02 \x01(\t\"\x1f\n\x0eTextPieceHeart\x12\r\n\x05\x63olor\x18\x01 \x01(\t\"D\n\rTextPieceGift\x12\x0e\n\x06giftId\x18\x01 \x01(\x04\x12#\n\x07nameRef\x18\x02 \x01(\x0b\x32\x12.douyin.PatternRef\"1\n\nPatternRef\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x16\n\x0e\x64\x65\x66\x61ultPattern\x18\x02 \x01(\t\">\n\rTextPieceUser\x12\x1a\n\x04user\x18\x01 \x01(\x0b\x32\x0c.douyin.User\x12\x11\n\twithColon\x18\x02 \x01(\x08\"\xa3\x01\n\nTextFormat\x12\r\n\x05\x63olor\x18\x01 \x01(\t\x12\x0c\n\x04\x62old\x18\x02 \x01(\x08\x12\x0e\n\x06italic\x18\x03 \x01(\x08\x12\x0e\n\x06weight\x18\x04 \x01(\r\x12\x13\n\x0bitalicAngle\x18\x05 \x01(\r\x12\x10\n\x08\x66ontSize\x18\x06 \x01(\r\x12\x1a\n\x12useHeighLightColor\x18\x07 \x01(\x08\x12\x15\n\ruseRemoteClor\x18\x08 \x01(\x08\"\xca\x02\n\x0bLikeMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\x12\r\n\x05total\x18\x03 \x01(\x04\x12\r\n\x05\x63olor\x18\x04 \x01(\x04\x12\x1a\n\x04user\x18\x05 \x01(\x0b\x32\x0c.douyin.User\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x32\n\x10\x64oubleLikeDetail\x18\x07 \x01(\x0b\x32\x18.douyin.DoubleLikeDetail\x12\x36\n\x12\x64isplayControlInfo\x18\x08 \x01(\x0b\x32\x1a.douyin.DisplayControlInfo\x12\x17\n\x0flinkmicGuestUid\x18\t \x01(\x04\x12\r\n\x05scene\x18\n \x01(\t\x12\x30\n\x0fpicoDisplayInfo\x18\x0b \x01(\x0b\x32\x17.douyin.PicoDisplayInfo\"\xcc\x01\n\rSocialMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\x11\n\tshareType\x18\x03 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\x04\x12\x13\n\x0bshareTarget\x18\x05 \x01(\t\x12\x13\n\x0b\x66ollowCount\x18\x06 \x01(\x04\x12\x32\n\x10publicAreaCommon\x18\x07 \x01(\x0b\x32\x18.douyin.PublicAreaCommon\"l\n\x0fPicoDisplayInfo\x12\x15\n\rcomboSumCount\x18\x01 \x01(\x04\x12\r\n\x05\x65moji\x18\x02 \x01(\t\x12 \n\temojiIcon\x18\x03 \x01(\x0b\x32\r.douyin.Image\x12\x11\n\temojiText\x18\x04 \x01(\t\"_\n\x10\x44oubleLikeDetail\x12\x12\n\ndoubleFlag\x18\x01 \x01(\x08\x12\r\n\x05seqId\x18\x02 \x01(\r\x12\x13\n\x0brenewalsNum\x18\x03 \x01(\r\x12\x13\n\x0btriggersNum\x18\x04 \x01(\r\"9\n\x12\x44isplayControlInfo\x12\x10\n\x08showText\x18\x01 \x01(\x08\x12\x11\n\tshowIcons\x18\x02 \x01(\x08\"\xc8\x01\n\x12\x45pisodeChatMessage\x12\x1f\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0f.douyin.Message\x12\x1a\n\x04user\x18\x02 \x01(\x0b\x32\x0c.douyin.User\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x16\n\x0evisibleToSende\x18\x04 \x01(\x08\x12 \n\tgiftImage\x18\x07 \x01(\x0b\x32\r.douyin.Image\x12\x12\n\nagreeMsgId\x18\x08 \x01(\x04\x12\x16\n\x0e\x63olorValueList\x18\t \x03(\t\"\x88\x01\n\x18MatchAgainstScoreMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12 \n\x07\x61gainst\x18\x02 \x01(\x0b\x32\x0f.douyin.Against\x12\x13\n\x0bmatchStatus\x18\x03 \x01(\r\x12\x15\n\rdisplayStatus\x18\x04 \x01(\r\"\x92\x03\n\x07\x41gainst\x12\x10\n\x08leftName\x18\x01 \x01(\t\x12\x1f\n\x08leftLogo\x18\x02 \x01(\x0b\x32\r.douyin.Image\x12\x10\n\x08leftGoal\x18\x03 \x01(\t\x12\x11\n\trightName\x18\x06 \x01(\t\x12 \n\trightLogo\x18\x07 \x01(\x0b\x32\r.douyin.Image\x12\x11\n\trightGoal\x18\x08 \x01(\t\x12\x11\n\ttimestamp\x18\x0b \x01(\x04\x12\x0f\n\x07version\x18\x0c \x01(\x04\x12\x12\n\nleftTeamId\x18\r \x01(\x04\x12\x13\n\x0brightTeamId\x18\x0e \x01(\x04\x12\x19\n\x11\x64iffSei2absSecond\x18\x0f \x01(\x04\x12\x16\n\x0e\x66inalGoalStage\x18\x10 \x01(\r\x12\x18\n\x10\x63urrentGoalStage\x18\x11 \x01(\r\x12\x19\n\x11leftScoreAddition\x18\x12 \x01(\r\x12\x1a\n\x12rightScoreAddition\x18\x13 \x01(\r\x12\x13\n\x0bleftGoalInt\x18\x14 \x01(\x04\x12\x14\n\x0crightGoalInt\x18\x15 \x01(\x04\"\xd1\x03\n\x06\x43ommon\x12\x0e\n\x06method\x18\x01 \x01(\t\x12\r\n\x05msgId\x18\x02 \x01(\x04\x12\x0e\n\x06roomId\x18\x03 \x01(\x04\x12\x12\n\ncreateTime\x18\x04 \x01(\x04\x12\x0f\n\x07monitor\x18\x05 \x01(\r\x12\x11\n\tisShowMsg\x18\x06 \x01(\x08\x12\x10\n\x08\x64\x65scribe\x18\x07 \x01(\t\x12\x10\n\x08\x66oldType\x18\t \x01(\x04\x12\x16\n\x0e\x61nchorFoldType\x18\n \x01(\x04\x12\x15\n\rpriorityScore\x18\x0b \x01(\x04\x12\r\n\x05logId\x18\x0c \x01(\t\x12\x19\n\x11msgProcessFilterK\x18\r \x01(\t\x12\x19\n\x11msgProcessFilterV\x18\x0e \x01(\t\x12\x1a\n\x04user\x18\x0f \x01(\x0b\x32\x0c.douyin.User\x12\x18\n\x10\x61nchorFoldTypeV2\x18\x11 \x01(\x04\x12\x1a\n\x12processAtSeiTimeMs\x18\x12 \x01(\x04\x12\x18\n\x10randomDispatchMs\x18\x13 \x01(\x04\x12\x12\n\nisDispatch\x18\x14 \x01(\x08\x12\x11\n\tchannelId\x18\x15 \x01(\x04\x12\x19\n\x11\x64iffSei2absSecond\x18\x16 \x01(\x04\x12\x1a\n\x12\x61nchorFoldDuration\x18\x17 \x01(\x04\"\x9f\x06\n\x04User\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07shortId\x18\x02 \x01(\x04\x12\x10\n\x08nickName\x18\x03 \x01(\t\x12\x0e\n\x06gender\x18\x04 \x01(\r\x12\x11\n\tSignature\x18\x05 \x01(\t\x12\r\n\x05Level\x18\x06 \x01(\r\x12\x10\n\x08\x42irthday\x18\x07 \x01(\x04\x12\x11\n\tTelephone\x18\x08 \x01(\t\x12\"\n\x0b\x41vatarThumb\x18\t \x01(\x0b\x32\r.douyin.Image\x12#\n\x0c\x41vatarMedium\x18\n \x01(\x0b\x32\r.douyin.Image\x12\"\n\x0b\x41vatarLarge\x18\x0b \x01(\x0b\x32\r.douyin.Image\x12\x10\n\x08Verified\x18\x0c \x01(\x08\x12\x12\n\nExperience\x18\r \x01(\r\x12\x0c\n\x04\x63ity\x18\x0e \x01(\t\x12\x0e\n\x06Status\x18\x0f \x01(\x05\x12\x12\n\nCreateTime\x18\x10 \x01(\x04\x12\x12\n\nModifyTime\x18\x11 \x01(\x04\x12\x0e\n\x06Secret\x18\x12 \x01(\r\x12\x16\n\x0eShareQrcodeUri\x18\x13 \x01(\t\x12\x1a\n\x12IncomeSharePercent\x18\x14 \x01(\r\x12%\n\x0e\x42\x61\x64geImageList\x18\x15 \x03(\x0b\x32\r.douyin.Image\x12&\n\nFollowInfo\x18\x16 \x01(\x0b\x32\x12.douyin.FollowInfo\x12\"\n\x08PayGrade\x18\x17 \x01(\x0b\x32\x10.douyin.PayGrade\x12\"\n\x08\x46\x61nsClub\x18\x18 \x01(\x0b\x32\x10.douyin.FansClub\x12\x11\n\tSpecialId\x18\x1a \x01(\t\x12#\n\x0c\x41vatarBorder\x18\x1b \x01(\x0b\x32\r.douyin.Image\x12\x1c\n\x05Medal\x18\x1c \x01(\x0b\x32\r.douyin.Image\x12(\n\x11RealTimeIconsList\x18\x1d \x03(\x0b\x32\r.douyin.Image\x12\x11\n\tdisplayId\x18& \x01(\t\x12\x0e\n\x06secUid\x18. \x01(\t\x12\x17\n\x0e\x66\x61nTicketCount\x18\xfe\x07 \x01(\x04\x12\x0e\n\x05idStr\x18\x84\x08 \x01(\t\x12\x11\n\x08\x61geRange\x18\x95\x08 \x01(\r\"\xe2\x06\n\x08PayGrade\x12\x19\n\x11totalDiamondCount\x18\x01 \x01(\x03\x12\"\n\x0b\x64iamondIcon\x18\x02 \x01(\x0b\x32\r.douyin.Image\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x1b\n\x04icon\x18\x04 \x01(\x0b\x32\r.douyin.Image\x12\x10\n\x08nextName\x18\x05 \x01(\t\x12\r\n\x05level\x18\x06 \x01(\x03\x12\x1f\n\x08nextIcon\x18\x07 \x01(\x0b\x32\r.douyin.Image\x12\x13\n\x0bnextDiamond\x18\x08 \x01(\x03\x12\x12\n\nnowDiamond\x18\t \x01(\x03\x12\x1b\n\x13thisGradeMinDiamond\x18\n \x01(\x03\x12\x1b\n\x13thisGradeMaxDiamond\x18\x0b \x01(\x03\x12\x15\n\rpayDiamondBak\x18\x0c \x01(\x03\x12\x15\n\rgradeDescribe\x18\r \x01(\t\x12(\n\rgradeIconList\x18\x0e \x03(\x0b\x32\x11.douyin.GradeIcon\x12\x16\n\x0escreenChatType\x18\x0f \x01(\x03\x12\x1d\n\x06imIcon\x18\x10 \x01(\x0b\x32\r.douyin.Image\x12&\n\x0fimIconWithLevel\x18\x11 \x01(\x0b\x32\r.douyin.Image\x12\x1f\n\x08liveIcon\x18\x12 \x01(\x0b\x32\r.douyin.Image\x12)\n\x12newImIconWithLevel\x18\x13 \x01(\x0b\x32\r.douyin.Image\x12\"\n\x0bnewLiveIcon\x18\x14 \x01(\x0b\x32\r.douyin.Image\x12\x1a\n\x12upgradeNeedConsume\x18\x15 \x01(\x03\x12\x16\n\x0enextPrivileges\x18\x16 \x01(\t\x12!\n\nbackground\x18\x17 \x01(\x0b\x32\r.douyin.Image\x12%\n\x0e\x62\x61\x63kgroundBack\x18\x18 \x01(\x0b\x32\r.douyin.Image\x12\r\n\x05score\x18\x19 \x01(\x03\x12\'\n\x08\x62uffInfo\x18\x1a \x01(\x0b\x32\x15.douyin.GradeBuffInfo\x12\x14\n\x0bgradeBanner\x18\xe9\x07 \x01(\t\x12\'\n\x0fprofileDialogBg\x18\xea\x07 \x01(\x0b\x32\r.douyin.Image\x12+\n\x13profileDialogBgBack\x18\xeb\x07 \x01(\x0b\x32\r.douyin.Image\"\xad\x01\n\x08\x46\x61nsClub\x12\"\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x14.douyin.FansClubData\x12\x34\n\npreferData\x18\x02 \x03(\x0b\x32 .douyin.FansClub.PreferDataEntry\x1aG\n\x0fPreferDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.douyin.FansClubData:\x02\x38\x01\"\x99\x01\n\x0c\x46\x61nsClubData\x12\x10\n\x08\x63lubName\x18\x01 \x01(\t\x12\r\n\x05level\x18\x02 \x01(\x05\x12\x1a\n\x12userFansClubStatus\x18\x03 \x01(\x05\x12 \n\x05\x62\x61\x64ge\x18\x04 \x01(\x0b\x32\x11.douyin.UserBadge\x12\x18\n\x10\x61vailableGiftIds\x18\x05 \x03(\x03\x12\x10\n\x08\x61nchorId\x18\x06 \x01(\x03\"\x84\x01\n\tUserBadge\x12+\n\x05icons\x18\x01 \x03(\x0b\x32\x1c.douyin.UserBadge.IconsEntry\x12\r\n\x05title\x18\x02 \x01(\t\x1a;\n\nIconsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.douyin.Image:\x02\x38\x01\"\x0f\n\rGradeBuffInfo\"\x08\n\x06\x42order\"^\n\tGradeIcon\x12\x1b\n\x04icon\x18\x01 \x01(\x0b\x32\r.douyin.Image\x12\x13\n\x0biconDiamond\x18\x02 \x01(\x03\x12\r\n\x05level\x18\x03 \x01(\x03\x12\x10\n\x08levelStr\x18\x04 \x01(\t\"\xae\x01\n\nFollowInfo\x12\x16\n\x0e\x66ollowingCount\x18\x01 \x01(\x04\x12\x15\n\rfollowerCount\x18\x02 \x01(\x04\x12\x14\n\x0c\x66ollowStatus\x18\x03 \x01(\x04\x12\x12\n\npushStatus\x18\x04 \x01(\x04\x12\x12\n\nremarkName\x18\x05 \x01(\t\x12\x18\n\x10\x66ollowerCountStr\x18\x06 \x01(\t\x12\x19\n\x11\x66ollowingCountStr\x18\x07 \x01(\t\"\xa2\x02\n\x05Image\x12\x13\n\x0burlListList\x18\x01 \x03(\t\x12\x0b\n\x03uri\x18\x02 \x01(\t\x12\x0e\n\x06height\x18\x03 \x01(\x04\x12\r\n\x05width\x18\x04 \x01(\x04\x12\x10\n\x08\x61vgColor\x18\x05 \x01(\t\x12\x11\n\timageType\x18\x06 \x01(\r\x12\x12\n\nopenWebUrl\x18\x07 \x01(\t\x12%\n\x07\x63ontent\x18\x08 \x01(\x0b\x32\x14.douyin.ImageContent\x12\x12\n\nisAnimated\x18\t \x01(\x08\x12\x31\n\x0f\x46lexSettingList\x18\n \x01(\x0b\x32\x18.douyin.NinePatchSetting\x12\x31\n\x0fTextSettingList\x18\x0b \x01(\x0b\x32\x18.douyin.NinePatchSetting\"+\n\x10NinePatchSetting\x12\x17\n\x0fsettingListList\x18\x01 \x03(\t\"W\n\x0cImageContent\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfontColor\x18\x02 \x01(\t\x12\r\n\x05level\x18\x03 \x01(\x04\x12\x17\n\x0f\x61lternativeText\x18\x04 \x01(\t\"\xb3\x01\n\tPushFrame\x12\r\n\x05seqId\x18\x01 \x01(\x04\x12\r\n\x05logId\x18\x02 \x01(\x04\x12\x0f\n\x07service\x18\x03 \x01(\x04\x12\x0e\n\x06method\x18\x04 \x01(\x04\x12(\n\x0bheadersList\x18\x05 \x03(\x0b\x32\x13.douyin.HeadersList\x12\x17\n\x0fpayloadEncoding\x18\x06 \x01(\t\x12\x13\n\x0bpayloadType\x18\x07 \x01(\t\x12\x0f\n\x07payload\x18\x08 \x01(\x0c\"\x0f\n\x02kk\x12\t\n\x01k\x18\x0e \x01(\r\"\xcd\x01\n\x0fSendMessageBody\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12\x18\n\x10\x63onversationType\x18\x02 \x01(\r\x12\x1b\n\x13\x63onversationShortId\x18\x03 \x01(\x04\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\x12\x1c\n\x03\x65xt\x18\x05 \x03(\x0b\x32\x0f.douyin.ExtList\x12\x13\n\x0bmessageType\x18\x06 \x01(\r\x12\x0e\n\x06ticket\x18\x07 \x01(\t\x12\x17\n\x0f\x63lientMessageId\x18\x08 \x01(\t\"%\n\x07\x45xtList\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xb7\x01\n\x03Rsp\x12\t\n\x01\x61\x18\x01 \x01(\x05\x12\t\n\x01\x62\x18\x02 \x01(\x05\x12\t\n\x01\x63\x18\x03 \x01(\x05\x12\t\n\x01\x64\x18\x04 \x01(\t\x12\t\n\x01\x65\x18\x05 \x01(\x05\x12\x18\n\x01\x66\x18\x06 \x01(\x0b\x32\r.douyin.Rsp.F\x12\t\n\x01g\x18\x07 \x01(\t\x12\t\n\x01h\x18\n \x01(\x04\x12\t\n\x01i\x18\x0b \x01(\x04\x12\t\n\x01j\x18\r \x01(\x04\x1a\x33\n\x01\x46\x12\n\n\x02q1\x18\x01 \x01(\x04\x12\n\n\x02q3\x18\x03 \x01(\x04\x12\n\n\x02q4\x18\x04 \x01(\t\x12\n\n\x02q5\x18\x05 \x01(\x04\"\xb2\x02\n\nPreMessage\x12\x0b\n\x03\x63md\x18\x01 \x01(\r\x12\x12\n\nsequenceId\x18\x02 \x01(\r\x12\x12\n\nsdkVersion\x18\x03 \x01(\t\x12\r\n\x05token\x18\x04 \x01(\t\x12\r\n\x05refer\x18\x05 \x01(\r\x12\x11\n\tinboxType\x18\x06 \x01(\r\x12\x13\n\x0b\x62uildNumber\x18\x07 \x01(\t\x12\x30\n\x0fsendMessageBody\x18\x08 \x01(\x0b\x32\x17.douyin.SendMessageBody\x12\n\n\x02\x61\x61\x18\t \x01(\t\x12\x16\n\x0e\x64\x65vicePlatform\x18\x0b \x01(\t\x12$\n\x07headers\x18\x0f \x03(\x0b\x32\x13.douyin.HeadersList\x12\x10\n\x08\x61uthType\x18\x12 \x01(\r\x12\x0b\n\x03\x62iz\x18\x15 \x01(\t\x12\x0e\n\x06\x61\x63\x63\x65ss\x18\x16 \x01(\t\")\n\x0bHeadersList\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"[\n\x13LiveShoppingMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x0f\n\x07msgType\x18\x02 \x01(\x05\x12\x13\n\x0bpromotionId\x18\x04 \x01(\x03\"\xed\x01\n\x10RoomStatsMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x14\n\x0c\x64isplayShort\x18\x02 \x01(\t\x12\x15\n\rdisplayMiddle\x18\x03 \x01(\t\x12\x13\n\x0b\x64isplayLong\x18\x04 \x01(\t\x12\x14\n\x0c\x64isplayValue\x18\x05 \x01(\x03\x12\x16\n\x0e\x64isplayVersion\x18\x06 \x01(\x03\x12\x13\n\x0bincremental\x18\x07 \x01(\x08\x12\x10\n\x08isHidden\x18\x08 \x01(\x08\x12\r\n\x05total\x18\t \x01(\x03\x12\x13\n\x0b\x64isplayType\x18\n \x01(\x03\"c\n\x0bProductInfo\x12\x13\n\x0bpromotionId\x18\x01 \x01(\x03\x12\r\n\x05index\x18\x02 \x01(\x05\x12\x1b\n\x13targetFlashUidsList\x18\x03 \x03(\x03\x12\x13\n\x0b\x65xplainType\x18\x04 \x01(\x03\"e\n\x0c\x43\x61tegoryInfo\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x18\n\x10promotionIdsList\x18\x03 \x03(\x03\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x13\n\x0buniqueIndex\x18\x05 \x01(\t\"\xdd\x01\n\x14ProductChangeMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x17\n\x0fupdateTimestamp\x18\x02 \x01(\x03\x12\x13\n\x0bupdateToast\x18\x03 \x01(\t\x12\x32\n\x15updateProductInfoList\x18\x04 \x03(\x0b\x32\x13.douyin.ProductInfo\x12\r\n\x05total\x18\x05 \x01(\x03\x12\x34\n\x16updateCategoryInfoList\x18\x08 \x03(\x0b\x32\x14.douyin.CategoryInfo\"@\n\x0e\x43ontrolMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x0e\n\x06status\x18\x02 \x01(\x05\"p\n\x0f\x46\x61nsclubMessage\x12\"\n\ncommonInfo\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x0c\n\x04type\x18\x02 \x01(\x05\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x1a\n\x04user\x18\x04 \x01(\x0b\x32\x0c.douyin.User\"\xb7\x01\n\x0fRoomRankMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x33\n\tranksList\x18\x02 \x03(\x0b\x32 .douyin.RoomRankMessage.RoomRank\x1aO\n\x08RoomRank\x12\x1a\n\x04user\x18\x01 \x01(\x0b\x32\x0c.douyin.User\x12\x10\n\x08scoreStr\x18\x02 \x01(\t\x12\x15\n\rprofileHidden\x18\x03 \x01(\x08\"\xc3\x02\n\x0bRoomMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x18\n\x10supprotLandscape\x18\x03 \x01(\x08\x12\x30\n\x0froommessagetype\x18\x04 \x01(\x0e\x32\x17.douyin.RoomMsgTypeEnum\x12\x14\n\x0csystemTopMsg\x18\x05 \x01(\x08\x12\x17\n\x0f\x66orcedGuarantee\x18\x06 \x01(\x08\x12\x10\n\x08\x62izScene\x18\x14 \x01(\t\x12?\n\x0e\x62uriedPointMap\x18\x1e \x03(\x0b\x32\'.douyin.RoomMessage.BuriedPointMapEntry\x1a\x35\n\x13\x42uriedPointMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x97\x01\n\x1bRoomStreamAdaptationMessage\x12\x1e\n\x06\x63ommon\x18\x01 \x01(\x0b\x32\x0e.douyin.Common\x12\x16\n\x0e\x61\x64\x61ptationType\x18\x02 \x01(\x05\x12\x1d\n\x15\x61\x64\x61ptationHeightRatio\x18\x03 \x01(\x02\x12!\n\x19\x61\x64\x61ptationBodyCenterRatio\x18\x04 \x01(\x02*C\n\x0e\x43ommentTypeTag\x12\x19\n\x15\x43OMMENTTYPETAGUNKNOWN\x10\x00\x12\x16\n\x12\x43OMMENTTYPETAGSTAR\x10\x01*\xdd\x01\n\x0fRoomMsgTypeEnum\x12\x12\n\x0e\x44\x45\x46\x41ULTROOMMSG\x10\x00\x12\x1d\n\x19\x45\x43OMLIVEREPLAYSAVEROOMMSG\x10\x01\x12\x1b\n\x17\x43ONSUMERRELATIONROOMMSG\x10\x02\x12\x1c\n\x18JUMANJIDATAAUTHNOTIFYMSG\x10\x03\x12\x10\n\x0cVSWELCOMEMSG\x10\x04\x12\x12\n\x0eMINORREFUNDMSG\x10\x05\x12\x1f\n\x1bPAIDLIVEROOMNOTIFYANCHORMSG\x10\x06\x12\x15\n\x11HOSTTEAMSYSTEMMSG\x10\x07\x62\x06proto3')

_COMMENTTYPETAG = DESCRIPTOR.enum_types_by_name['CommentTypeTag']
CommentTypeTag = enum_type_wrapper.EnumTypeWrapper(_COMMENTTYPETAG)
_ROOMMSGTYPEENUM = DESCRIPTOR.enum_types_by_name['RoomMsgTypeEnum']
RoomMsgTypeEnum = enum_type_wrapper.EnumTypeWrapper(_ROOMMSGTYPEENUM)
COMMENTTYPETAGUNKNOWN = 0
COMMENTTYPETAGSTAR = 1
DEFAULTROOMMSG = 0
ECOMLIVEREPLAYSAVEROOMMSG = 1
CONSUMERRELATIONROOMMSG = 2
JUMANJIDATAAUTHNOTIFYMSG = 3
VSWELCOMEMSG = 4
MINORREFUNDMSG = 5
PAIDLIVEROOMNOTIFYANCHORMSG = 6
HOSTTEAMSYSTEMMSG = 7


_RESPONSE = DESCRIPTOR.message_types_by_name['Response']
_RESPONSE_ROUTEPARAMSENTRY = _RESPONSE.nested_types_by_name['RouteParamsEntry']
_MESSAGE = DESCRIPTOR.message_types_by_name['Message']
_EMOJICHATMESSAGE = DESCRIPTOR.message_types_by_name['EmojiChatMessage']
_CHATMESSAGE = DESCRIPTOR.message_types_by_name['ChatMessage']
_LANDSCAPEAREACOMMON = DESCRIPTOR.message_types_by_name['LandscapeAreaCommon']
_ROOMUSERSEQMESSAGE = DESCRIPTOR.message_types_by_name['RoomUserSeqMessage']
_COMMONTEXTMESSAGE = DESCRIPTOR.message_types_by_name['CommonTextMessage']
_UPDATEFANTICKETMESSAGE = DESCRIPTOR.message_types_by_name['UpdateFanTicketMessage']
_ROOMUSERSEQMESSAGECONTRIBUTOR = DESCRIPTOR.message_types_by_name['RoomUserSeqMessageContributor']
_GIFTMESSAGE = DESCRIPTOR.message_types_by_name['GiftMessage']
_GIFTSTRUCT = DESCRIPTOR.message_types_by_name['GiftStruct']
_GIFTIMPRIORITY = DESCRIPTOR.message_types_by_name['GiftIMPriority']
_TEXTEFFECT = DESCRIPTOR.message_types_by_name['TextEffect']
_TEXTEFFECTDETAIL = DESCRIPTOR.message_types_by_name['TextEffectDetail']
_MEMBERMESSAGE = DESCRIPTOR.message_types_by_name['MemberMessage']
_PUBLICAREACOMMON = DESCRIPTOR.message_types_by_name['PublicAreaCommon']
_EFFECTCONFIG = DESCRIPTOR.message_types_by_name['EffectConfig']
_EFFECTCONFIG_EXTRAMAPENTRY = _EFFECTCONFIG.nested_types_by_name['ExtraMapEntry']
_TEXT = DESCRIPTOR.message_types_by_name['Text']
_TEXTPIECE = DESCRIPTOR.message_types_by_name['TextPiece']
_TEXTPIECEIMAGE = DESCRIPTOR.message_types_by_name['TextPieceImage']
_TEXTPIECEPATTERNREF = DESCRIPTOR.message_types_by_name['TextPiecePatternRef']
_TEXTPIECEHEART = DESCRIPTOR.message_types_by_name['TextPieceHeart']
_TEXTPIECEGIFT = DESCRIPTOR.message_types_by_name['TextPieceGift']
_PATTERNREF = DESCRIPTOR.message_types_by_name['PatternRef']
_TEXTPIECEUSER = DESCRIPTOR.message_types_by_name['TextPieceUser']
_TEXTFORMAT = DESCRIPTOR.message_types_by_name['TextFormat']
_LIKEMESSAGE = DESCRIPTOR.message_types_by_name['LikeMessage']
_SOCIALMESSAGE = DESCRIPTOR.message_types_by_name['SocialMessage']
_PICODISPLAYINFO = DESCRIPTOR.message_types_by_name['PicoDisplayInfo']
_DOUBLELIKEDETAIL = DESCRIPTOR.message_types_by_name['DoubleLikeDetail']
_DISPLAYCONTROLINFO = DESCRIPTOR.message_types_by_name['DisplayControlInfo']
_EPISODECHATMESSAGE = DESCRIPTOR.message_types_by_name['EpisodeChatMessage']
_MATCHAGAINSTSCOREMESSAGE = DESCRIPTOR.message_types_by_name['MatchAgainstScoreMessage']
_AGAINST = DESCRIPTOR.message_types_by_name['Against']
_COMMON = DESCRIPTOR.message_types_by_name['Common']
_USER = DESCRIPTOR.message_types_by_name['User']
_PAYGRADE = DESCRIPTOR.message_types_by_name['PayGrade']
_FANSCLUB = DESCRIPTOR.message_types_by_name['FansClub']
_FANSCLUB_PREFERDATAENTRY = _FANSCLUB.nested_types_by_name['PreferDataEntry']
_FANSCLUBDATA = DESCRIPTOR.message_types_by_name['FansClubData']
_USERBADGE = DESCRIPTOR.message_types_by_name['UserBadge']
_USERBADGE_ICONSENTRY = _USERBADGE.nested_types_by_name['IconsEntry']
_GRADEBUFFINFO = DESCRIPTOR.message_types_by_name['GradeBuffInfo']
_BORDER = DESCRIPTOR.message_types_by_name['Border']
_GRADEICON = DESCRIPTOR.message_types_by_name['GradeIcon']
_FOLLOWINFO = DESCRIPTOR.message_types_by_name['FollowInfo']
_IMAGE = DESCRIPTOR.message_types_by_name['Image']
_NINEPATCHSETTING = DESCRIPTOR.message_types_by_name['NinePatchSetting']
_IMAGECONTENT = DESCRIPTOR.message_types_by_name['ImageContent']
_PUSHFRAME = DESCRIPTOR.message_types_by_name['PushFrame']
_KK = DESCRIPTOR.message_types_by_name['kk']
_SENDMESSAGEBODY = DESCRIPTOR.message_types_by_name['SendMessageBody']
_EXTLIST = DESCRIPTOR.message_types_by_name['ExtList']
_RSP = DESCRIPTOR.message_types_by_name['Rsp']
_RSP_F = _RSP.nested_types_by_name['F']
_PREMESSAGE = DESCRIPTOR.message_types_by_name['PreMessage']
_HEADERSLIST = DESCRIPTOR.message_types_by_name['HeadersList']
_LIVESHOPPINGMESSAGE = DESCRIPTOR.message_types_by_name['LiveShoppingMessage']
_ROOMSTATSMESSAGE = DESCRIPTOR.message_types_by_name['RoomStatsMessage']
_PRODUCTINFO = DESCRIPTOR.message_types_by_name['ProductInfo']
_CATEGORYINFO = DESCRIPTOR.message_types_by_name['CategoryInfo']
_PRODUCTCHANGEMESSAGE = DESCRIPTOR.message_types_by_name['ProductChangeMessage']
_CONTROLMESSAGE = DESCRIPTOR.message_types_by_name['ControlMessage']
_FANSCLUBMESSAGE = DESCRIPTOR.message_types_by_name['FansclubMessage']
_ROOMRANKMESSAGE = DESCRIPTOR.message_types_by_name['RoomRankMessage']
_ROOMRANKMESSAGE_ROOMRANK = _ROOMRANKMESSAGE.nested_types_by_name['RoomRank']
_ROOMMESSAGE = DESCRIPTOR.message_types_by_name['RoomMessage']
_ROOMMESSAGE_BURIEDPOINTMAPENTRY = _ROOMMESSAGE.nested_types_by_name['BuriedPointMapEntry']
_ROOMSTREAMADAPTATIONMESSAGE = DESCRIPTOR.message_types_by_name['RoomStreamAdaptationMessage']
Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {

  'RouteParamsEntry' : _reflection.GeneratedProtocolMessageType('RouteParamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _RESPONSE_ROUTEPARAMSENTRY,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.Response.RouteParamsEntry)
    })
  ,
  'DESCRIPTOR' : _RESPONSE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Response)
  })
_sym_db.RegisterMessage(Response)
_sym_db.RegisterMessage(Response.RouteParamsEntry)

Message = _reflection.GeneratedProtocolMessageType('Message', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Message)
  })
_sym_db.RegisterMessage(Message)

EmojiChatMessage = _reflection.GeneratedProtocolMessageType('EmojiChatMessage', (_message.Message,), {
  'DESCRIPTOR' : _EMOJICHATMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.EmojiChatMessage)
  })
_sym_db.RegisterMessage(EmojiChatMessage)

ChatMessage = _reflection.GeneratedProtocolMessageType('ChatMessage', (_message.Message,), {
  'DESCRIPTOR' : _CHATMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.ChatMessage)
  })
_sym_db.RegisterMessage(ChatMessage)

LandscapeAreaCommon = _reflection.GeneratedProtocolMessageType('LandscapeAreaCommon', (_message.Message,), {
  'DESCRIPTOR' : _LANDSCAPEAREACOMMON,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.LandscapeAreaCommon)
  })
_sym_db.RegisterMessage(LandscapeAreaCommon)

RoomUserSeqMessage = _reflection.GeneratedProtocolMessageType('RoomUserSeqMessage', (_message.Message,), {
  'DESCRIPTOR' : _ROOMUSERSEQMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.RoomUserSeqMessage)
  })
_sym_db.RegisterMessage(RoomUserSeqMessage)

CommonTextMessage = _reflection.GeneratedProtocolMessageType('CommonTextMessage', (_message.Message,), {
  'DESCRIPTOR' : _COMMONTEXTMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.CommonTextMessage)
  })
_sym_db.RegisterMessage(CommonTextMessage)

UpdateFanTicketMessage = _reflection.GeneratedProtocolMessageType('UpdateFanTicketMessage', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEFANTICKETMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.UpdateFanTicketMessage)
  })
_sym_db.RegisterMessage(UpdateFanTicketMessage)

RoomUserSeqMessageContributor = _reflection.GeneratedProtocolMessageType('RoomUserSeqMessageContributor', (_message.Message,), {
  'DESCRIPTOR' : _ROOMUSERSEQMESSAGECONTRIBUTOR,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.RoomUserSeqMessageContributor)
  })
_sym_db.RegisterMessage(RoomUserSeqMessageContributor)

GiftMessage = _reflection.GeneratedProtocolMessageType('GiftMessage', (_message.Message,), {
  'DESCRIPTOR' : _GIFTMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.GiftMessage)
  })
_sym_db.RegisterMessage(GiftMessage)

GiftStruct = _reflection.GeneratedProtocolMessageType('GiftStruct', (_message.Message,), {
  'DESCRIPTOR' : _GIFTSTRUCT,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.GiftStruct)
  })
_sym_db.RegisterMessage(GiftStruct)

GiftIMPriority = _reflection.GeneratedProtocolMessageType('GiftIMPriority', (_message.Message,), {
  'DESCRIPTOR' : _GIFTIMPRIORITY,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.GiftIMPriority)
  })
_sym_db.RegisterMessage(GiftIMPriority)

TextEffect = _reflection.GeneratedProtocolMessageType('TextEffect', (_message.Message,), {
  'DESCRIPTOR' : _TEXTEFFECT,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextEffect)
  })
_sym_db.RegisterMessage(TextEffect)

TextEffectDetail = _reflection.GeneratedProtocolMessageType('TextEffectDetail', (_message.Message,), {
  'DESCRIPTOR' : _TEXTEFFECTDETAIL,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextEffectDetail)
  })
_sym_db.RegisterMessage(TextEffectDetail)

MemberMessage = _reflection.GeneratedProtocolMessageType('MemberMessage', (_message.Message,), {
  'DESCRIPTOR' : _MEMBERMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.MemberMessage)
  })
_sym_db.RegisterMessage(MemberMessage)

PublicAreaCommon = _reflection.GeneratedProtocolMessageType('PublicAreaCommon', (_message.Message,), {
  'DESCRIPTOR' : _PUBLICAREACOMMON,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.PublicAreaCommon)
  })
_sym_db.RegisterMessage(PublicAreaCommon)

EffectConfig = _reflection.GeneratedProtocolMessageType('EffectConfig', (_message.Message,), {

  'ExtraMapEntry' : _reflection.GeneratedProtocolMessageType('ExtraMapEntry', (_message.Message,), {
    'DESCRIPTOR' : _EFFECTCONFIG_EXTRAMAPENTRY,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.EffectConfig.ExtraMapEntry)
    })
  ,
  'DESCRIPTOR' : _EFFECTCONFIG,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.EffectConfig)
  })
_sym_db.RegisterMessage(EffectConfig)
_sym_db.RegisterMessage(EffectConfig.ExtraMapEntry)

Text = _reflection.GeneratedProtocolMessageType('Text', (_message.Message,), {
  'DESCRIPTOR' : _TEXT,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Text)
  })
_sym_db.RegisterMessage(Text)

TextPiece = _reflection.GeneratedProtocolMessageType('TextPiece', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPIECE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextPiece)
  })
_sym_db.RegisterMessage(TextPiece)

TextPieceImage = _reflection.GeneratedProtocolMessageType('TextPieceImage', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPIECEIMAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextPieceImage)
  })
_sym_db.RegisterMessage(TextPieceImage)

TextPiecePatternRef = _reflection.GeneratedProtocolMessageType('TextPiecePatternRef', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPIECEPATTERNREF,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextPiecePatternRef)
  })
_sym_db.RegisterMessage(TextPiecePatternRef)

TextPieceHeart = _reflection.GeneratedProtocolMessageType('TextPieceHeart', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPIECEHEART,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextPieceHeart)
  })
_sym_db.RegisterMessage(TextPieceHeart)

TextPieceGift = _reflection.GeneratedProtocolMessageType('TextPieceGift', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPIECEGIFT,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextPieceGift)
  })
_sym_db.RegisterMessage(TextPieceGift)

PatternRef = _reflection.GeneratedProtocolMessageType('PatternRef', (_message.Message,), {
  'DESCRIPTOR' : _PATTERNREF,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.PatternRef)
  })
_sym_db.RegisterMessage(PatternRef)

TextPieceUser = _reflection.GeneratedProtocolMessageType('TextPieceUser', (_message.Message,), {
  'DESCRIPTOR' : _TEXTPIECEUSER,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextPieceUser)
  })
_sym_db.RegisterMessage(TextPieceUser)

TextFormat = _reflection.GeneratedProtocolMessageType('TextFormat', (_message.Message,), {
  'DESCRIPTOR' : _TEXTFORMAT,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.TextFormat)
  })
_sym_db.RegisterMessage(TextFormat)

LikeMessage = _reflection.GeneratedProtocolMessageType('LikeMessage', (_message.Message,), {
  'DESCRIPTOR' : _LIKEMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.LikeMessage)
  })
_sym_db.RegisterMessage(LikeMessage)

SocialMessage = _reflection.GeneratedProtocolMessageType('SocialMessage', (_message.Message,), {
  'DESCRIPTOR' : _SOCIALMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.SocialMessage)
  })
_sym_db.RegisterMessage(SocialMessage)

PicoDisplayInfo = _reflection.GeneratedProtocolMessageType('PicoDisplayInfo', (_message.Message,), {
  'DESCRIPTOR' : _PICODISPLAYINFO,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.PicoDisplayInfo)
  })
_sym_db.RegisterMessage(PicoDisplayInfo)

DoubleLikeDetail = _reflection.GeneratedProtocolMessageType('DoubleLikeDetail', (_message.Message,), {
  'DESCRIPTOR' : _DOUBLELIKEDETAIL,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.DoubleLikeDetail)
  })
_sym_db.RegisterMessage(DoubleLikeDetail)

DisplayControlInfo = _reflection.GeneratedProtocolMessageType('DisplayControlInfo', (_message.Message,), {
  'DESCRIPTOR' : _DISPLAYCONTROLINFO,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.DisplayControlInfo)
  })
_sym_db.RegisterMessage(DisplayControlInfo)

EpisodeChatMessage = _reflection.GeneratedProtocolMessageType('EpisodeChatMessage', (_message.Message,), {
  'DESCRIPTOR' : _EPISODECHATMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.EpisodeChatMessage)
  })
_sym_db.RegisterMessage(EpisodeChatMessage)

MatchAgainstScoreMessage = _reflection.GeneratedProtocolMessageType('MatchAgainstScoreMessage', (_message.Message,), {
  'DESCRIPTOR' : _MATCHAGAINSTSCOREMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.MatchAgainstScoreMessage)
  })
_sym_db.RegisterMessage(MatchAgainstScoreMessage)

Against = _reflection.GeneratedProtocolMessageType('Against', (_message.Message,), {
  'DESCRIPTOR' : _AGAINST,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Against)
  })
_sym_db.RegisterMessage(Against)

Common = _reflection.GeneratedProtocolMessageType('Common', (_message.Message,), {
  'DESCRIPTOR' : _COMMON,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Common)
  })
_sym_db.RegisterMessage(Common)

User = _reflection.GeneratedProtocolMessageType('User', (_message.Message,), {
  'DESCRIPTOR' : _USER,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.User)
  })
_sym_db.RegisterMessage(User)

PayGrade = _reflection.GeneratedProtocolMessageType('PayGrade', (_message.Message,), {
  'DESCRIPTOR' : _PAYGRADE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.PayGrade)
  })
_sym_db.RegisterMessage(PayGrade)

FansClub = _reflection.GeneratedProtocolMessageType('FansClub', (_message.Message,), {

  'PreferDataEntry' : _reflection.GeneratedProtocolMessageType('PreferDataEntry', (_message.Message,), {
    'DESCRIPTOR' : _FANSCLUB_PREFERDATAENTRY,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.FansClub.PreferDataEntry)
    })
  ,
  'DESCRIPTOR' : _FANSCLUB,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.FansClub)
  })
_sym_db.RegisterMessage(FansClub)
_sym_db.RegisterMessage(FansClub.PreferDataEntry)

FansClubData = _reflection.GeneratedProtocolMessageType('FansClubData', (_message.Message,), {
  'DESCRIPTOR' : _FANSCLUBDATA,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.FansClubData)
  })
_sym_db.RegisterMessage(FansClubData)

UserBadge = _reflection.GeneratedProtocolMessageType('UserBadge', (_message.Message,), {

  'IconsEntry' : _reflection.GeneratedProtocolMessageType('IconsEntry', (_message.Message,), {
    'DESCRIPTOR' : _USERBADGE_ICONSENTRY,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.UserBadge.IconsEntry)
    })
  ,
  'DESCRIPTOR' : _USERBADGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.UserBadge)
  })
_sym_db.RegisterMessage(UserBadge)
_sym_db.RegisterMessage(UserBadge.IconsEntry)

GradeBuffInfo = _reflection.GeneratedProtocolMessageType('GradeBuffInfo', (_message.Message,), {
  'DESCRIPTOR' : _GRADEBUFFINFO,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.GradeBuffInfo)
  })
_sym_db.RegisterMessage(GradeBuffInfo)

Border = _reflection.GeneratedProtocolMessageType('Border', (_message.Message,), {
  'DESCRIPTOR' : _BORDER,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Border)
  })
_sym_db.RegisterMessage(Border)

GradeIcon = _reflection.GeneratedProtocolMessageType('GradeIcon', (_message.Message,), {
  'DESCRIPTOR' : _GRADEICON,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.GradeIcon)
  })
_sym_db.RegisterMessage(GradeIcon)

FollowInfo = _reflection.GeneratedProtocolMessageType('FollowInfo', (_message.Message,), {
  'DESCRIPTOR' : _FOLLOWINFO,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.FollowInfo)
  })
_sym_db.RegisterMessage(FollowInfo)

Image = _reflection.GeneratedProtocolMessageType('Image', (_message.Message,), {
  'DESCRIPTOR' : _IMAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Image)
  })
_sym_db.RegisterMessage(Image)

NinePatchSetting = _reflection.GeneratedProtocolMessageType('NinePatchSetting', (_message.Message,), {
  'DESCRIPTOR' : _NINEPATCHSETTING,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.NinePatchSetting)
  })
_sym_db.RegisterMessage(NinePatchSetting)

ImageContent = _reflection.GeneratedProtocolMessageType('ImageContent', (_message.Message,), {
  'DESCRIPTOR' : _IMAGECONTENT,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.ImageContent)
  })
_sym_db.RegisterMessage(ImageContent)

PushFrame = _reflection.GeneratedProtocolMessageType('PushFrame', (_message.Message,), {
  'DESCRIPTOR' : _PUSHFRAME,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.PushFrame)
  })
_sym_db.RegisterMessage(PushFrame)

kk = _reflection.GeneratedProtocolMessageType('kk', (_message.Message,), {
  'DESCRIPTOR' : _KK,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.kk)
  })
_sym_db.RegisterMessage(kk)

SendMessageBody = _reflection.GeneratedProtocolMessageType('SendMessageBody', (_message.Message,), {
  'DESCRIPTOR' : _SENDMESSAGEBODY,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.SendMessageBody)
  })
_sym_db.RegisterMessage(SendMessageBody)

ExtList = _reflection.GeneratedProtocolMessageType('ExtList', (_message.Message,), {
  'DESCRIPTOR' : _EXTLIST,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.ExtList)
  })
_sym_db.RegisterMessage(ExtList)

Rsp = _reflection.GeneratedProtocolMessageType('Rsp', (_message.Message,), {

  'F' : _reflection.GeneratedProtocolMessageType('F', (_message.Message,), {
    'DESCRIPTOR' : _RSP_F,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.Rsp.F)
    })
  ,
  'DESCRIPTOR' : _RSP,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.Rsp)
  })
_sym_db.RegisterMessage(Rsp)
_sym_db.RegisterMessage(Rsp.F)

PreMessage = _reflection.GeneratedProtocolMessageType('PreMessage', (_message.Message,), {
  'DESCRIPTOR' : _PREMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.PreMessage)
  })
_sym_db.RegisterMessage(PreMessage)

HeadersList = _reflection.GeneratedProtocolMessageType('HeadersList', (_message.Message,), {
  'DESCRIPTOR' : _HEADERSLIST,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.HeadersList)
  })
_sym_db.RegisterMessage(HeadersList)

LiveShoppingMessage = _reflection.GeneratedProtocolMessageType('LiveShoppingMessage', (_message.Message,), {
  'DESCRIPTOR' : _LIVESHOPPINGMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.LiveShoppingMessage)
  })
_sym_db.RegisterMessage(LiveShoppingMessage)

RoomStatsMessage = _reflection.GeneratedProtocolMessageType('RoomStatsMessage', (_message.Message,), {
  'DESCRIPTOR' : _ROOMSTATSMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.RoomStatsMessage)
  })
_sym_db.RegisterMessage(RoomStatsMessage)

ProductInfo = _reflection.GeneratedProtocolMessageType('ProductInfo', (_message.Message,), {
  'DESCRIPTOR' : _PRODUCTINFO,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.ProductInfo)
  })
_sym_db.RegisterMessage(ProductInfo)

CategoryInfo = _reflection.GeneratedProtocolMessageType('CategoryInfo', (_message.Message,), {
  'DESCRIPTOR' : _CATEGORYINFO,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.CategoryInfo)
  })
_sym_db.RegisterMessage(CategoryInfo)

ProductChangeMessage = _reflection.GeneratedProtocolMessageType('ProductChangeMessage', (_message.Message,), {
  'DESCRIPTOR' : _PRODUCTCHANGEMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.ProductChangeMessage)
  })
_sym_db.RegisterMessage(ProductChangeMessage)

ControlMessage = _reflection.GeneratedProtocolMessageType('ControlMessage', (_message.Message,), {
  'DESCRIPTOR' : _CONTROLMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.ControlMessage)
  })
_sym_db.RegisterMessage(ControlMessage)

FansclubMessage = _reflection.GeneratedProtocolMessageType('FansclubMessage', (_message.Message,), {
  'DESCRIPTOR' : _FANSCLUBMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.FansclubMessage)
  })
_sym_db.RegisterMessage(FansclubMessage)

RoomRankMessage = _reflection.GeneratedProtocolMessageType('RoomRankMessage', (_message.Message,), {

  'RoomRank' : _reflection.GeneratedProtocolMessageType('RoomRank', (_message.Message,), {
    'DESCRIPTOR' : _ROOMRANKMESSAGE_ROOMRANK,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.RoomRankMessage.RoomRank)
    })
  ,
  'DESCRIPTOR' : _ROOMRANKMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.RoomRankMessage)
  })
_sym_db.RegisterMessage(RoomRankMessage)
_sym_db.RegisterMessage(RoomRankMessage.RoomRank)

RoomMessage = _reflection.GeneratedProtocolMessageType('RoomMessage', (_message.Message,), {

  'BuriedPointMapEntry' : _reflection.GeneratedProtocolMessageType('BuriedPointMapEntry', (_message.Message,), {
    'DESCRIPTOR' : _ROOMMESSAGE_BURIEDPOINTMAPENTRY,
    '__module__' : 'douyin_pb2'
    # @@protoc_insertion_point(class_scope:douyin.RoomMessage.BuriedPointMapEntry)
    })
  ,
  'DESCRIPTOR' : _ROOMMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.RoomMessage)
  })
_sym_db.RegisterMessage(RoomMessage)
_sym_db.RegisterMessage(RoomMessage.BuriedPointMapEntry)

RoomStreamAdaptationMessage = _reflection.GeneratedProtocolMessageType('RoomStreamAdaptationMessage', (_message.Message,), {
  'DESCRIPTOR' : _ROOMSTREAMADAPTATIONMESSAGE,
  '__module__' : 'douyin_pb2'
  # @@protoc_insertion_point(class_scope:douyin.RoomStreamAdaptationMessage)
  })
_sym_db.RegisterMessage(RoomStreamAdaptationMessage)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _RESPONSE_ROUTEPARAMSENTRY._options = None
  _RESPONSE_ROUTEPARAMSENTRY._serialized_options = b'8\001'
  _EFFECTCONFIG_EXTRAMAPENTRY._options = None
  _EFFECTCONFIG_EXTRAMAPENTRY._serialized_options = b'8\001'
  _FANSCLUB_PREFERDATAENTRY._options = None
  _FANSCLUB_PREFERDATAENTRY._serialized_options = b'8\001'
  _USERBADGE_ICONSENTRY._options = None
  _USERBADGE_ICONSENTRY._serialized_options = b'8\001'
  _ROOMMESSAGE_BURIEDPOINTMAPENTRY._options = None
  _ROOMMESSAGE_BURIEDPOINTMAPENTRY._serialized_options = b'8\001'
  _COMMENTTYPETAG._serialized_start=13920
  _COMMENTTYPETAG._serialized_end=13987
  _ROOMMSGTYPEENUM._serialized_start=13990
  _ROOMMSGTYPEENUM._serialized_end=14211
  _RESPONSE._serialized_start=25
  _RESPONSE._serialized_end=381
  _RESPONSE_ROUTEPARAMSENTRY._serialized_start=331
  _RESPONSE_ROUTEPARAMSENTRY._serialized_end=381
  _MESSAGE._serialized_start=384
  _MESSAGE._serialized_end=538
  _EMOJICHATMESSAGE._serialized_start=541
  _EMOJICHATMESSAGE._serialized_end=788
  _CHATMESSAGE._serialized_start=791
  _CHATMESSAGE._serialized_end=1377
  _LANDSCAPEAREACOMMON._serialized_start=1380
  _LANDSCAPEAREACOMMON._serialized_end=1541
  _ROOMUSERSEQMESSAGE._serialized_start=1544
  _ROOMUSERSEQMESSAGE._serialized_end=1935
  _COMMONTEXTMESSAGE._serialized_start=1937
  _COMMONTEXTMESSAGE._serialized_end=2031
  _UPDATEFANTICKETMESSAGE._serialized_start=2034
  _UPDATEFANTICKETMESSAGE._serialized_end=2171
  _ROOMUSERSEQMESSAGECONTRIBUTOR._serialized_start=2174
  _ROOMUSERSEQMESSAGECONTRIBUTOR._serialized_end=2343
  _GIFTMESSAGE._serialized_start=2346
  _GIFTMESSAGE._serialized_end=3163
  _GIFTSTRUCT._serialized_start=3166
  _GIFTSTRUCT._serialized_end=3585
  _GIFTIMPRIORITY._serialized_start=3587
  _GIFTIMPRIORITY._serialized_end=3672
  _TEXTEFFECT._serialized_start=3674
  _TEXTEFFECT._serialized_end=3775
  _TEXTEFFECTDETAIL._serialized_start=3778
  _TEXTEFFECTDETAIL._serialized_end=4088
  _MEMBERMESSAGE._serialized_start=4091
  _MEMBERMESSAGE._serialized_end=4714
  _PUBLICAREACOMMON._serialized_start=4716
  _PUBLICAREACOMMON._serialized_end=4826
  _EFFECTCONFIG._serialized_start=4829
  _EFFECTCONFIG._serialized_end=5491
  _EFFECTCONFIG_EXTRAMAPENTRY._serialized_start=5444
  _EFFECTCONFIG_EXTRAMAPENTRY._serialized_end=5491
  _TEXT._serialized_start=5493
  _TEXT._serialized_end=5617
  _TEXTPIECE._serialized_start=5620
  _TEXTPIECE._serialized_end=5928
  _TEXTPIECEIMAGE._serialized_start=5930
  _TEXTPIECEIMAGE._serialized_end=5997
  _TEXTPIECEPATTERNREF._serialized_start=5999
  _TEXTPIECEPATTERNREF._serialized_end=6057
  _TEXTPIECEHEART._serialized_start=6059
  _TEXTPIECEHEART._serialized_end=6090
  _TEXTPIECEGIFT._serialized_start=6092
  _TEXTPIECEGIFT._serialized_end=6160
  _PATTERNREF._serialized_start=6162
  _PATTERNREF._serialized_end=6211
  _TEXTPIECEUSER._serialized_start=6213
  _TEXTPIECEUSER._serialized_end=6275
  _TEXTFORMAT._serialized_start=6278
  _TEXTFORMAT._serialized_end=6441
  _LIKEMESSAGE._serialized_start=6444
  _LIKEMESSAGE._serialized_end=6774
  _SOCIALMESSAGE._serialized_start=6777
  _SOCIALMESSAGE._serialized_end=6981
  _PICODISPLAYINFO._serialized_start=6983
  _PICODISPLAYINFO._serialized_end=7091
  _DOUBLELIKEDETAIL._serialized_start=7093
  _DOUBLELIKEDETAIL._serialized_end=7188
  _DISPLAYCONTROLINFO._serialized_start=7190
  _DISPLAYCONTROLINFO._serialized_end=7247
  _EPISODECHATMESSAGE._serialized_start=7250
  _EPISODECHATMESSAGE._serialized_end=7450
  _MATCHAGAINSTSCOREMESSAGE._serialized_start=7453
  _MATCHAGAINSTSCOREMESSAGE._serialized_end=7589
  _AGAINST._serialized_start=7592
  _AGAINST._serialized_end=7994
  _COMMON._serialized_start=7997
  _COMMON._serialized_end=8462
  _USER._serialized_start=8465
  _USER._serialized_end=9264
  _PAYGRADE._serialized_start=9267
  _PAYGRADE._serialized_end=10133
  _FANSCLUB._serialized_start=10136
  _FANSCLUB._serialized_end=10309
  _FANSCLUB_PREFERDATAENTRY._serialized_start=10238
  _FANSCLUB_PREFERDATAENTRY._serialized_end=10309
  _FANSCLUBDATA._serialized_start=10312
  _FANSCLUBDATA._serialized_end=10465
  _USERBADGE._serialized_start=10468
  _USERBADGE._serialized_end=10600
  _USERBADGE_ICONSENTRY._serialized_start=10541
  _USERBADGE_ICONSENTRY._serialized_end=10600
  _GRADEBUFFINFO._serialized_start=10602
  _GRADEBUFFINFO._serialized_end=10617
  _BORDER._serialized_start=10619
  _BORDER._serialized_end=10627
  _GRADEICON._serialized_start=10629
  _GRADEICON._serialized_end=10723
  _FOLLOWINFO._serialized_start=10726
  _FOLLOWINFO._serialized_end=10900
  _IMAGE._serialized_start=10903
  _IMAGE._serialized_end=11193
  _NINEPATCHSETTING._serialized_start=11195
  _NINEPATCHSETTING._serialized_end=11238
  _IMAGECONTENT._serialized_start=11240
  _IMAGECONTENT._serialized_end=11327
  _PUSHFRAME._serialized_start=11330
  _PUSHFRAME._serialized_end=11509
  _KK._serialized_start=11511
  _KK._serialized_end=11526
  _SENDMESSAGEBODY._serialized_start=11529
  _SENDMESSAGEBODY._serialized_end=11734
  _EXTLIST._serialized_start=11736
  _EXTLIST._serialized_end=11773
  _RSP._serialized_start=11776
  _RSP._serialized_end=11959
  _RSP_F._serialized_start=11908
  _RSP_F._serialized_end=11959
  _PREMESSAGE._serialized_start=11962
  _PREMESSAGE._serialized_end=12268
  _HEADERSLIST._serialized_start=12270
  _HEADERSLIST._serialized_end=12311
  _LIVESHOPPINGMESSAGE._serialized_start=12313
  _LIVESHOPPINGMESSAGE._serialized_end=12404
  _ROOMSTATSMESSAGE._serialized_start=12407
  _ROOMSTATSMESSAGE._serialized_end=12644
  _PRODUCTINFO._serialized_start=12646
  _PRODUCTINFO._serialized_end=12745
  _CATEGORYINFO._serialized_start=12747
  _CATEGORYINFO._serialized_end=12848
  _PRODUCTCHANGEMESSAGE._serialized_start=12851
  _PRODUCTCHANGEMESSAGE._serialized_end=13072
  _CONTROLMESSAGE._serialized_start=13074
  _CONTROLMESSAGE._serialized_end=13138
  _FANSCLUBMESSAGE._serialized_start=13140
  _FANSCLUBMESSAGE._serialized_end=13252
  _ROOMRANKMESSAGE._serialized_start=13255
  _ROOMRANKMESSAGE._serialized_end=13438
  _ROOMRANKMESSAGE_ROOMRANK._serialized_start=13359
  _ROOMRANKMESSAGE_ROOMRANK._serialized_end=13438
  _ROOMMESSAGE._serialized_start=13441
  _ROOMMESSAGE._serialized_end=13764
  _ROOMMESSAGE_BURIEDPOINTMAPENTRY._serialized_start=13711
  _ROOMMESSAGE_BURIEDPOINTMAPENTRY._serialized_end=13764
  _ROOMSTREAMADAPTATIONMESSAGE._serialized_start=13767
  _ROOMSTREAMADAPTATIONMESSAGE._serialized_end=13918
# @@protoc_insertion_point(module_scope)
