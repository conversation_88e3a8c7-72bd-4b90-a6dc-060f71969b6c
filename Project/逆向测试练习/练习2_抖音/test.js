var bar = 1;
var counter = {
    bar:1,
    fun: function(){
        this.bar++;
    }
}

function Fn(fun,obj){
    fun.call(obj);
    console.log(obj.bar);
}

// Fn(counter.fun.bind(counter), counter);
// Fn(counter.fun.bind(counter), counter);
// Fn(counter.fun.bind(counter), counter);
// Fn(counter.fun.bind(counter), counter);
Fn(counter.fun, counter);
Fn(counter.fun, counter);
Fn(counter.fun, counter);
Fn(counter.fun, counter);
num = Fn(counter.fun, counter);
console.log(bar);

