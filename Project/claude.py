# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/5/10 15:15
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : claude.py
# @explain : 

import json
from curl_cffi import requests
import string
import random
import re

# 自动通过anthropic API 获取 claude SK 密钥
proxies = {
    "http": "http://127.0.0.1:7890",  # 适用于 http 请求
    "https": "http://127.0.0.1:7890", # 适用于 https 请求
}
# 这里的代理可以自己用nodejs搓一个，也可以用curl_cffi
prefix_url = "https://console.anthropic.com"

def generate_password(length):
    if length > 42:
        raise ValueError("Password length must be 42 or fewer characters.")

    # Characters allowed in the password
    characters = string.ascii_lowercase + string.digits + "-"

    # Generate a random password
    password = ''.join(random.choice(characters) for _ in range(length))

    # Ensure it matches the regex pattern
    if re.match(r"^[a-z0-9]([-a-z0-9]{,40}[a-z0-9])?$", password):
        return password
    else:
        return generate_password(length)  # Retry if it doesn't match


def get_org_id(sessionKey):
    """获取组织 ID，处理 API 响应（列表或字典），返回 name : sessionKey : uuid"""
    url = f'{prefix_url}/api/organizations'
    headers = {
        "Cookie": f"sessionKey={sessionKey}",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
    }
    try:
        # 发送 GET 请求
        response = requests.get(url, headers=headers, impersonate="safari17_0",proxies=proxies)

        print(response.json())
        if response.status_code != 200:
            return f"HTTP 错误 {response.status_code} : {sessionKey} : 无 UUID"

        # 解析 JSON 响应
        response_data = response.json()
        # print(f"GET 响应 ({sessionKey}): {response_data}")  # 调试：打印 GET 响应

        # 处理列表响应
        if isinstance(response_data, list) and len(response_data) > 0:
            uuid = response_data[0].get('uuid', '无 UUID')
            # return f"{sessionKey} : {uuid}"
            return uuid
        # 处理字典响应
        elif isinstance(response_data, dict):
            if "error" in response_data:
                return f"账号被封 : {sessionKey} : 无 UUID"
            uuid = response_data.get('uuid', '无 UUID')
            return f"{sessionKey} : {uuid}"
            # print(uuid)
            # return uuid
        else:
            return f"意外的响应类型 : {sessionKey} : 无 UUID"
    except Exception as e:
        return f"错误 {str(e)} : {sessionKey} : 无 UUID"


def create_sk(sessionKey):
    org_id = get_org_id(sessionKey)
    url = f'{prefix_url}/api/console/organizations/{org_id}/workspaces/default/api_keys'

    payload = json.dumps({
        "name": generate_password(10)
    })
    headers = {
        'content-type': 'application/json',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        "Cookie": f"sessionKey={sessionKey}",
    }

    response = requests.post(url, headers=headers, data=payload,proxies=proxies)
    print(response.json())
    if response.status_code == 200:
        claude_sk = sessionKey
        api_sk = response.json()['raw_key']
        org_id = org_id
        print("提取结果")
        print(f'{claude_sk}----{org_id}----{api_sk}')


create_sk("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")
# sk-ant-sid01 是你F12抓包能看到的
# 剩下的自己弄吧
