import re
import requests

base_url = 'https://login.fairies.ai'
fairies_url = 'https://fairies.ai'

session = requests.Session()
# session.headers = {
#     'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
#     'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
#     'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
#     'Upgrade-Insecure-Requests': '1',
#     'Sec-Fetch-Dest': 'document',
#     'Sec-Fetch-Mode': 'navigate',
#     'Sec-Fetch-Site': 'none',
#     'Sec-Fetch-User': '?1',
# }
#
# response = session.get('https://fairies.ai/api/auth/auth0/login?app_type=web', allow_redirects=False)
# redirect_url = response.headers.get('Location')
# print(redirect_url+'\n')
#
# response = session.get(redirect_url, allow_redirects=False)
# redirect_url = response.headers.get('Location')
# print(redirect_url+'\n')
#
# # 如果是相对路径，需要添加base_url
# if redirect_url.startswith('/'):
#     redirect_url = base_url + redirect_url
# state_match = re.search(r'state=([^&]+)', redirect_url)
# state = state_match.group(1)
# print(state+'\n')
#
#
# response = session.get(redirect_url)
# signup_url = f"{base_url}/u/signup/identifier?state={state}"
# print(signup_url)
#
# response = session.get(signup_url)
# print(response.text)




# response = session.get('https://fairies.ai/api/auth/auth0/login?app_type=web', allow_redirects=False)
# redirect_url = response.headers.get('Location')
# print(redirect_url+'\n')

response = session.get("https://login.fairies.ai/authorize?redirect_uri=https%3A%2F%2Ffairies.ai%2Fapi%2Fauth%2Fauth0%2Fcallback%3Fapp_type%3Dweb&client_id=vVeCPG2ODvQrUMU1FG3unzEJGJNhR4Fo&response_type=code&scope=openid+profile+email&app_type=web", allow_redirects=False)

redirect_url = response.headers['Location']

state_match = re.search(r'state=([^&]+)', redirect_url)
state = state_match.group(1)
print(state)

redirect_url = base_url + redirect_url
response = session.get(redirect_url)
# print(response.text)

signup_url = f"{base_url}/u/signup/identifier?state={state}"
response = session.get(signup_url)

params = {
    'state': state,
}

data = {
    'state': state,
    'email': '<EMAIL>',
    'username': 'vbfdaa543vv',
    'action': 'default',
}


response = session.post(signup_url,data=data, params=params,headers={'Content-Type': 'application/x-www-form-urlencoded'})


print(response.url)
print('===========')
print(response.headers)