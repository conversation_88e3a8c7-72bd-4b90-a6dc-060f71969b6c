import json
import time

import ddddocr
import requests
import execjs
import re
from PIL import Image
import io

def get_room_id():

    cookies = {
        '__ac_nonce':'0677cd822009854415249',
    }

    headers = {
        # 'Accept-Encoding': 'gzip, deflate, sdch',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Connection': 'keep-alive',
    }

    fp,cb = execjs.compile(open('D:\pycharm\python_project_exe\Project\wangyi\wangyi.js',encoding='utf-8').read()).call("get_value")

    get_picture_url = f"https://c.dun.163.com/api/v3/get?referer=https%3A%2F%2Fdun.163.com%2Ftrial%2Fjigsaw&zoneId=CN31&dt=mKPJrEOND6tBFwFURFbGO%2F0rq7qlbA%2BL&irToken=kJhgfaY2YLFAZ0BRERbCgUP8UdQjHDqZ&id=07e2387ab53a4d6f930b8d9a9be71bdf&fp={fp}&https=true&type=2&version=2.28.5&dpr=1.25&dev=1&cb={cb}&ipv6=false&runEnv=10&group=&scene=&lang=zh-CN&sdkVersion=&loadVersion=2.5.3&iv=4&user=&width=320&audio=false&sizeType=10&smsVersion=v3&token=&callback=__JSONP_qh0yqu4_0"

#    get_picture_url = f"https://c.dun.163.com/api/v3/get?referer=https%3A%2F%2Fdun.163.com%2Ftrial%2Fjigsaw&zoneId=CN31&dt=I6iOpKssqexBQhQEBVeTxA94ZksVtiZx&irToken=JYeFxFYWmdFBYlFUAUfWwEpp5C%2BFA0Wc&id=a6ab911423aa49b39abd800c30e1f671&fp={fp}&https=true&type=2&version=2.28.5&dpr=1.25&dev=1&cb={cb}&ipv6=false&runEnv=10&group=&scene=&lang=zh-CN&sdkVersion=&loadVersion=2.5.3&iv=4&user=&width=320&audio=false&sizeType=10&smsVersion=v3&token=&callback=__JSONP_29248vi_1"

    response = requests.get(get_picture_url, headers=headers)

    res = response.text

    print('fp==',fp,'\n','cb==',cb,'\n')
    print(res)
    json_match = re.search(r'\((.*)\)', res)
    if json_match:
        json_str = json_match.group(1)

        data = json.loads(json_str)

        # 背景图
        shadeImage_url = data['data']['bg'][0]

        # 方块图
        CutouImage_url = data['data']['front'][0]

        # token值
        token_value = data['data']['token']

        print(f"提取到的背景图链接: {shadeImage_url}")
        print(f"提取到的方块图链接: {CutouImage_url}")
        print(f"token值: {token_value}")

        # --- ddddocr 识别部分 ---

        # 2. 初始化ddddocr，关闭广告和不必要的功能
        ocr = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)

        # 3. 下载背景图和方块图的图片字节流
        print("\n正在下载验证码图片...")
        cutout_image_bytes = requests.get(CutouImage_url).content
        shade_image_bytes = requests.get(shadeImage_url).content
        print("图片下载完成。")

        # 4. 调用 slide_match 进行识别
        print("正在使用ddddocr识别缺口距离...")
        # simple_target 参数通常用于目标简单的滑块
        res = ocr.slide_match(cutout_image_bytes, shade_image_bytes, simple_target=True)

        # 5. 提取并打印x坐标
        distance = res['target'][0]
        print(f"\n>>>>>> 识别出的滑块距离 (x坐标): {distance} <<<<<<")

        # 检查是否成功识别
        if 'target' in res:
            distance = res['target'][0]
            y_coord = res['target'][1] # 有时y坐标也有用
            print(f"\n>>>>>> 识别出的滑块距离 (x坐标): {distance} <<<<<<")

            print("正在生成验证图片...")
            # 从内存中的字节流加载图片
            background_image = Image.open(io.BytesIO(shade_image_bytes))
            slider_image = Image.open(io.BytesIO(cutout_image_bytes))

            # 将滑块图粘贴到背景图的指定位置
            # 第一个参数是滑块图，第二个是位置元组(x, y)
            # 第三个参数是遮罩，使用滑块图自身作为遮罩可以处理透明度
            background_image.paste(slider_image, (distance, y_coord), slider_image)

            # 保存结果图以便查看
            result_filename = "verification_result.png"
            background_image.save(result_filename)
            print(f"验证图片已保存为: {result_filename}")
            print("请打开该图片，检查滑块是否准确地填补了缺口。")

        else:
            print("ddddocr未能成功识别目标位置。")

if __name__ == '__main__':
    get_room_id()