# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/6/18 15:32
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : ucsc.py
# @explain : 

#!/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/6/18 15:32
# <AUTHOR> shaocanfan
# @File    : ucsc.py
# @explain :

import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException, ElementClickInterceptedException
import time

def click_element_by_id(driver, element_id, timeout=15, max_retries=3):
    """使用显式等待点击指定 ID 的元素，处理点击被拦截的异常，并滚动到元素可见位置，增加小延时"""
    retries = 0
    while retries < max_retries:
        try:
            wait = WebDriverWait(driver, timeout)
            element = wait.until(EC.presence_of_element_located((By.ID, element_id)))
            driver.execute_script("arguments[0].scrollIntoView({behavior: 'auto', block: 'center', inline: 'center'});", element) # 滚动到元素中心
            time.sleep(0.1)  # 增加一个极小的延时，确保滚动完成
            wait.until(EC.element_to_be_clickable((By.ID, element_id))).click()
            print(f"成功点击了 ID 为 '{element_id}' 的元素")
            return  # 点击成功，退出循环
        except NoSuchElementException:
            print(f"找不到 ID 为 '{element_id}' 的元素")
            return
        except TimeoutException:
            print(f"等待 ID 为 '{element_id}' 的元素超时")
            return
        except ElementClickInterceptedException:
            retries += 1
            print(f"点击 ID 为 '{element_id}' 的元素被拦截，正在重试 ({retries}/{max_retries})...")
            time.sleep(2)  # 增加重试的间隔时间
        except Exception as e:
            print(f"点击 ID 为 '{element_id}' 的元素时发生错误: {e}")
            return
    print(f"点击 ID 为 '{element_id}' 的元素多次尝试失败")

def input_text_by_id(driver, element_id, text_to_input, timeout=15):
    """使用显式等待向指定 ID 的文本输入框输入文本"""
    try:
        wait = WebDriverWait(driver, timeout)
        input_field = wait.until(EC.presence_of_element_located((By.ID, element_id)))
        input_field.send_keys(text_to_input)
        print(f"成功向 ID 为 '{element_id}' 的输入框输入了 '{text_to_input}'")
    except NoSuchElementException:
        print(f"找不到 ID 为 '{element_id}' 的输入框")
    except TimeoutException:
        print(f"等待 ID 为 '{element_id}' 的输入框超时")
    except Exception as e:
        print(f"向 ID 为 '{element_id}' 的输入框输入文本时发生错误: {e}")

def select_option_by_id_and_text(driver, select_id, visible_text, timeout=15):
    """使用显式等待根据可见文本选择指定 ID 的下拉选择框中的选项"""
    try:
        wait = WebDriverWait(driver, timeout)
        select_element = wait.until(EC.presence_of_element_located((By.ID, select_id)))
        select = Select(select_element)
        select.select_by_visible_text(visible_text)
        print(f"成功在 ID 为 '{select_id}' 的下拉框中选择了 '{visible_text}'")
    except NoSuchElementException:
        print(f"找不到 ID 为 '{select_id}' 的下拉选择框")
    except TimeoutException:
        print(f"等待 ID 为 '{select_id}' 的下拉选择框超时")
    except Exception as e:
        print(f"在 ID 为 '{select_id}' 的下拉框中选择 '{visible_text}' 时发生错误: {e}")


def click_element_by_xpath(driver, xpath_expression, timeout=15, max_retries=3):
    """使用显式等待点击指定 XPath 的元素，处理点击被拦截的异常，并滚动到元素可见位置，增加小延时"""
    retries = 0
    while retries < max_retries:
        try:
            wait = WebDriverWait(driver, timeout)
            element = wait.until(EC.presence_of_element_located((By.XPATH, xpath_expression)))
            driver.execute_script("arguments[0].scrollIntoView({behavior: 'auto', block: 'center', inline: 'center'});", element) # 滚动到元素中心
            time.sleep(0.1)  # 增加一个极小的延时，确保滚动完成
            wait.until(EC.element_to_be_clickable((By.XPATH, xpath_expression))).click()
            print(f"成功点击了 XPath 为 '{xpath_expression}' 的元素")
            return  # 点击成功，退出循环
        except NoSuchElementException:
            print(f"找不到 XPath 为 '{xpath_expression}' 的元素")
            return
        except TimeoutException:
            print(f"等待 XPath 为 '{xpath_expression}' 的元素超时")
            return
        except ElementClickInterceptedException:
            retries += 1
            print(f"点击 XPath 为 '{xpath_expression}' 的元素被拦截，正在重试 ({retries}/{max_retries})...")
            time.sleep(2)  # 增加重试的间隔时间
        except Exception as e:
            print(f"点击 XPath 为 '{xpath_expression}' 的元素时发生错误: {e}")
            return
    print(f"点击 XPath 为 '{xpath_expression}' 的元素多次尝试失败")

# 主程序
if __name__ == "__main__":
    # Excel 文件路径
    excel_file_path = "ucsc.xlsx"
    # Excel 文件中的 sheet 名称
    sheet_name = "Sheet1"

    try:
        # 使用 pandas 读取 Excel 文件
        df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

        # 遍历 DataFrame 的每一行
        for index, row in df.iterrows():
            print(f"\n--- 处理第 {index + 1} 行数据 ---")
            # 启动 Chrome WebDriver
            driver = webdriver.Chrome()
            wait_time = 25

            try:
                # 打开目标网站
                driver.get("https://registrations.summer.ucsc.edu/visiting_reg")
                time.sleep(2)

                # 根据 Excel 中的列名获取数据并填写表单
                click_element_by_id(driver, "edit-currently-attending-uc-n", wait_time)
                select_option_by_id_and_text(driver, "edit-other-institution", "None of the Above", wait_time)

                full_ssn = row.get('full_ssn', '')
                input_text_by_id(driver, "edit-full-ssn", str(int(full_ssn)) if pd.notna(full_ssn) else '', wait_time)


                click_element_by_id(driver, "edit-applied-attended-before-n", wait_time)

                first_name = row.get('first_name', '')
                input_text_by_id(driver, "edit-first-name", first_name, wait_time)

                last_name = row.get('last_name', '')
                input_text_by_id(driver, "edit-last-name", last_name, wait_time)

                date_of_birth = row.get('date_of_birth', '')
                input_text_by_id(driver, "edit-date-of-birth", date_of_birth, wait_time)

                gender = row.get('gender', '')
                if pd.notna(gender):
                    select_option_by_id_and_text(driver, "edit-gender", gender, wait_time)

                phone_number = row.get('phone_number', '')
                input_text_by_id(driver, "edit-phone-number", phone_number, wait_time)

                e_mail_address = row.get('e_mail_address', '')
                input_text_by_id(driver, "edit-e-mail-address", e_mail_address, wait_time)

                confirm_e_mail_address = row.get('confirm_e_mail_address', '')
                input_text_by_id(driver, "edit-confirm-e-mail-address", confirm_e_mail_address, wait_time)

                address = row.get('address', '')
                input_text_by_id(driver, "edit-address", address, wait_time)

                city = row.get('city', '')
                input_text_by_id(driver, "edit-city", city, wait_time)

                country = row.get('country', '')
                if pd.notna(country):
                    select_option_by_id_and_text(driver, "edit-country", country, wait_time)

                us_state = row.get('us_state', '')
                if pd.notna(us_state) and country == 'United States':
                    select_option_by_id_and_text(driver, "edit-us-state", us_state, wait_time)

                zip_code = row.get('zip', '')
                input_text_by_id(driver, "edit-zip", str(zip_code) if pd.notna(zip_code) else '', wait_time)

                # 点击各种复选框 - 假设 Excel 中有相应的列
                click_element_by_id(driver, "edit-fees", wait_time)
                click_element_by_id(driver, "edit-pay", wait_time)
                click_element_by_id(driver, "edit-dropped", wait_time)
                click_element_by_id(driver, "edit-seat", wait_time)
                click_element_by_id(driver, "edit-attend", wait_time)
                click_element_by_id(driver, "edit-waitlist", wait_time)
                click_element_by_id(driver, "edit-nofall", wait_time)
                click_element_by_id(driver, "edit-consequences", wait_time)
                click_element_by_id(driver, "edit-rules", wait_time)

                # ==================== AUTOMATIC DETECTION STEP ====================
                print("SCRIPT PAUSED: Now waiting for you to manually click the 'Next Step' (id='edit-save') button...")

                try:
                    # First, get a reference to the button we are waiting for you to click.
                    button_to_watch = WebDriverWait(driver, 15).until(
                        EC.presence_of_element_located((By.ID, "edit-save"))
                    )

                    wait = WebDriverWait(driver, 600)
                    wait.until(EC.staleness_of(button_to_watch))
                    time.sleep(2)
                    print("\nManual click on 'edit-save' DETECTED! Proceeding to the next item.")

                except TimeoutException:
                    print("\nWaited for 10 minutes, no click was detected. Moving to the next item anyway.")
                except Exception as e:
                    print(f"\nAn error occurred while waiting for the manual click: {e}")
                # ======================================================================

            except Exception as e:
                print(f"An error occurred during the automation for row {index + 1}: {e}")
            finally:
                # Whether the click was detected or not, we close the browser and move on.
                print("Closing the browser for this item.")
                driver.quit()

        print("\n--- 批量填写完成 ---")

    except FileNotFoundError:
        print(f"找不到 Excel 文件: {excel_file_path}")
    except Exception as e:
        print(f"读取 Excel 文件时发生错误: {e}")