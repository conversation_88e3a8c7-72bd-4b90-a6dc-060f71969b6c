# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/5/17 15:12
# <AUTHOR> shaocanfan
# @File    : csuci.py
# @explain :
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import (
    TimeoutException,
    WebDriverException,
    ElementNotInteractableException,
    InvalidElementStateException,
    NoSuchElementException
)
import time


# -----------------------------------------------------------------------------
# 函数 1: open_portal_and_click_new_student (简化版)
# -----------------------------------------------------------------------------
def open_portal_and_click_new_student(url: str) -> webdriver.Chrome | None:
    driver = None
    try:
        driver_service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=driver_service)
        driver.maximize_window()
        driver.get(url)

        WebDriverWait(driver, 20).until(
            EC.frame_to_be_available_and_switch_to_it((By.ID, "ptifrmtgtframe"))
        )

        new_student_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.ID, "CSU_DERIVED_SQA_CSU_SQA_NEW_STDNT"))
        )
        new_student_button.click()

        WebDriverWait(driver, 30).until(
            EC.invisibility_of_element_located((By.ID, "WAIT_win0"))
        )
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "CSU_SQA_REG_WRK_FIRST_NAME"))
        )
        print("门户打开成功，并已导航到新学生表单。")
        return driver
    except Exception as e:
        print(f"错误：初始化导航失败 - {e}")
        if driver:
            driver.quit()
        return None


# -----------------------------------------------------------------------------
# 函数 2: enter_text_into_field (简化版)
# -----------------------------------------------------------------------------
def enter_text_into_field(driver: webdriver.Chrome, field_id: str, text_to_enter: str,
                          clear_first: bool = True) -> bool:
    if not driver: return False
    try:
        input_field = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.ID, field_id))
        )
        driver.execute_script("arguments[0].scrollIntoView(true);", input_field)
        time.sleep(0.1)

        if clear_first:
            input_field.clear()
            time.sleep(0.1)
        input_field.send_keys(text_to_enter)
        return True
    except (ElementNotInteractableException, InvalidElementStateException):
        try:
            input_field_js = driver.find_element(By.ID, field_id)
            if clear_first:
                driver.execute_script("arguments[0].value = '';", input_field_js)
            driver.execute_script("arguments[0].value = arguments[1];", input_field_js, text_to_enter)
            for event_name in ['input', 'change']:
                driver.execute_script("arguments[0].dispatchEvent(new Event(arguments[1], { bubbles: true }));",
                                      input_field_js, event_name)
            return True
        except Exception as e_js:
            print(f"  -> 错误: JS输入 (ID: {field_id}) 失败: {e_js}")
            return False
    except TimeoutException:
        print(f"  -> 错误: 等待输入框 (ID: {field_id}) 超时。")
        return False
    except Exception as e:
        print(f"  -> 错误: 输入文本到 (ID: {field_id}) 时发生: {e}")
        return False


# -----------------------------------------------------------------------------
# 函数 3: select_option_by_value_from_dropdown (简化版)
# -----------------------------------------------------------------------------
def select_option_by_value_from_dropdown(driver: webdriver.Chrome, dropdown_id: str, value_to_select: str) -> bool:
    if not driver: return False
    try:
        dropdown_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, dropdown_id))
        )
        driver.execute_script("arguments[0].scrollIntoView(true);", dropdown_element)
        time.sleep(0.1)

        select_obj = Select(dropdown_element)
        select_obj.select_by_value(value_to_select)
        return True
    except TimeoutException:
        print(f"  -> 错误: 等待下拉框 (ID: {dropdown_id}) 超时。")
        return False
    except NoSuchElementException:
        print(f"  -> 错误: 在下拉框 (ID: {dropdown_id}) 中未找到值 '{value_to_select}'。")
        return False
    except Exception as e:
        print(f"  -> 错误: 从下拉框 (ID: {dropdown_id}) 选择时发生: {e}")
        return False


# -----------------------------------------------------------------------------
# 函数 4: click_element_by_id (简化版)
# -----------------------------------------------------------------------------
def click_element_by_id(driver: webdriver.Chrome, element_id: str, description: str) -> bool:
    if not driver: return False
    try:
        element_to_click = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.ID, element_id))
        )
        driver.execute_script("arguments[0].scrollIntoView(true);", element_to_click)
        time.sleep(0.1)
        element_to_click.click()
        return True
    except ElementNotInteractableException:
        try:
            element_js = driver.find_element(By.ID, element_id)
            driver.execute_script("arguments[0].click();", element_js)
            return True
        except Exception as e_js:
            print(f"  -> 错误: JS点击 '{description}' (ID: {element_id}) 失败: {e_js}")
            return False
    except TimeoutException:
        print(f"  -> 错误: 等待 '{description}' (ID: {element_id}) 超时或不可点击。")
        return False
    except Exception as e:
        print(f"  -> 错误: 点击 '{description}' (ID: {element_id}) 时发生: {e}")
        return False


# -----------------------------------------------------------------------------
# 主程序 / 示例用法 (简化版)
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    portal_url = "https://cmsweb.csuci.edu/psp/CCIPRD/EMPLOYEE/SA/c/CI_SR_CUSTOM.CI_SSS_QKADMIT_CMP.GBL"
    excel_file_path = "registration_data.xlsx"  # User should create this file
    sheet_name = "Sheet1"

    CHECKBOX_ID_TO_TICK = "CI_SSS_QKA_WORK_CONSENT"
    CONTINUE_BUTTON_ID = "CSU_DERIVED_SQA_CONTINUE_PB"
    SUCCESS_MESSAGE_DIV_ID = "win0div$ICField12"
    SUCCESS_MESSAGE_PARTIAL_TEXT = "Your submission will be processed"

    # Template for fields, linking IDs to Excel column names
    # Gender is static and handled separately or included with a note.
    field_mapping = [
        {"id": "CSU_SQA_REG_WRK_FIRST_NAME", "excel_column": "FIRST_NAME", "description": "名字"},
        {"id": "CSU_SQA_REG_WRK_LAST_NAME", "excel_column": "LAST_NAME", "description": "姓氏"},
        {"id": "CSU_SQA_REG_WRK_BIRTHDATE", "excel_column": "BIRTHDATE", "description": "出生日期"},  # Expects YYYY/MM/DD
        {"id": "CSU_SQA_REG_WRK_SSN", "excel_column": "SSN", "description": "SSN"},  # Expects XXX-XX-XXXX
        {"id": "CSU_SQA_REG_WRK_ADDRESS1", "excel_column": "ADDRESS1", "description": "地址1"},
        {"id": "CSU_SQA_REG_WRK_CITY", "excel_column": "CITY", "description": "城市"},
        {"id": "CI_SSS_QKA_WORK_POSTAL", "excel_column": "POSTAL", "description": "邮政编码"},
        {"id": "CSU_SQA_REG_WRK_CONTACT_PHONE", "excel_column": "CONTACT_PHONE", "description": "联系电话"},
        # Expects XXX-XXX-XXXX
        {"id": "CI_SSS_QKA_WORK_EMAIL_ADDR", "excel_column": "EMAIL_ADDR", "description": "邮箱地址"}
    ]
    static_gender_field = {"id": "CSU_SQA_REG_WRK_SEX", "value": "M", "description": "性别",
                           "action_type": "dropdown_value"}

    try:
        df = pd.read_excel("information.xlsx", sheet_name=sheet_name,
                           dtype=str)  # Read all as string to preserve formats like SSN, postal
        # If BIRTHDATE needs to be parsed from Excel dates, then don't use dtype=str globally,
        # or convert specific columns later. For YYYY/MM/DD text, dtype=str is fine.
    except FileNotFoundError:
        print(f"错误: Excel文件 '{excel_file_path}' 未找到。请创建此文件并填充数据。")
        exit()
    except Exception as e:
        print(f"错误: 读取Excel文件失败: {e}")
        exit()

    print(f"--- 从 '{excel_file_path}' 读取到 {len(df)} 条记录 ---")

    for index, row_data in df.iterrows():
        print(f"\n--- 正在处理记录 {index + 1} / {len(df)} ---")
        # Extract data for current row, using .get() for safety if columns are missing
        print(f"数据: {row_data.to_dict()}")

        browser = open_portal_and_click_new_student(portal_url)
        if not browser:
            print(f"错误: 记录 {index + 1} 未能启动浏览器或导航到表单。跳过此记录。")
            continue  # Move to the next row in Excel

        print("\n--- 正在填写表单字段 ---")
        current_fields_to_fill = []
        for fm in field_mapping:
            field_value = row_data.get(fm["excel_column"])
            if field_value is None or (isinstance(field_value, str) and not field_value.strip()):
                print(f"  警告: 记录 {index + 1} 的列 '{fm['excel_column']}' 为空或仅包含空格。")
                field_value = ""  # Use empty string if data is missing or blank

            # Handle BIRTHDATE formatting (assuming Excel might have it as datetime)
            if fm["id"] == "CSU_SQA_REG_WRK_BIRTHDATE":
                try:
                    # If pandas parsed it as a datetime object despite dtype=str (unlikely but good to check)
                    # or if it was already a string in a different date format.
                    # For now, assume it's a string "YYYY/MM/DD" from Excel due to dtype=str.
                    # If it's a pandas Timestamp (e.g., if dtype=str was not used and Excel had dates)
                    if isinstance(field_value, pd.Timestamp):
                        field_value = field_value.strftime('%Y/%m/%d')
                    # else, assume it's already a string in the correct format or needs to be YYYY/MM/DD
                    # No specific conversion here if it's already a string. User must ensure format.
                except Exception as e_date:
                    print(f"  警告: 转换出生日期时出错 (原始值: {field_value}): {e_date}. 将按原样使用。")
                    pass  # Use as is, or handle error more strictly

            current_fields_to_fill.append({
                "id": fm["id"],
                "value": str(field_value),  # Ensure it's a string for send_keys
                "description": fm["description"],
                "action_type": "text"
            })
        current_fields_to_fill.append(static_gender_field)  # Add the static gender field

        actions_successful_for_row = True
        for field_data in current_fields_to_fill:
            if not actions_successful_for_row: break

            print(f"处理中: {field_data['description']} (ID: {field_data['id']})")
            success_current_field = False
            action_type = field_data.get("action_type", "text")

            if action_type == "text":
                if enter_text_into_field(browser, field_data['id'], field_data['value']):
                    # print(f"  OK: {field_data['description']} = '{field_data['value']}'") # Verbose
                    success_current_field = True
            elif action_type == "dropdown_value":
                if select_option_by_value_from_dropdown(browser, field_data['id'], field_data['value']):
                    # print(f"  OK: {field_data['description']} 选择值 '{field_data['value']}'") # Verbose
                    success_current_field = True

            if success_current_field:
                print(f"  OK: {field_data['description']} 设置为 '{field_data['value']}'")
            else:
                actions_successful_for_row = False
                print(f"  失败: 未能设置 {field_data['description']}")

        if actions_successful_for_row:
            print("\n表单字段填写完成。")
        else:
            print(f"\n记录 {index + 1} 表单字段填写过程中出现错误。跳过提交。")
            browser.quit()
            continue  # Next record

        # --- 勾选复选框 ---
        if CHECKBOX_ID_TO_TICK:
            print(f"处理中: 复选框 (ID: {CHECKBOX_ID_TO_TICK})")
            if click_element_by_id(browser, CHECKBOX_ID_TO_TICK, "复选框"):
                print(f"  OK: 复选框已勾选。")
            else:
                actions_successful_for_row = False
                print(f"  失败: 未能勾选复选框。")

        if not actions_successful_for_row:
            print(f"记录 {index + 1} 未能勾选复选框。跳过提交。")
            browser.quit()
            continue

        # --- 点击继续按钮并验证成功信息 ---
        if CONTINUE_BUTTON_ID:
            print(f"处理中: 继续按钮 (ID: {CONTINUE_BUTTON_ID})")
            if click_element_by_id(browser, CONTINUE_BUTTON_ID, "继续按钮"):
                print(f"  OK: “继续”按钮已点击。正在等待提交确认...")
                try:
                    success_message_locator = (By.ID, SUCCESS_MESSAGE_DIV_ID)
                    WebDriverWait(browser, 45).until(  # Increased timeout for submission processing
                        EC.presence_of_element_located(success_message_locator)
                    )
                    WebDriverWait(browser, 10).until(
                        EC.text_to_be_present_in_element(success_message_locator, SUCCESS_MESSAGE_PARTIAL_TEXT)
                    )
                    print(f"  成功: 记录 {index + 1} 提交成功！确认信息 '{SUCCESS_MESSAGE_PARTIAL_TEXT}' 已找到。")
                except TimeoutException:
                    print(
                        f"  错误: 记录 {index + 1} 提交后未在ID '{SUCCESS_MESSAGE_DIV_ID}' 中检测到预期的成功文本 '{SUCCESS_MESSAGE_PARTIAL_TEXT}'。请手动检查此条记录的浏览器状态。")
                    # Keep browser open for this failed submission for manual check if needed
                    input(f"记录 {index + 1} 提交失败或未确认。请检查浏览器，然后按 Enter 继续处理下一条记录或关闭...")
                    actions_successful_for_row = False  # Mark as failed for this row
            else:
                actions_successful_for_row = False  # Click continue button failed
                print(f"  失败: 点击“继续”按钮失败。")

        if actions_successful_for_row:
            print(f"\n记录 {index + 1} 所有操作成功完成。")
        else:
            print(f"\n记录 {index + 1} 自动化流程中存在错误或未能确认成功。")

        # Decide whether to keep browser open or quit per iteration
        # For full automation, quit. For debugging, maybe prompt.
        # Let's make it quit by default after each attempt.
        # If a submission failed to confirm, we already prompted above.
        print("\n--- 正在关闭当前记录的浏览器 ---")
        browser.quit()
        print("当前浏览器已关闭。")
        time.sleep(2)  # Small pause before next record

    print("\n--- 所有记录处理完毕 ---")
    # No final input prompt, script ends.

