const CryptoJs = require("crypto-js")



// a = ",live_id=1,aid=6383,version_code=180800,webcast_sdk_version=1.0.14-beta.0,room_id=7527479662918814498,sub_room_id=,sub_channel_id=,did_rule=3,user_unique_id=7310123775072175650,device_platform=web,device_type=,ac=,identity=audience"
// b = ",live_id=1,aid=6383,version_code=180800,webcast_sdk_version=1.0.14-beta.0,room_id=7527520773150395187,sub_room_id=,sub_channel_id=,did_rule=3,user_unique_id=7310123775072175650,device_platform=web,device_type=,ac=,identity=audience"
// c = a.substring(1)
// d = b.substring(1)
// console.log(c)
// console.log("================")
// console.log(d)

    function get_sign(){
        let o = ",live_id=1,aid=6383,version_code=180800,webcast_sdk_version=1.0.14-beta.0,room_id=7527520773150395187,sub_room_id=,sub_channel_id=,did_rule=3,user_unique_id=7310123775072175650,device_platform=web,device_type=,ac=,identity=audience"
        o = o.substring(1)
        let md5_data = CryptoJs.MD5(o).toString()
        return md5_data
    }

console.log(get_sign())