# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/17 0:34
# <AUTHOR> shaocan<PERSON>
# @File    : test.py
# @explain : 




import base64

hmac_bytes = base64.b64decode("PSSg6DCZM8J2qcsNudhFX86fX0Pvwtl15uDfxG1e5zQ=")
print(len(hmac_bytes)) # 32
print(hmac_bytes.hex())
key_bytes = base64.b64decode("AQIDAHiVUsqkwSBqpf819sm950Ar+PDhDc0xIJZ8mSdeIzXqswFWyUNTcRm/bVQsvDbvcWrdAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMQeWXmcolEuUwsU2xAgEQgDu0WmTkiisWbAHL8DEXTSBZYOMY07I6Y3YLL3smxAIRTrDZhS4Um1j+aGtcKb+trHq9VQJuek1tPiOTaw==")
print(len(key_bytes))  # 看看字节长度


