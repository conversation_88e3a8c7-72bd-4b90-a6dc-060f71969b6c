# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/10 15:26
# <AUTHOR> shaocanfan
# @File    : outlook和moemail.py
# @explain :


# import requests
# import json  # 引入json库，方便格式化打印输出
#
# # 1. 定义目标 URL
# # 我们使用 httpbin.org/post，它会将我们的POST请求数据返回给我们
# url = 'https://moemail.app/api/auth/callback/credentials?'
#
# # =================================================================
# # 示例 1: 发送表单数据 (Content-Type: application/x-www-form-urlencoded)
# # =================================================================
# print("--- 示例 1: 发送表单数据 ---")
#
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#     'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
#     'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
#
# }
#
# # 定义要发送的表单数据，使用一个字典
# form_data = {
#     'username': 'ioioqwe123432fdd',
#     'password': 'ioioqwe123432fdd',
#     'csrfToken': '1d746357c391a080b2519bef4d4b8703af88783960d7aa2d7e57c58f1890517c',
#     'callbackUrl':'https://mail.blny.de/login',
# }
#
# cookie = {
#     '__Host-authjs.csrf-token':'cea1db6473a238361b2aad71d36c7a633a4fd5a5316d325136ac13c83a50ec79%7C3c5bb10b93f7f8857f5be5e4dd6886079891601852259ecc2877d28707498f4e; __Secure-authjs.callback-url=https%3A%2F%2Fmoemail.app%2Flogin',
#     '__Secure-authjs.session-token':'eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiRjdFSmYtTm1SQ2ZaVmM4eGhfaFBYUVJ6RUk0MjRsRW1yUGs0Ti1nZ2pCVnVZVkUxX3lFbHFOQnc1NVVnTng3blA1cEFxTVZNLUlvdU9ndHF0VkwtV2cifQ..qqLrXiSu1j_UPtEtRLZ66w.PXJA6tnBVRDDMv2rVI1RdTS5kC38t3LgBQXNcy_dHYtgLxjlvBYHb9WYFbV8WK3QhIpmF3bOSw6Fcz4bR9wu0M_6eFexwJFwAYWNXznald_-ALjbGByqsexIPudKhwPa3xwE9Sah-Q3y_sY8MmzM8R1jrUIKZu7FqdgVPbKH-3iTm3Hcl9BrkCQxCF0jjKHcnx_toISCcsp0PTJoRF3CqMQwXbgobD8A-jMBztfMNxkvL7O8IwavRCIILyuxd0_m9trwjuXkHmTfP9fMV5DPcBtHT6QM7G85bl98TX6xquUE8o8pXFDgD5jm3RjbvwMwKCZ1yPbnR2-chfTxDsTvzRma4Dse47_U8ABO9_tvriug7wEMaIEtV_ziBbeNtjEnh-essNXP9uMJm7A3qZcQa0kW61GRExnWSfsEANQAu8eqelWgRnaq8zjMsC6yo46TD0OQBtIvhbo_oB-rqI4vGw9BfdsMQiNSrYHLKrex-LFPYMGUAqiTWbZrYoWsc7PSBzuGclVoZSbwJY69DVdZ2Wg0NbVct7yewa_yXH0PqKXqesWVah_JkSVF4oHbDqDGs1HmEcn3DWxt7Oqv54G3dwbr2lBwuMonhJ6MSkQ6LJ7fz-Z7RoqBdwPCgduwKDje6wqtVI0C6wh5y8uRJilS7U4WshAOkXwNYfv6KMOAmftLq92ZfHupt7RGYU83jXGTJ2kIcC3ZS2KcS7ZxRwppFg4hBKKKqXGZsRu6f9Cw_FrCNwMm7qcEn43hwAvSrwXQjRoY3L8SieyIQz93Gxsg7EbXYtS1L72nEhmLjB8N_WUTQpF1hmVmHokuxghp7ECeV2lt0BKBUydRCbmkP9yU1zpODx_pEBs9IbqaY8Tw0eW0XIi0fTPEMScAA8vxeBZwlZqciqHwSMe_kGHLLSlY0W2dCOVp3E46NtGubVvkC0dDuSzio_aoD6PTJBaJKxarLNkZOlLYcFw36ewZqi5ANhm2CzbibVkJSIQiKAv9IPDmLufvI3Kw6MkaIoXdmL5kwN3t5ZVXnqvfrNSrOPtG2AK0bLRa_aPuR1vaHh5CaU-GQAA2oBI_Efmy5n_jDFKzjXqE1Ju7v2Pt7olme5op1ZeQOOida6INEVhZVqZoNfRlcm-VpbboXI58V4fCz7Dk1NYeYu93a2_iq5nwBT5Iw6msdz1NO82d47Kijo9wJfwThMXr9_cddGnLHMSEk_A5lN_kcgXAJBKu65vmFCaeLe9DbvWYDrz6z1pR95UBRO13YTwruJsQkH0huvszuzp8lE39HfJ7BOIQz6Oeihudyg.L800Fzxmt6IR-3CJJZtwUXDki9v95yUcvBG2TORNt_k; cfzs_google-analytics_v4=%7B%22nYPa_pageviewCounter%22%3A%7B%22v%22%3A%224%22%7D%7D',
#     'cfz_google-analytics_v4':'%7B%22nYPa_engagementDuration%22%3A%7B%22v%22%3A%220%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_engagementStart%22%3A%7B%22v%22%3A%221752137725439%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_counter%22%3A%7B%22v%22%3A%224%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_ga4sid%22%3A%7B%22v%22%3A%221801985518%22%2C%22e%22%3A1752139525439%7D%2C%22nYPa_session_counter%22%3A%7B%22v%22%3A%221%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_ga4%22%3A%7B%22v%22%3A%2248188387-d2f4-4c03-99e4-fe9bc5369be4%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_let%22%3A%7B%22v%22%3A%221752137725439%22%2C%22e%22%3A1783673725439%7D%7D',
#
# }
#
# try:
#     # 使用 requests.post() 发送请求
#     # 将字典传递给 `data` 参数，requests会自动将其编码为表单格式
#     response_form = requests.post(url,data=form_data,headers=headers, cookies=cookie)
#
#     # 检查响应状态码是否为 200 (OK)
#     if response_form.status_code == 200:
#         print("表单数据发送成功!")
#         # httpbin返回的是JSON格式的字符串，使用.json()方法可以将其解析为Python字典
#         # response_data = response_form.json() ###
#
#         # 使用json.dumps美化输出
#         print("服务器返回内容:")
#         # print(json.dumps(response_data, indent=2, ensure_ascii=False)) ###
#         print(response_form.cookies)
#         # 你可以特别关注返回内容中的 'form' 字段，它应该和你发送的一致
#         # assert response_data['form'] == form_data
#
#     else:
#         print(f"请求失败，状态码: {response_form.status_code}")
#
# except requests.exceptions.RequestException as e:
#     print(f"请求发生错误: {e}")
#
# print("\n" + "=" * 50 + "\n")











# import requests
# import pandas as pd
# import json
#
#
# def fetch_and_save_emails_to_excel(api_url, output_filename):
#     headers = {
#         'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#         'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
#         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
#
#     }
#
#     cookie = {
#         '__Host-authjs.csrf-token': 'cea1db6473a238361b2aad71d36c7a633a4fd5a5316d325136ac13c83a50ec79%7C3c5bb10b93f7f8857f5be5e4dd6886079891601852259ecc2877d28707498f4e; __Secure-authjs.callback-url=https%3A%2F%2Fmoemail.app%2Flogin',
#         '__Secure-authjs.session-token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiRjdFSmYtTm1SQ2ZaVmM4eGhfaFBYUVJ6RUk0MjRsRW1yUGs0Ti1nZ2pCVnVZVkUxX3lFbHFOQnc1NVVnTng3blA1cEFxTVZNLUlvdU9ndHF0VkwtV2cifQ..qqLrXiSu1j_UPtEtRLZ66w.PXJA6tnBVRDDMv2rVI1RdTS5kC38t3LgBQXNcy_dHYtgLxjlvBYHb9WYFbV8WK3QhIpmF3bOSw6Fcz4bR9wu0M_6eFexwJFwAYWNXznald_-ALjbGByqsexIPudKhwPa3xwE9Sah-Q3y_sY8MmzM8R1jrUIKZu7FqdgVPbKH-3iTm3Hcl9BrkCQxCF0jjKHcnx_toISCcsp0PTJoRF3CqMQwXbgobD8A-jMBztfMNxkvL7O8IwavRCIILyuxd0_m9trwjuXkHmTfP9fMV5DPcBtHT6QM7G85bl98TX6xquUE8o8pXFDgD5jm3RjbvwMwKCZ1yPbnR2-chfTxDsTvzRma4Dse47_U8ABO9_tvriug7wEMaIEtV_ziBbeNtjEnh-essNXP9uMJm7A3qZcQa0kW61GRExnWSfsEANQAu8eqelWgRnaq8zjMsC6yo46TD0OQBtIvhbo_oB-rqI4vGw9BfdsMQiNSrYHLKrex-LFPYMGUAqiTWbZrYoWsc7PSBzuGclVoZSbwJY69DVdZ2Wg0NbVct7yewa_yXH0PqKXqesWVah_JkSVF4oHbDqDGs1HmEcn3DWxt7Oqv54G3dwbr2lBwuMonhJ6MSkQ6LJ7fz-Z7RoqBdwPCgduwKDje6wqtVI0C6wh5y8uRJilS7U4WshAOkXwNYfv6KMOAmftLq92ZfHupt7RGYU83jXGTJ2kIcC3ZS2KcS7ZxRwppFg4hBKKKqXGZsRu6f9Cw_FrCNwMm7qcEn43hwAvSrwXQjRoY3L8SieyIQz93Gxsg7EbXYtS1L72nEhmLjB8N_WUTQpF1hmVmHokuxghp7ECeV2lt0BKBUydRCbmkP9yU1zpODx_pEBs9IbqaY8Tw0eW0XIi0fTPEMScAA8vxeBZwlZqciqHwSMe_kGHLLSlY0W2dCOVp3E46NtGubVvkC0dDuSzio_aoD6PTJBaJKxarLNkZOlLYcFw36ewZqi5ANhm2CzbibVkJSIQiKAv9IPDmLufvI3Kw6MkaIoXdmL5kwN3t5ZVXnqvfrNSrOPtG2AK0bLRa_aPuR1vaHh5CaU-GQAA2oBI_Efmy5n_jDFKzjXqE1Ju7v2Pt7olme5op1ZeQOOida6INEVhZVqZoNfRlcm-VpbboXI58V4fCz7Dk1NYeYu93a2_iq5nwBT5Iw6msdz1NO82d47Kijo9wJfwThMXr9_cddGnLHMSEk_A5lN_kcgXAJBKu65vmFCaeLe9DbvWYDrz6z1pR95UBRO13YTwruJsQkH0huvszuzp8lE39HfJ7BOIQz6Oeihudyg.L800Fzxmt6IR-3CJJZtwUXDki9v95yUcvBG2TORNt_k; cfzs_google-analytics_v4=%7B%22nYPa_pageviewCounter%22%3A%7B%22v%22%3A%224%22%7D%7D',
#         'cfz_google-analytics_v4': '%7B%22nYPa_engagementDuration%22%3A%7B%22v%22%3A%220%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_engagementStart%22%3A%7B%22v%22%3A%221752137725439%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_counter%22%3A%7B%22v%22%3A%224%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_ga4sid%22%3A%7B%22v%22%3A%221801985518%22%2C%22e%22%3A1752139525439%7D%2C%22nYPa_session_counter%22%3A%7B%22v%22%3A%221%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_ga4%22%3A%7B%22v%22%3A%2248188387-d2f4-4c03-99e4-fe9bc5369be4%22%2C%22e%22%3A1783673725439%7D%2C%22nYPa_let%22%3A%7B%22v%22%3A%221752137725439%22%2C%22e%22%3A1783673725439%7D%7D',
#
#     }
#
#     """
#     从指定的API URL获取邮件数据，并将'id'和'address'字段保存到Excel文件中。
#
#     Args:
#         api_url (str): 要获取数据的API端点URL。
#         output_filename (str): 输出的Excel文件名 (例如 'emails.xlsx')。
#     """
#     try:
#         # 步骤 1: 发送HTTP GET请求到API URL
#         print(f"正在从 {api_url} 获取数据...")
#         response = requests.get(api_url,headers=headers, cookies=cookie)
#
#         # 检查请求是否成功 (状态码 200)
#         response.raise_for_status()
#         print("数据获取成功。")
#
#         # 步骤 2: 解析JSON响应
#         data = response.json()
#
#         # 步骤 3: 提取 'emails' 列表
#         emails_list = data.get('emails')
#
#         if not emails_list:
#             print("在响应中未找到 'emails' 列表。")
#             return
#
#         # 步骤 4: 准备要存入Excel的数据
#         # 创建一个列表，其中每个元素都是一个包含 'id' 和 'address' 的字典
#         data_to_save = []
#         for email in emails_list:
#             data_to_save.append({
#                 'ID': email.get('id'),
#                 'Address': email.get('address')
#             })
#
#         # 步骤 5: 使用pandas创建DataFrame
#         df = pd.DataFrame(data_to_save)
#
#         # 步骤 6: 将DataFrame保存到Excel文件
#         # `index=False` 表示在Excel中不包含DataFrame的索引列
#         df.to_excel(output_filename, index=False, engine='openpyxl')
#
#         print(f"数据已成功保存到文件: {output_filename}")
#
#     except requests.exceptions.RequestException as e:
#         print(f"请求URL时发生错误: {e}")
#     except json.JSONDecodeError:
#         print("解析JSON响应失败，请检查URL返回的是否为有效的JSON格式。")
#     except KeyError:
#         print("解析数据时出错，请检查JSON结构是否与预期一致。")
#     except Exception as e:
#         print(f"发生未知错误: {e}")
#
#
# # --- 主程序 ---
# if __name__ == "__main__":
#     # 定义API的URL和输出的Excel文件名
#     API_URL = "https://moemail.app/api/emails"
#     OUTPUT_EXCEL_FILE = "moemail_addresses.xlsx"
#
#     # 调用函数执行操作
#     fetch_and_save_emails_to_excel(API_URL, OUTPUT_EXCEL_FILE)






# ==========================================================================================================
# ==========================================================================================================
# outook邮箱协议登录
# ==========================================================================================================
# ==========================================================================================================

# 步骤A：生成授权链接，让用户同意授权
# import urllib.parse
#
# client_id = '7656482a-bd11-4f9d-bf48-b6936f861e1e' #在Azure的App registrations里添加设置
# redirect_uri = 'http://localhost:8000/callback'  #需要添加
# scope = 'https://graph.microsoft.com/Mail.Read offline_access'
#
# params = {
#     'client_id': client_id,
#     'response_type': 'code',
#     'redirect_uri': redirect_uri,
#     'scope': scope,
#     'response_mode': 'query'
# }
# authorize_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?' + urllib.parse.urlencode(params)
# print(authorize_url)

# 步骤B：用 code 换 token
# import pandas as pd
# import requests
#
# client_id = '7656482a-bd11-4f9d-bf48-b6936f861e1e'  #在Azure的App registrations里添加设置
# client_secret = '****************************************'  #在Azure的App registrations里添加设置
# redirect_uri = 'http://localhost:8000/callback'  #需要添加
# code = 'M.C525_BL2.2.U.c046ecab-880c-3f81-bea2-960f04c740e1'  #需要步骤A登录账密获取， 只能用一次
#
# token_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
# data = {
#     'client_id': client_id,
#     'scope': 'https://graph.microsoft.com/Mail.Read offline_access',
#     'code': code,
#     'redirect_uri': redirect_uri,
#     'grant_type': 'authorization_code',
#     'client_secret': client_secret
# }
# resp = requests.post(token_url, data=data)
# tokens = resp.json()
# print(tokens)
#
#
# access_token = tokens['access_token']
# headers = {'Authorization': f'Bearer {access_token}'}
# resp = requests.get('https://graph.microsoft.com/v1.0/me/messages', headers=headers)
# mails = resp.json().get('value', [])
#
# rows = []
# for mail in mails:
#     rows.append({
#         '主题': mail.get('subject'),
#         '发件人': mail.get('from', {}).get('emailAddress', {}).get('address'),
#         '时间': mail.get('receivedDateTime'),
#         '正文预览': mail.get('bodyPreview'),
#         '正文内容': mail.get('body', {}).get('content'),
#         '是否已读': mail.get('isRead')
#     })
#
# df = pd.DataFrame(rows)
# df.to_excel('我的邮件.xlsx', index=False)
# print('\n邮件内容已保存为 我的邮件.xlsx')















