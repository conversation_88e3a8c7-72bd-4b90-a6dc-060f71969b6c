#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的滑块验证测试脚本
提高成功率的关键优化：
1. 更真实的轨迹生成算法
2. 多种距离识别方法
3. 智能重试机制
4. 人类行为模拟
"""

import time
import random
from improved_verification import get_captcha_data, smart_verification_attempt

def run_improved_test(max_attempts=10):
    """
    运行改进的滑块验证测试
    """
    print("🚀 开始改进的滑块验证测试")
    print("=" * 50)
    
    success_count = 0
    total_attempts = 0
    
    for attempt in range(max_attempts):
        print(f"\n📋 第 {attempt + 1}/{max_attempts} 次测试")
        print("-" * 30)
        
        try:
            # 获取验证码数据
            print("🔄 获取验证码数据...")
            bg_url, slider_url, token = get_captcha_data()
            
            if not all([bg_url, slider_url, token]):
                print("❌ 获取验证码数据失败")
                continue
            
            print(f"✅ 获取成功 - Token: {token[:20]}...")
            
            # 智能验证尝试
            print("🧠 开始智能验证...")
            success, message = smart_verification_attempt(bg_url, slider_url, token)
            
            total_attempts += 1
            
            if success:
                success_count += 1
                print(f"🎉 验证成功！{message}")
            else:
                print(f"💔 验证失败：{message}")
            
            # 添加随机延迟，避免请求过快
            delay = random.uniform(2, 5)
            print(f"⏱️ 等待 {delay:.1f} 秒...")
            time.sleep(delay)
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            break
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            continue
    
    # 统计结果
    print("\n" + "=" * 50)
    print("📊 测试结果统计")
    print("=" * 50)
    
    if total_attempts > 0:
        success_rate = (success_count / total_attempts) * 100
        print(f"总尝试次数: {total_attempts}")
        print(f"成功次数: {success_count}")
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 70:
            print("🏆 优秀！成功率很高")
        elif success_rate >= 50:
            print("👍 良好！成功率中等")
        elif success_rate >= 30:
            print("⚠️ 一般，还有改进空间")
        else:
            print("❌ 成功率较低，需要进一步优化")
    else:
        print("❌ 没有有效的尝试")
    
    return success_count, total_attempts

def analyze_failure_patterns():
    """
    分析失败模式，帮助进一步优化
    """
    print("\n🔍 失败模式分析建议：")
    print("1. 检查 verification_result.png 查看距离识别是否准确")
    print("2. 观察轨迹是否过于规律或不自然")
    print("3. 确认网络连接稳定性")
    print("4. 检查请求频率是否过快")
    print("5. 验证JavaScript环境是否正常")

if __name__ == '__main__':
    print("🎯 滑块验证成功率优化测试")
    print("本脚本包含多种优化策略：")
    print("- 真实轨迹生成算法")
    print("- 多重距离识别")
    print("- 智能重试机制")
    print("- 人类行为模拟")
    print()
    
    try:
        # 运行测试
        success, total = run_improved_test(10)
        
        # 分析建议
        analyze_failure_patterns()
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
