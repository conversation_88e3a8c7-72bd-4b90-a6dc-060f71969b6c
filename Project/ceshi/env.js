window = self = top = global

delete global;  // 记得用 _global = global 备份一下
delete Buffer;
delete __filename;
delete __dirname;

screen = {}

location = {
    "ancestorOrigins": {},
    "href": "https://dun.163.com/trial/sense",
    "origin": "https://dun.163.com",
    "protocol": "https:",
    "host": "dun.163.com",
    "hostname": "dun.163.com",
    "port": "",
    "pathname": "/trial/sense",
    "search": "",
    "hash": ""
}

navigator = {
    appCodeName: "Mozilla",
    appName: "Netscape",
    appVersion: "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

history = {}


div = {
    addEventListener: function (){},
    getAttribute: function (a){
        // console.log("div getAttribute:::",a)
    }
}

body = {}

document = {
    createElement :function (ele){
        // console.log("document createElement:::", ele)
        if(ele==="div"){
            return div
        }
    },
    body : body,
    getElementById: function (ele){
        // console.log('document getElementById:::', ele)
    }
}



ActiveXObject = function () {}
XMLHttpRequest = function () {}
addEventListener = function () {}
attachEvent = function () {}
setTimeout = function (){}
setInterval = function (){}