# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/24 16:35
# <AUTHOR> shaocan<PERSON>
# @File    : main.py
# @explain :
import json
import random
import time

import requests
import execjs
import ddddocr
from PIL import Image
import io

def get_picture():
    headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Referer': 'https://dun.163.com/',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    cb = execjs.compile(open('main.js',encoding='utf-8').read()).call("get_cb")
    fp = execjs.compile(open('main.js',encoding='utf-8').read()).call("get_fp")
    params = {
        'referer': 'https://dun.163.com/trial/jigsaw',
        'zoneId': 'CN31',
        'dt': 'YocoXUaUAmdEVgEVVFPCxA/eZZkRy77V',
        'irToken': 'SwBgJDEkLZVFN1BFVBeX0Bvakd5hzpuP',
        'id': '07e2387ab53a4d6f930b8d9a9be71bdf',
        'fp': fp,
        'https': 'true',
        'type': '2',
        'version': '2.28.5',
        'dpr': '1.25',
        'dev': '1',
        'cb': cb,
        'ipv6': 'false',
        'runEnv': '10',
        'group': '',
        'scene': '',
        'lang': 'zh-CN',
        'sdkVersion': '',
        'loadVersion': '2.5.3',
        'iv': '4',
        'user': '',
        'width': '320',
        'audio': 'false',
        'sizeType': '10',
        'smsVersion': 'v3',
        # 'token': '6a0baa63720d4b1693bc8bb15070dd0a',
        'callback': '__JSONP_neyfzp3_7',
    }

    response = requests.get('https://c.dun.163.com/api/v3/get', params=params, headers=headers)

    response = "{" + response.text.split('({')[1].split('})')[0] + '}'

    response = json.loads(response)

    token0 = response['data']['token']

    # 背景图
    shadeImage_url = response['data']['bg'][0]

    # 方块图
    cutouImage_url = response['data']['front'][0]

    # print(token0+'\n'+shadeImage_url+'\n'+cutouImage_url)

    return shadeImage_url,cutouImage_url,token0


def test_distance(shade_image_url,cutou_image_url):

    shade_image_bytes = requests.get(shade_image_url).content
    cutou_image_bytes = requests.get(cutou_image_url).content

    ocr = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
    # 4. 调用 slide_match 进行识别
    # print("正在使用ddddocr识别缺口距离...")
    # simple_target 参数通常用于目标简单的滑块
    res = ocr.slide_match(cutou_image_bytes, shade_image_bytes, simple_target=True)

    # 检查是否成功识别
    if 'target' in res:
        distance = res['target'][0]
        y_coord = res['target'][1]  # 有时y坐标也有用
        # print(f"\n>>>>>> 识别出的滑块距离 (x坐标): {distance} <<<<<<")

        # print("正在生成验证图片...")
        # 从内存中的字节流加载图片
        background_image = Image.open(io.BytesIO(shade_image_bytes))
        slider_image = Image.open(io.BytesIO(cutou_image_bytes))

        # 将滑块图粘贴到背景图的指定位置
        # 第一个参数是滑块图，第二个是位置元组(x, y)
        # 第三个参数是遮罩，使用滑块图自身作为遮罩可以处理透明度
        background_image.save('debug_bg.png')
        slider_image.save('debug_slider.png')


        background_image.paste(slider_image, (distance, y_coord), slider_image)


        # 保存结果图以便查看
        result_filename = "verification_result.png"
        background_image.save(result_filename)
        # print(f"验证图片已保存为: {result_filename}")
        # print("请打开该图片，检查滑块是否准确地填补了缺口。")
        return str(distance)

    else:
        print("ddddocr未能成功识别目标位置。")


def get_track_raw(x_end):
    """生成原始的，未经加工的滑块轨迹"""
    x = 0
    y = 0
    t = random.randint(70, 100)
    count = 0
    track = []
    while x <= x_end:
        track_item = [x, y, t,1]  # 直接在后面加个1
        track.append(track_item)
        x += random.randint(0, 3)
        t += random.randint(5, 10)
        if count % 6 == 0:
            y -= 1
        count += 1

    return track



def get_track_enc(token, track):
    """获得经过加密处理的滑块轨迹"""
    return execjs.compile(open('main.js', encoding='utf-8').read()).call('get_track_encrypt', token, track)


def get_data(track_encrypt, track, token, x_end):
    """护额的请求参数data"""
    return \
    execjs.compile(open('main.js', encoding='utf-8').read()).call('get_data', track_encrypt, track, token, x_end)[
        'data']




def get_verify(token, data):
    headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Referer': 'https://dun.163.com/',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        # 'Cookie': '_ga=GA.1.2b6292b04e59e.236d9618e512ea8b1f38; hb_MA-93D5-9AD06EA4329A_source=www.google.com; Hm_lvt_4671c5d502135636b837050ec6d716ce=**********; HMACCOUNT=F3A60F48205F0D02; __root_domain_v=.163.com; _qddaz=QD.***************; Hm_lpvt_4671c5d502135636b837050ec6d716ce=**********',
    }
    cb = execjs.compile(open('main.js', encoding='utf-8').read()).call('get_cb')
    params = {
        'referer': 'https://dun.163.com/trial/jigsaw',
        'zoneId': 'CN31',
        'dt': 'YocoXUaUAmdEVgEVVFPCxA/eZZkRy77V',
        'id': '07e2387ab53a4d6f930b8d9a9be71bdf',
        'token': token,
        'data': data,
        'width': '320',
        'type': '2',
        'version': '2.28.5',
        'cb': cb,
        'user': '',
        'extraData': '',
        'bf': '0',
        'runEnv': '10',
        'sdkVersion': '',
        'loadVersion': '2.5.3',
        'iv': '4',
        'callback': '__JSONP_suvtmk9_25',
    }

    response = requests.get('https://c.dun.163.com/api/v3/check', params=params, headers=headers)
    # print('=====================')
    # print(response.text)
    return response.text
    print('=====================')
    print(response.url)


if __name__ == '__main__':
    for i in range(1):
        print(f"=== 执行第{i}次滑块验证 ===")

        # 获取验证码数据
        shade_image_url, cutou_image_url, token = get_picture()
        # print(f"Token: {token}")

        # 识别距离
        distance = test_distance(shade_image_url, cutou_image_url)
        distance = int(distance)
        # print(f"识别距离: {distance}")

        # 生成轨迹
        track = get_track_raw(distance)
        # print(f"轨迹长度: {len(track)}")
        # print(f"轨迹长度: {len(track)}")
        # print(track)

        # 加密轨迹
        track_encrypt = get_track_enc(token, track)
        # print(f"加密轨迹长度: {len(track_encrypt)}")
        # print("加密轨迹长度:", track_encrypt)

        # 生成数据
        # data2 = get_data(track_encrypt, track, token, distance)
        data = get_data(track_encrypt, track, token, distance-10)
        # print(f"生成的data: {data}")


        # 提交验证
        # result2 = get_verify(token, data2)
        result = get_verify(token, data)

        # 检查结果
        # print("rsult2:",result2)
        print("rsult:",result)

        print("=== 执行完成 ===")

