import json
import random
import time
import requests
import execjs
import ddddocr
from PIL import Image
import io
import os
from improved_track import generate_realistic_track, generate_bezier_track, add_human_behavior

def get_script_dir():
    """获取脚本目录"""
    return os.path.dirname(os.path.abspath(__file__))

def improved_distance_detection(shade_image_url, cutou_image_url):
    """
    改进的距离识别，使用多种方法
    """
    try:
        # 下载图片
        shade_image_bytes = requests.get(shade_image_url, timeout=10).content
        cutou_image_bytes = requests.get(cutou_image_url, timeout=10).content
        
        # 保存调试图片
        script_dir = get_script_dir()
        with open(os.path.join(script_dir, 'debug_bg.png'), 'wb') as f:
            f.write(shade_image_bytes)
        with open(os.path.join(script_dir, 'debug_slider.png'), 'wb') as f:
            f.write(cutou_image_bytes)
        
        # 方法1: 使用ddddocr的simple_target=True
        ocr1 = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
        res1 = ocr1.slide_match(cutou_image_bytes, shade_image_bytes, simple_target=True)
        
        # 方法2: 使用ddddocr的simple_target=False
        ocr2 = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
        res2 = ocr2.slide_match(cutou_image_bytes, shade_image_bytes, simple_target=False)
        
        distances = []
        
        if 'target' in res1 and len(res1['target']) >= 2:
            distances.append(res1['target'][0])
            print(f"方法1识别距离: {res1['target'][0]}")
        
        if 'target' in res2 and len(res2['target']) >= 2:
            distances.append(res2['target'][0])
            print(f"方法2识别距离: {res2['target'][0]}")
        
        if distances:
            # 如果多个结果相近，取平均值
            if len(distances) > 1 and abs(distances[0] - distances[1]) <= 3:
                final_distance = sum(distances) // len(distances)
                print(f"多方法平均距离: {final_distance}")
            else:
                final_distance = distances[0]
            
            # 生成验证图片
            background_image = Image.open(io.BytesIO(shade_image_bytes))
            slider_image = Image.open(io.BytesIO(cutou_image_bytes))
            
            verify_image = background_image.copy()
            y_coord = res1.get('target', [0, 0])[1] if 'target' in res1 else 0
            verify_image.paste(slider_image, (final_distance, y_coord), slider_image)
            
            result_filename = os.path.join(script_dir, "verification_result.png")
            verify_image.save(result_filename)
            
            return final_distance, y_coord
        else:
            print("所有方法都无法识别距离")
            return None, None
            
    except Exception as e:
        print(f"距离识别出错: {e}")
        return None, None

def smart_verification_attempt(shade_image_url, cutou_image_url, token):
    """
    智能验证尝试，包含多种策略
    """
    script_dir = get_script_dir()
    
    # 识别距离
    distance, y_coord = improved_distance_detection(shade_image_url, cutou_image_url)
    if distance is None:
        return False, "距离识别失败"
    
    print(f"基础识别距离: {distance}")
    
    # 策略1: 尝试不同的距离偏移
    distance_offsets = [0, -1, 1, -2, 2, -3, 3]
    
    for offset in distance_offsets:
        adjusted_distance = distance + offset
        if adjusted_distance < 0:
            continue
            
        print(f"尝试距离: {adjusted_distance} (偏移: {offset})")
        
        # 策略2: 尝试不同的轨迹生成方法
        track_methods = [
            ("realistic", lambda d: generate_realistic_track(d)),
            ("bezier", lambda d: generate_bezier_track(d)),
            ("realistic_with_behavior", lambda d: add_human_behavior(generate_realistic_track(d)))
        ]
        
        for method_name, track_generator in track_methods:
            try:
                print(f"  使用轨迹方法: {method_name}")
                
                # 生成轨迹
                track = track_generator(adjusted_distance)
                
                # 加密轨迹
                track_encrypt = execjs.compile(
                    open(os.path.join(script_dir, 'main.js'), encoding='utf-8').read()
                ).call('get_track_encrypt', token, track)
                
                # 生成data
                data = execjs.compile(
                    open(os.path.join(script_dir, 'main.js'), encoding='utf-8').read()
                ).call('get_data', track_encrypt, track, token, adjusted_distance)['data']
                
                # 验证
                result = verify_slider(token, data)
                
                if result and result.get('result'):
                    print(f"✅ 验证成功！距离: {adjusted_distance}, 方法: {method_name}")
                    return True, f"成功 - 距离: {adjusted_distance}, 方法: {method_name}"
                else:
                    print(f"❌ 验证失败 - 距离: {adjusted_distance}, 方法: {method_name}")
                    
                # 添加随机延迟，避免请求过快
                time.sleep(random.uniform(0.5, 1.5))
                
            except Exception as e:
                print(f"轨迹方法 {method_name} 出错: {e}")
                continue
    
    return False, "所有尝试都失败了"

def verify_slider(token, data):
    """验证滑块"""
    headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Referer': 'https://dun.163.com/',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    
    script_dir = get_script_dir()
    cb = execjs.compile(
        open(os.path.join(script_dir, 'main.js'), encoding='utf-8').read()
    ).call('get_cb')
    
    params = {
        'referer': 'https://dun.163.com/trial/jigsaw',
        'zoneId': 'CN31',
        'dt': 'YocoXUaUAmdEVgEVVFPCxA/eZZkRy77V',
        'id': '07e2387ab53a4d6f930b8d9a9be71bdf',
        'token': token,
        'data': data,
        'width': '320',
        'type': '2',
        'version': '2.28.5',
        'cb': cb,
        'user': '',
        'extraData': '',
        'bf': '0',
        'runEnv': '10',
        'sdkVersion': '',
        'loadVersion': '2.5.3',
        'iv': '4',
        'callback': f'__JSONP_callback_{random.randint(1000, 9999)}',
    }

    try:
        response = requests.get('https://c.dun.163.com/api/v3/check', params=params, headers=headers, timeout=10)
        print(f"验证响应: {response.text}")
        
        # 解析响应
        response_text = "{" + response.text.split('({')[1].split('})')[0] + '}'
        result = json.loads(response_text)
        return result
        
    except Exception as e:
        print(f"验证请求出错: {e}")
        return None

def get_captcha_data():
    """获取验证码数据"""
    headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Referer': 'https://dun.163.com/',
        'Sec-Fetch-Dest': 'script',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    script_dir = get_script_dir()
    cb = execjs.compile(
        open(os.path.join(script_dir, 'main.js'), encoding='utf-8').read()
    ).call("get_cb")

    params = {
        'referer': 'https://dun.163.com/trial/jigsaw',
        'zoneId': 'CN31',
        'dt': 'YocoXUaUAmdEVgEVVFPCxA/eZZkRy77V',
        'id': '07e2387ab53a4d6f930b8d9a9be71bdf',
        'https': 'true',
        'type': '2',
        'version': '2.28.5',
        'dpr': '1.25',
        'dev': '1',
        'cb': cb,
        'ipv6': 'false',
        'runEnv': '10',
        'group': '',
        'scene': '',
        'lang': 'zh-CN',
        'sdkVersion': '',
        'loadVersion': '2.5.3',
        'iv': '4',
        'user': '',
        'width': '320',
        'audio': 'false',
        'sizeType': '10',
        'smsVersion': 'v3',
        'callback': f'__JSONP_callback_{random.randint(1000, 9999)}',
    }

    try:
        response = requests.get('https://c.dun.163.com/api/v3/get', params=params, headers=headers, timeout=10)
        response_text = "{" + response.text.split('({')[1].split('})')[0] + '}'
        response_data = json.loads(response_text)
        
        token = response_data['data']['token']
        bg_url = response_data['data']['bg'][0]
        slider_url = response_data['data']['front'][0]
        
        return bg_url, slider_url, token
        
    except Exception as e:
        print(f"获取验证码数据出错: {e}")
        return None, None, None
