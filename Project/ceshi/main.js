require('./env')
require('./loader')


x = window.loader(71)

cb = x()
function get_cb(){
    return cb
}

J_f8 = window.loader(10)['xorEncode']
function get_track_encrypt(token, arr){
    var track_encrypt = []
    for(i of arr){
       track_encrypt.push( J_f8(token, i + ''))
    }
    return track_encrypt
}


get_data_ = window.loader(36)['_options']['methods']['onMouseUp']
function get_data(track_enc, track, token, x_end) {
    return get_data_(track_enc, track, token, x_end)
}


// var track1 = [[5,-2,93,1],[6,-2,102,1],[9,-2,109,1],[12,-3,118,1],[21,-4,126,1],[29,-5,134,1],[39,-7,142,1],[47,-9,150,1],[55,-10,158,1],[60,-11,166,1],[66,-12,175,1],[71,-13,182,1],[75,-13,190,1],[77,-14,197,1],[80,-15,206,1],[82,-15,213,1],[84,-15,222,1],[86,-15,238,1],[88,-15,253,1],[90,-15,261,1],[92,-15,270,1],[94,-15,277,1],[97,-15,286,1],[100,-15,293,1],[103,-15,302,1],[106,-15,310,1],[110,-15,318,1],[113,-15,325,1],[115,-15,334,1],[119,-15,342,1],[120,-15,350,1],[123,-15,360,1],[124,-15,375,1],[127,-15,390,1],[128,-15,429,1],[131,-15,470,1],[132,-15,493,1]]
// var track_enc1 = ["rearvwJGrrFx","r/arvwJPr4XRxp33","\\earvwJPr4SRxp33","vArvnpvlrEz8ggp3","viqvnp7lrEXgggp3","vimvnp+lrEi0ggp3","vpmvnpglrEj/ggp3","rcivnpLlrEq1ggp3","rAzvnp9nP4zUiX4P","rijvnp9PP4zg1Z4P","riXvnp9pP4zX1r4P","rpqvnp9OP4z8xZ4P","rpzvnp9OP4zkxX4P","rpivnp96P4zk1I4P","\\cjvnp9\\P4X11Z4P","\\crvnp9\\P4XxxI4P","\\cNvnp9\\P4X/xZ4P","\\cXvnp9\\P4XiiX4P","\\cFvnp9\\P4XUxI4P","\\Ajvnp9\\P4Xgxr4P","\\Arvnp9\\P4XXxX4P","\\ANvnp9\\P4XX1I4P","\\Aivnp9\\P4X81Z4P","vAj7n/WPvIF/ivul/p33","vAjkn/WPvIFixg9l/p33","vAjCn/WPvIFixvvl/p33","vAq7n/WPvIFixvEl/p33","vAqkn/WPvIFix4/l/p33","vAqNn/WPvIFixEgl/p33","vAqin/WPvIFi1g9l/p33","vAr7n/WPvIFi1vvl/p33","vArkn/WPvIFi14vl/p33","vAr+n/WPvIFi1E/l/p33","vArln/WPvIFiivvl/p33","vAr/n/WPvIF0x4el/p33","vApzn/WPvIF01Evl/p33","vAp8n/WPvIF0ivul/p33"]
// var token1 = 'f79d6bbc38eb42d2b23028e7a412b0e8';
// var x_end1 = 121.5
//
//
// function aaaaaa(){
//     return get_data(track_enc1, track1, token1, x_end1);  // (加密轨迹，真轨迹，token，x_edn)
// }
// console.log(get_data(track_enc1, track1, token1, x_end1));  // (加密轨迹，真轨迹，token，x_edn)
// console.log(track_enc1);
// console.log("============")


function get_fp(){
    return window.gdxidpyhxde
}

