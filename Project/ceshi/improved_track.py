import random
import math

def generate_realistic_track(target_distance):
    """
    生成更真实的滑块轨迹
    模拟人类滑动行为：加速 -> 匀速 -> 减速 -> 微调
    """
    track = []
    x = 0
    y = 0
    t = random.randint(50, 80)  # 起始时间
    
    # 阶段1: 加速阶段 (0 -> 30%距离)
    acceleration_end = target_distance * 0.3
    while x < acceleration_end:
        # 加速度逐渐增加
        speed = min(3.5, x / acceleration_end * 3.5)
        x += speed + random.uniform(-0.3, 0.3)
        
        # Y轴微小抖动
        y += random.uniform(-0.8, 0.8)
        
        # 时间间隔随速度变化
        t += random.randint(8, 15)
        
        if x <= target_distance:
            track.append([int(x), int(y), t, 1])
    
    # 阶段2: 匀速阶段 (30% -> 80%距离)
    uniform_end = target_distance * 0.8
    while x < uniform_end:
        # 保持相对稳定的速度
        x += random.uniform(2.5, 3.8)
        y += random.uniform(-0.5, 0.5)
        t += random.randint(10, 18)
        
        if x <= target_distance:
            track.append([int(x), int(y), t, 1])
    
    # 阶段3: 减速阶段 (80% -> 95%距离)
    deceleration_end = target_distance * 0.95
    while x < deceleration_end:
        # 逐渐减速
        remaining = deceleration_end - x
        speed = max(0.8, remaining / (deceleration_end - uniform_end) * 2.5)
        x += speed + random.uniform(-0.2, 0.2)
        y += random.uniform(-0.3, 0.3)
        t += random.randint(15, 25)
        
        if x <= target_distance:
            track.append([int(x), int(y), t, 1])
    
    # 阶段4: 精确定位阶段 (95% -> 100%距离)
    while x < target_distance:
        x += random.uniform(0.3, 1.2)
        y += random.uniform(-0.2, 0.2)
        t += random.randint(20, 35)
        
        if x <= target_distance:
            track.append([int(x), int(y), t, 1])
    
    # 确保最后一个点是目标位置
    if track and track[-1][0] != target_distance:
        track.append([target_distance, int(y), t + random.randint(15, 30), 1])
    
    # 阶段5: 微调阶段 (模拟人类的精确调整)
    for _ in range(random.randint(2, 4)):
        # 在目标位置附近做小幅调整
        adjust_x = target_distance + random.randint(-2, 2)
        y += random.uniform(-0.5, 0.5)
        t += random.randint(25, 45)
        track.append([adjust_x, int(y), t, 1])
    
    return track

def generate_bezier_track(target_distance):
    """
    使用贝塞尔曲线生成更自然的轨迹
    """
    def bezier_curve(t, p0, p1, p2, p3):
        """三次贝塞尔曲线"""
        return (1-t)**3 * p0 + 3*(1-t)**2*t * p1 + 3*(1-t)*t**2 * p2 + t**3 * p3
    
    track = []
    
    # 控制点
    p0_x, p0_y = 0, 0
    p1_x, p1_y = target_distance * 0.3, random.randint(-5, 5)
    p2_x, p2_y = target_distance * 0.7, random.randint(-3, 3)
    p3_x, p3_y = target_distance, random.randint(-2, 2)
    
    # 生成轨迹点
    steps = random.randint(25, 40)
    start_time = random.randint(50, 80)
    
    for i in range(steps):
        t = i / (steps - 1)
        
        x = bezier_curve(t, p0_x, p1_x, p2_x, p3_x)
        y = bezier_curve(t, p0_y, p1_y, p2_y, p3_y)
        
        # 时间间隔模拟加速减速
        if t < 0.3:  # 加速
            time_interval = random.randint(8, 15)
        elif t < 0.8:  # 匀速
            time_interval = random.randint(12, 20)
        else:  # 减速
            time_interval = random.randint(18, 30)
        
        start_time += time_interval
        
        # 添加噪声
        x += random.uniform(-0.5, 0.5)
        y += random.uniform(-0.5, 0.5)
        
        track.append([int(x), int(y), start_time, 1])
    
    return track

def add_human_behavior(track):
    """
    为轨迹添加人类行为特征
    """
    # 随机暂停
    if random.random() < 0.3:  # 30%概率暂停
        pause_index = random.randint(len(track)//3, len(track)*2//3)
        pause_duration = random.randint(50, 200)
        for i in range(pause_index, len(track)):
            track[i][2] += pause_duration
    
    # 随机回退
    if random.random() < 0.2:  # 20%概率回退
        backtrack_index = random.randint(len(track)//2, len(track)*3//4)
        if backtrack_index < len(track):
            original_x = track[backtrack_index][0]
            # 回退几个像素
            track[backtrack_index][0] = max(0, original_x - random.randint(2, 5))
            # 然后再前进
            if backtrack_index + 1 < len(track):
                track[backtrack_index + 1][0] = original_x
    
    return track
