# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/7/25 16:11
# <AUTHOR> shaocanfan
# @File    : 111.py
# @explain : 


import json
import random

import requests
import execjs
import ddddocr
from PIL import Image
import io

headers = {
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'DNT': '1',
    'Pragma': 'no-cache',
    'Referer': 'https://dun.163.com/',
    'Sec-Fetch-Dest': 'script',
    'Sec-Fetch-Mode': 'no-cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': '_ga=GA.1.26ff623c3f2dc.f227d5589a4629b2a948; Hm_lvt_4671c5d502135636b837050ec6d716ce=**********; HMACCOUNT=E03906CDC92BBFAA; hb_MA-93D5-9AD06EA4329A_source=www.google.com; __root_domain_v=.163.com; _qddaz=QD.***************; Hm_lpvt_4671c5d502135636b837050ec6d716ce=**********',
}

data = execjs.compile(open('main.js',encoding='utf-8').read()).call("aaaaaa")
token = 'f79d6bbc38eb42d2b23028e7a412b0e8'
cb = execjs.compile(open('main.js',encoding='utf-8').read()).call("get_cb")

params = {
    'referer': 'https://dun.163.com/trial/jigsaw',
    'zoneId': 'CN31',
    'dt': '6BIUlXpfFz9BEkUQBFfSwQ7ub8o5lUSQ',
    'id': '07e2387ab53a4d6f930b8d9a9be71bdf',
    'token': token,
    'data': data,
    'width': '320',
    'type': '2',
    'version': '2.28.5',
    'cb': cb,
    'user': '',
    'extraData': '',
    'bf': '0',
    'runEnv': '10',
    'sdkVersion': '',
    'loadVersion': '2.5.3',
    'iv': '4',
    'callback': '__JSONP_8n2sk26_50',
}

# response = requests.get('https://c.dun.163.com/api/v3/check', params=params, headers=headers)
# print(response.text)

a = 7
print(str(a-2.5))
