# !/usr/bin/env python
# -*- coding: utf-8 -*-

# @Time    : 2025/5/15 1:19
# <AUTHOR> s<PERSON><PERSON><PERSON>
# @File    : submit_claude.py
# @explain : 

# ... (imports and other helper functions) ...
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.ui import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException  # 确保导入
import time


# ... [fill_student_builder_form_dropdowns 和 Fill_In_The_Information_Content 函数定义如前] ...
def fill_student_builder_form_dropdowns(driver):
    """
    在 Anthropic Student Builders 表单上选择下拉菜单的选项。
    :param driver: Selenium WebDriver 实例
    :return: True 如果成功, False 如果失败
    """
    try:
        select_element_locator_by_name = (By.NAME, "edu_api_credits_use")
        select_element = WebDriverWait(driver, 10).until(
            EC.visibility_of_element_located(select_element_locator_by_name)
        )
        select_object = Select(select_element)
        select_object.select_by_visible_text("Personal use (learning, school assignments, etc)")
        print("已通过可见文本选择 'Personal use (learning, school assignments, etc)'")

        how_use_credit_by_name = (By.NAME, "edu_credit_claude_usage_type")
        how_use_credit_select_element = WebDriverWait(driver, 10).until(
            EC.visibility_of_element_located(how_use_credit_by_name)
        )
        how_use_credit_select_object = Select(how_use_credit_select_element)
        how_use_credit_select_object.select_by_visible_text("Building with the API")
        print("已通过可见文本选择 'Building with the API' for Claude usage.")
        return True
    except Exception as e:
        print(f"错误: 在填写表单下拉菜单时发生错误: {e}")
        return False


def Fill_In_The_Information_Content(driver, name_attribute, text_to_input):
    """
    定位一个 name 属性为 name_attribute 的输入框，并输入 text_to_input。
    :param driver: Selenium WebDriver 实例
    :param name_attribute: 输入框的 'name' 属性值
    :param text_to_input: 要输入的文本
    :return: True 如果成功, False 如果失败
    """
    try:
        input_locator = (By.NAME, name_attribute)
        input_field = WebDriverWait(driver, 10).until(
            EC.visibility_of_element_located(input_locator)
        )
        input_field.clear()
        input_field.send_keys(text_to_input)
        print(f"成功在 name='{name_attribute}' 中输入: '{text_to_input[:50]}{'...' if len(text_to_input) > 50 else ''}'")
        return True
    except Exception as e:
        print(f"错误: 操作 name='{name_attribute}' 输入框失败: {e}")
        return False


# 新的错误检测函数
def check_for_required_fields_error(driver):
    """
    检查页面是否显示 "Please complete all required fields." 的错误提示。
    :param driver: Selenium WebDriver 实例
    :return: True 如果错误提示存在且可见, False 其他情况。
    """
    try:
        error_message_locator = (
        By.XPATH, "//div[@class='hs_error_rollup']//label[contains(text(), 'Please complete all required fields.')]")
        error_element = WebDriverWait(driver, 5).until(  # 等待最多5秒
            EC.visibility_of_element_located(error_message_locator)
        )
        if error_element.is_displayed():  # 再次确认元素是否真的可见 (WebDriverWait 已处理大部分情况)
            print(f"检测到提交错误提示: {error_element.text.strip()}")
            return True
    except TimeoutException:
        print("未在5秒内检测到 'Please complete all required fields.' 错误提示。")
        return False
    except Exception as e:
        print(f"检查提交错误时发生意外: {e}")
        return False
    return False


def register_account(Firstname, Lastname, Student_Email, University, Major, Message, Additional_Comments_Contact,uuid):
    driver = None
    overall_registration_status = False  # 用于跟踪整个注册流程的最终状态

    try:
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
        driver.get("https://www.anthropic.com/contact-sales/for-student-builders")
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.NAME, "firstname"))
        )

        field_filling_actions = [
            lambda: Fill_In_The_Information_Content(driver, "firstname", Firstname),
            lambda: Fill_In_The_Information_Content(driver, "lastname", Lastname),
            lambda: Fill_In_The_Information_Content(driver, "email", Student_Email),
            lambda: Fill_In_The_Information_Content(driver, "0-2/name", University),
            lambda: Fill_In_The_Information_Content(driver, "country", "United States of America"),
            lambda: Fill_In_The_Information_Content(driver, "degree_program", Major),
            lambda: Fill_In_The_Information_Content(driver, "message", Message),
            lambda: Fill_In_The_Information_Content(driver, "additional_comments_contact", Additional_Comments_Contact),
            lambda: Fill_In_The_Information_Content(driver,"0-2/organization_uuid",uuid),
            lambda: fill_student_builder_form_dropdowns(driver)
        ]

        form_content_filled_successfully = True
        for i, action in enumerate(field_filling_actions):
            print(f"正在执行字段填写操作 {i + 1}/{len(field_filling_actions)}...")
            if not action():
                form_content_filled_successfully = False
                print(f"字段填写操作 {i + 1} 失败。中止后续填写。")
                break

        if form_content_filled_successfully:
            print("\n所有字段和下拉菜单已成功填写。")
            print("正在尝试提交表单...")
            submission_successful = False  # 用于跟踪提交步骤是否成功

            try:
                submit_button_locator = (By.XPATH, "//input[@type='submit' and @value='Submit']")
                submit_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable(submit_button_locator)
                )
                time.sleep(5)
                submit_button.click()
                print("表单“提交”按钮已点击。现在检查提交结果...")

                # 检查是否有“请填写所有必填项”的错误
                if check_for_required_fields_error(driver):
                    print("提交失败：表单提示部分必填项未完成。")
                    # submission_successful 保持 False
                else:
                    print("未检测到'必填项'错误。尝试验证实际提交成功状态...")
                    try:
                        # ******** 优化点：使用新的HTML信息更新成功标志定位器 ********
                        # 定位包含成功消息的 div 元素。
                        # 我们查找 class 包含 'submitted-message' 的 div，并且其内部的 div 包含文本 "Thank you for applying!"
                        success_indicator_locator = (
                            By.XPATH,
                            "//div[contains(@class, 'submitted-message')]//div[contains(text(), 'Thank you for applying!')]"
                        )

                        success_message_element = WebDriverWait(driver, 15).until(  # 略微增加等待时间以确保消息加载
                            EC.visibility_of_element_located(success_indicator_locator)
                        )
                        print(f"检测到成功提交信息: '{success_message_element.text.strip()}'")
                        submission_successful = True
                    except TimeoutException:
                        print("未在预期时间内检测到明确的成功标志（'Thank you for applying!'消息）。提交状态不确定或失败。")
                    except Exception as e_success_check:
                        print(f"验证提交成功状态时发生错误: {e_success_check}")

            except Exception as e_submit:
                print(f"错误：点击提交按钮或后续检查时发生异常: {e_submit}")
                # submission_successful 保持 False

            if submission_successful:
                overall_registration_status = True  # 只有当字段填写和提交都成功时，才认为整体成功

        else:  # form_content_filled_successfully is False
            print("\n由于部分字段填写失败，未尝试提交表单。")
            # overall_registration_status 保持 False

    except Exception as e_main:
        print(f"注册过程中发生严重意外错误: {e_main}")
        # overall_registration_status 保持 False
    finally:
        if driver:
            # 可以在这里多停留几秒，以便在关闭前观察最终页面状态，特别是调试时
            final_pause_duration = 3 if overall_registration_status else 10  # 成功则短暂停，失败则长暂停
            print(f"流程结束，暂停 {final_pause_duration} 秒后关闭浏览器...")
            time.sleep(final_pause_duration)
            driver.quit()
            print("浏览器已关闭。")
    return overall_registration_status


if __name__ == "__main__":
    Firstname = "Brewer"  # 示例数据，确保它们符合表单字段的预期格式和长度
    Lastname = "Caroline"
    Student_Email = "<EMAIL>"  # 使用一个看起来更合法的测试邮箱
    University = "University of Washington"
    Major = "Undergrad student,Applied Mathematics Major"
    Message = "I'm interested in building a small application that uses the API to generate personalized study guides based on my course notes. Being able to interact with my material in a new way via an API-driven tool sounds very promising."
    Additional_Comments_Contact = "I came across Claude while reading an article on Hacker News discussing different AI models and their unique features."

    uuid = "6e55bf43-7ee9-47ba-905d-a2d3aad981e1"
    print("开始注册流程...")
    success = register_account(Firstname, Lastname, Student_Email, University, Major, Message,
                               Additional_Comments_Contact,uuid)

    if success:
        print("\n注册流程成功完成并且提交成功！")
    else:
        print("\n注册流程未能成功完成或提交失败。请检查上面的日志获取详细信息。")


